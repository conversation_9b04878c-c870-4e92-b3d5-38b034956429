package com.ygxj.scale

import android.util.Range
import org.junit.Assert.assertEquals
import org.junit.Test

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {

    @Test
    fun addition_isCorrect() {
        assertEquals(4, 2 + 2)
    }

    @Test
    fun getResult() {
        // A因素题目索引 A:**********.*************.151.176.
        val aIndex = arrayOf(3, 26, 27, 51, 52, 76, 101, 126, 151, 176)
        // B因素题目索引 B:***********.**************.***************.178.180.
        val bIndex = arrayOf(28, 53, 54, 77, 78, 102, 103, 127, 128, 152, 153, 177, 178, 180)
        // C:*********.************.***************.179.
        val cIndex = arrayOf(4, 5, 29, 30, 55, 79, 80, 104, 105, 129, 130, 154, 179)
        // E:*********.************.***************.
        val eIndex = arrayOf(6, 7, 31, 32, 56, 57, 81, 106, 131, 155, 156, 181)
        // F:**********.**************.***************.183
        val fIndex = arrayOf(8, 33, 58, 82, 83, 107, 108, 132, 133, 157, 158, 182, 183)
        // G:**********.***************.184.185
        val gIndex = arrayOf(9, 34, 59, 84, 109, 134, 159, 160, 184, 185)
        // H:10.35.36.60.61.85.86.110.111.135.136.161.186.
        val hIndex = arrayOf(10, 35, 36, 60, 61, 85, 86, 110, 111, 135, 136, 161, 186)
        // I:11.12.37.62.87.112.137.138.162.163.
        val iIndex = arrayOf(11, 12, 37, 62, 87, 112, 137, 138, 162, 163)
        // L:13.38.63.64.88.89.113.114.139.164
        val lIndex = arrayOf(13, 38, 63, 64, 88, 89, 113, 114, 139, 164)
        // M:14.15.39.40.65.90.91.115.116.140.141.165.166
        val mIndex = arrayOf(14, 15, 39, 40, 65, 90, 91, 115, 116, 140, 141, 165, 166)
        // N:16.17.41.42.66.67.92.117.142.167
        val nIndex = arrayOf(16, 17, 41, 42, 66, 67, 92, 117, 142, 167)
        // O:18.19.43.44.68.69.93.94.118.119.143.144.168.
        val oIndex = arrayOf(18, 19, 43, 44, 68, 69, 93, 94, 118, 119, 143, 144, 168)
        // Q1:20.21.45.46.70.95.120.145.169.170
        val q1Index = arrayOf(20, 21, 45, 46, 70, 95, 120, 145, 169, 170)
        // Q2:22.47.71.72.96.97.121.122.146.171
        val q2Index = arrayOf(22, 47, 71, 72, 96, 97, 121, 122, 146, 171)
        // Q3:23.24.48.73.98.123.147.148.172.173
        val q3Index = arrayOf(23, 24, 48, 73, 98, 123, 147, 148, 172, 173)
        // Q4:25.49.50.74.75.99.100.124.125.149.150.174.175
        val q4Index = arrayOf(25, 49, 50, 74, 75, 99, 100, 124, 125, 149, 150, 174, 175)

        val list = ArrayList<Int>()
        list.addAll(aIndex.toList())
        list.addAll(bIndex.toList())
        list.addAll(cIndex.toList())
        list.addAll(eIndex.toList())
        list.addAll(fIndex.toList())
        list.addAll(gIndex.toList())
        list.addAll(hIndex.toList())
        list.addAll(iIndex.toList())
        list.addAll(lIndex.toList())
        list.addAll(mIndex.toList())
        list.addAll(nIndex.toList())
        list.addAll(oIndex.toList())
        list.addAll(q1Index.toList())
        list.addAll(q2Index.toList())
        list.addAll(q3Index.toList())
        list.addAll(q4Index.toList())
        println(list.sorted().toString())
        assertEquals(184, list.size)
    }
}