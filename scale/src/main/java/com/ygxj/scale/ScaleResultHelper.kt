package com.ygxj.scale

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.scaleResult.ResultUtil16PF
import com.ygxj.scale.scaleResult.ResultUtilAISV2
import com.ygxj.scale.scaleResult.ResultUtilBAIV2
import com.ygxj.scale.scaleResult.ResultUtilBHS
import com.ygxj.scale.scaleResult.ResultUtilBPRS
import com.ygxj.scale.scaleResult.ResultUtilCECSV2
import com.ygxj.scale.scaleResult.ResultUtilEES
import com.ygxj.scale.scaleResult.ResultUtilEPQAV2
import com.ygxj.scale.scaleResult.ResultUtilIAS
import com.ygxj.scale.scaleResult.ResultUtilIRIDS
import com.ygxj.scale.scaleResult.ResultUtilJTJY
import com.ygxj.scale.scaleResult.ResultUtilLSI
import com.ygxj.scale.scaleResult.ResultUtilLSR
import com.ygxj.scale.scaleResult.ResultUtilMBTIV2
import com.ygxj.scale.scaleResult.ResultUtilMMPI354
import com.ygxj.scale.scaleResult.ResultUtilMMPI45
import com.ygxj.scale.scaleResult.ResultUtilQuickEvaluate
import com.ygxj.scale.scaleResult.ResultUtilSAS
import com.ygxj.scale.scaleResult.ResultUtilSCL90
import com.ygxj.scale.scaleResult.ResultUtilSDS
import com.ygxj.scale.scaleResult.ResultUtilSPM
import com.ygxj.scale.scaleResult.ResultUtilSRHMS
import com.ygxj.scale.scaleResult.ResultUtilSocial213
import com.ygxj.scale.scaleResult.ResultUtilSocial42
import com.ygxj.scale.scaleResult.ResultUtilUCLA55
import com.ygxj.scale.scaleResult.ResultUtilVOSQ

/**
 * 量表结果助手
 */
object ScaleResultHelper {

    /**
     * 获取量表测试结果
     * - [scaleId] 量表id
     * - [selectedOptions] 用户选择的选项,key为题目索引,value为选项索引,都是从0开始计
     * - [costTime] 测试所花费的时间
     * - [userAge] 测试用户年龄
     * - [userSex] 测试用户性别
     */
    fun getResult(scaleId: Int, selectedOptions: Map<Int, String>, costTime: String, userAge: Int, userSex: String): ScaleResultEntity? {
        val options = selectedOptions.values.toList()
        return when (scaleId) {
            1 -> ResultUtil16PF().getResult(scaleId, options, costTime) // 卡塔尔 16 种人格因素问卷(16PF)
            3 -> ResultUtilVOSQ().getResult(scaleId, options, costTime) // 职业倾向系列问卷（VOSQ）
            6 -> ResultUtilSCL90().getResult(scaleId, options, costTime) //症状自评量表(SCL-90)
            17 -> ResultUtilEPQAV2().getResult(scaleId, options, costTime, userAge, userSex) // 艾森克人格问卷成人式(EPQA)
            22 -> ResultUtilBAIV2().getResult(scaleId, options, costTime) //贝克焦虑量表(BAI)
            36 -> ResultUtilSAS().getResult(scaleId, options, costTime) //焦虑自评量表(SAS)
            37 -> ResultUtilEES().getResult(scaleId, options, costTime) //艾森克情绪稳定性测验(EES)
            672 -> ResultUtilSDS().getResult(scaleId, options, costTime) // 宗(Zung)氏抑郁自评量表（SDS)
            41 -> ResultUtilIAS().getResult(scaleId, options, costTime) // 交往焦虑量表(IAS)
            42 -> ResultUtilSocial42().getResult(scaleId, options, costTime) // 社会适应能力诊断量表
            45 -> ResultUtilMMPI45().getResult(scaleId, options, costTime, userSex, userAge) // 明尼苏达多相个性测验(MMPI)
            50 -> ResultUtilSPM().getResult(scaleId, options, costTime, userAge.toFloat()) //瑞文标准推理测验(SPM)
            55 -> ResultUtilUCLA55().getResult(scaleId, options, costTime) // UCLA 孤独量表
            100 -> ResultUtilLSR().getResult(scaleId, options, costTime) //生活满意度评定量表(LSR)
            143 -> ResultUtilCECSV2().getResult(scaleId, options, costTime) //情绪控制量表(CECS)
            206 -> ResultUtilLSI().getResult(scaleId, options, costTime)  // 学习风格测验(LSI)
            210 -> ResultUtilJTJY().getResult(scaleId, options, costTime) //家庭教育方式综合测评
            213 -> ResultUtilSocial213().getResult(scaleId, options, costTime) // 心理社会适应性测试
            321 -> ResultUtilAISV2().getResult(scaleId, options, costTime) //阿森斯失眠评定量表（AIS）
            337 -> ResultUtilBPRS().getResult(scaleId, options, costTime) // 简明精神病评定量表18题版
            354 -> ResultUtilMMPI354().getResult(scaleId, options, costTime, userSex, userAge) //明尼苏达多相个性测验(MMPI)
            513 -> ResultUtilBHS().getResult(scaleId, options, costTime) // 贝克绝望量表(BHS)
            523 -> ResultUtilSRHMS().getResult(scaleId, options, costTime) // 自测健康评定量表(SRHMS)
            529 -> ResultUtilIRIDS().getResult(scaleId, options, costTime) // 人际关系综合诊断量表(IRIDS)
            615 -> ResultUtilMBTIV2().getResult(scaleId, options, costTime) // MBTI 人格量表
            in arrayOf(690, 691, 692, 693, 694) -> ResultUtilQuickEvaluate().getResult(scaleId, options, costTime) // 快速评估量表
            else -> null
        }
    }
}