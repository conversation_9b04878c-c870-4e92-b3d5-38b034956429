package com.ygxj.scale.database

import android.app.Application
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.ygxj.scale.ScaleHelper
import com.ygxj.scale.database.table.QuestionData
import com.ygxj.scale.database.table.ScaleData

@Database(
    entities = [
        ScaleData::class,
        QuestionData::class
    ],
    version = 2
)
abstract class ScaleDataBase : RoomDatabase() {

    abstract fun scaleDao(): ScaleDao

    companion object {

        private var instance: ScaleDataBase? = null

        fun getInstance(context: Application): ScaleDataBase {
            instance ?: synchronized(ScaleDataBase::class.java) {
                instance ?: let {
                    instance = Room.databaseBuilder(context, ScaleDataBase::class.java, ScaleHelper.DB_NAME)
                        .allowMainThreadQueries()
                        .createFromAsset("scale.db")
                        .build()
                }
            }
            return instance!!
        }
    }
}