package com.ygxj.scale.database

import androidx.room.Dao
import androidx.room.Query
import androidx.room.Update
import com.ygxj.scale.database.table.QuestionData
import com.ygxj.scale.database.table.ScaleData

@Dao
interface ScaleDao {

    @Query("select * from ScaleData")
    fun getAllScale(): List<ScaleData>

    @Query("select * from ScaleData where id = :scaleId")
    fun getScaleDataById(scaleId: Int): ScaleData

    // 按照id升序
    @Query("select * from QuestionData where scaleId = :scaleId order by questionIndex asc")
    fun getScaleQuestionDataes(scaleId: Int): List<QuestionData>

    @Update(entity = ScaleData::class)
    fun updateScaleData(scaleData: ScaleData)

}
