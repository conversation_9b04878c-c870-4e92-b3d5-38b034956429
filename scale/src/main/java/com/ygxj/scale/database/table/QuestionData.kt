package com.ygxj.scale.database.table

import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey

/**量表题目和选项*/
@Entity
data class QuestionData(
    @PrimaryKey val id: Int,
    /**所属量表id*/
    val scaleId: Int,
    /**问题*/
    val question: String,
    /**问题序号*/
    val questionIndex: Int,
    /**选项1-11*/
    val option1: String?,
    val option2: String?,
    val option3: String?,
    val option4: String?,
    val option5: String?,
    val option6: String?,
    val option7: String?,
    val option8: String?,
    val option9: String?,
    val option10: String?,
    val option11: String?,
) {

    /**选中状态,供recyclerView中标记状态使用,如用不到可以忽略,不会存储在数据库中*/
    @Ignore
    var selected: Boolean = false
}
