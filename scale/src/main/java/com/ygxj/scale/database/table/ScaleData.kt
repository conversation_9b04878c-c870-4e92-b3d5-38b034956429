package com.ygxj.scale.database.table

import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey

/**量表*/
@Entity
data class ScaleData(

    /** 量表id */
    @PrimaryKey val id: Int,

    /** 量表名 */
    var name: String,

    /** 量表简介 */
    var introduce: String,

    /**量表帮助*/
    var help: String,

    var CSXS: Int?,
    var ZT: Int?,
    var FL: Int?,
    var PX: Int?,
    var FP: Int?,
    var SetMark: Int?,
    var IsDisplay: Int?,
    var BackGround: String?,
    var IsShowResult: Int?,
) {

    /**选中状态,供recyclerView中标记状态使用,如用不到可以忽略,不会存储在数据库中*/
    @Ignore
    var selected: Boolean = false
}
