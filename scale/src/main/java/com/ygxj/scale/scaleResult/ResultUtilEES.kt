package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils

//艾森克情绪稳定性测验(EES)
class ResultUtilEES {

    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        // 因素 计分  3得0.5分 1:是  2:否
        val zYlist = mutableListOf(1, 29, 57, 60, 71, 106, 148, 155, 197, 204) //自卑感  是得1分
        val zNlist = mutableListOf(
            8,
            15,
            22,
            36,
            43,
            50,
            78,
            85,
            92,
            99,
            113,
            120,
            127,
            134,
            141,
            162,
            169,
            176,
            183,
            190
        ) //否得1分
        val yYlist = mutableListOf(16, 23, 30, 37, 51, 79, 121, 138, 142, 177, 205) //抑郁性  是得1分
        val yNlist = mutableListOf(
            2,
            9,
            44,
            58,
            65,
            72,
            86,
            93,
            100,
            107,
            114,
            135,
            149,
            156,
            163,
            170,
            184,
            191,
            198
        ) //否得1分
        val jYlist = mutableListOf(
            3,
            24,
            31,
            38,
            45,
            52,
            59,
            76,
            80,
            101,
            108,
            115,
            129,
            136,
            143,
            150,
            164,
            171,
            178,
            185,
            192,
            119,
            206
        ) //焦虑性 是得1分
        val jNlist = mutableListOf(10, 17, 66, 87, 94, 122, 157) // 否得1分
        val qYlist = mutableListOf(
            4,
            11,
            18,
            25,
            32,
            39,
            46,
            53,
            60,
            67,
            74,
            81,
            88,
            95,
            102,
            109,
            130,
            177,
            144,
            151,
            158,
            165,
            172,
            179,
            186,
            193,
            200,
            207
        ) //强迫状态 是得1分
        val qNlist = mutableListOf(116, 123) //选否得1分
        val zzYlist = mutableListOf(12, 19, 33, 75, 89, 96, 131, 138, 180) //自主性 选 是得1分
        val zzNlist = mutableListOf(
            5,
            26,
            40,
            47,
            54,
            61,
            68,
            82,
            103,
            110,
            117,
            124,
            145,
            152,
            159,
            166,
            173,
            187,
            194,
            201,
            208
        ) //选否得1分
        val hYlist = mutableListOf(
            13,
            20,
            27,
            34,
            41,
            48,
            55,
            62,
            69,
            76,
            83,
            90,
            97,
            111,
            118,
            125,
            132,
            139,
            146,
            153,
            160,
            174,
            181,
            188,
            195,
            202,
            209
        ) //怀疑 选 是得1分
        val hNlist = mutableListOf(6, 104, 167) //选 否得1分
        val fYlist = mutableListOf(
            7,
            14,
            21,
            28,
            35,
            42,
            49,
            56,
            63,
            77,
            84,
            91,
            98,
            105,
            112,
            119,
            126,
            133,
            140,
            154,
            161,
            168,
            175,
            182,
            189,
            196,
            203
        ) //负罪感 选是得1分
        val fNlist = mutableListOf(70, 149, 210) //选否得1分
        //各因素总分
        var zCount = 0f
        var yCount = 0f
        var jCount = 0f
        var qCount = 0f
        var zzCount = 0f
        var hCount = 0f
        var fCount = 0f
        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            val value = answers[index].toInt() + 1
            if (index in zYlist) zCount += if (value == 1) 1f else if (value == 3) 0.5f else 0f
            if (index in zNlist) zCount += if (value == 2) 1f else if (value == 3) 0.5f else 0f

            if (index in yYlist) yCount += if (value == 1) 1f else if (value == 3) 0.5f else 0f
            if (index in yNlist) yCount += if (value == 2) 1f else if (value == 3) 0.5f else 0f

            if (index in jYlist) jCount += if (value == 1) 1f else if (value == 3) 0.5f else 0f
            if (index in jNlist) jCount += if (value == 2) 1f else if (value == 3) 0.5f else 0f

            if (index in qYlist) qCount += if (value == 1) 1f else if (value == 3) 0.5f else 0f
            if (index in qNlist) qCount += if (value == 2) 1f else if (value == 3) 0.5f else 0f

            if (index in zzYlist) zzCount += if (value == 1) 1f else if (value == 3) 0.5f else 0f
            if (index in zzNlist) zzCount += if (value == 2) 1f else if (value == 3) 0.5f else 0f

            if (index in hYlist) hCount += if (value == 1) 1f else if (value == 3) 0.5f else 0f
            if (index in hNlist) hCount += if (value == 2) 1f else if (value == 3) 0.5f else 0f

            if (index in fYlist) fCount += if (value == 1) 1f else if (value == 3) 0.5f else 0f
            if (index in fNlist) fCount += if (value == 2) 1f else if (value == 3) 0.5f else 0f
        }
        // 意见列表
        val yj = StringBuffer()
            .append(
                when (zCount) {
                    in 0f..21f -> "【自卑感】${zCount}分，被测者自我评价低，自认自己不被人喜爱。"
                    in 22f..30f -> "【自卑感】${zCount}分，被测者对自己及自己的能力充满自信，认为自己是有价值的、有用的人， 并相信自己是受人欢迎的。这种人非常自爱、不自高自大。 "
                    else -> ""
                }
            )
            .append(
                when (yCount) {
                    in 0f..22f -> "【抑郁性】${yCount}分，被测者悲观厌世，易灰心，心情抑郁，对自己的生活感到失望，与环境格格不入， 感到自己在这个世界上多余的。"
                    in 23f..30f -> "【抑郁性】${yCount}分，被测者欢快乐观，情绪状态良好，对自己感到满意，对生活感到满足，与世无争。 "
                    else -> ""
                }
            )
            .append(
                when (jCount) {
                    in 0f..16f -> "【焦虑】${jCount}分，被测者容易为一些区区小事而烦恼焦虑，对一些可能发生的不幸事件存在着毫无必要的担忧，杞人忧天。"
                    in 0f..15f -> "【焦虑】${jCount}分，被测者平静、安详，并且对不合理的恐惧、焦虑有抵抗能力。"
                    else -> ""
                }
            )
            .append(
                when (qCount) {
                    in 11f..Float.MAX_VALUE -> "【强迫状态】${qCount}分，被测者谨小慎微，认真仔细，追求细节的完美，规章严明，沉着稳重，容易因脏污不净、零乱无序而烦恼不安。 "
                    in 1f..9f -> "【强迫状态】${qCount}分，被测者不拘礼仪，随遇而安，不讲究规则、常规、形式、程序。"
                    else -> ""
                }
            )
            .append(
                when (zzCount) {
                    in 0f..20f -> "【自主性】${zzCount}分，被测者常缺乏自信心，自认为是命运的牺牲品，易受到周围其他人或事件所摆布，趋附权威。"
                    in 21f..29f -> "【自主性】${zzCount}分，被测者自主性强，尽情享受自由自在的乐趣，很少依赖别人，凡事自己做主，把自己视为命运的主人，以现实主义的态度去解决自己的问题。"
                    else -> ""
                }
            )
            .append(
                when (hCount) {
                    in 6f..Float.MAX_VALUE -> "【疑心】${hCount}分，被测者常常抱怨躯体各个部分的不适感，过分关心自己的健康状况，经常要求医生、家人及朋友对自己予以同情。\""
                    in 1f..5f -> "【疑心】${hCount}分，被测者很少生病，也不为自己的健康状况担心。"
                    else -> ""
                }
            )
            .append(
                when (fCount) {
                    in 8f..23f -> "【负罪感】${fCount}分，被测者自责、自卑，常为良心的折磨所烦恼，不考虑自己的行为是否真正应受到道德的谴责。"
                    in 1f..7f -> "【负罪感】${fCount}分，被测者很少有惩罚自己或追悔过去行为的倾向。"
                    else -> ""
                }
            )

        val ysm = mutableListOf("自卑感", "抑郁性", "焦虑", "强迫状态", "自主性", "疑心", "负罪感").joinToString("|")
        val bzf =
            mutableListOf(zCount, yCount, jCount, qCount, zzCount, hCount, fCount).joinToString("|")
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = ysm,
            BZF = bzf,
            JF = "",
            BZC = "",
            YJ = yj.toString(),
            XX = answers.joinToString("|")
        )
    }
}