package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity

class ResultUtil16PF {
    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        var xx = ""
        //因素
        val aArr = mutableListOf(3, 26, 27, 51, 52, 76, 101, 126, 151, 176)
        val bArr = mutableListOf(28, 53, 54, 77, 78, 102, 103, 127, 128, 152, 153, 177, 178)
        val cArr = mutableListOf(4, 5, 29, 30, 55, 79, 80, 104, 105, 129, 130, 154, 179)
        val eArr = mutableListOf(6, 7, 31, 32, 56, 57, 81, 106, 131, 155, 156, 180, 181)
        val fArr = mutableListOf(8, 33, 58, 82, 83, 107, 108, 132, 133, 157, 158, 182, 183)
        val gArr = mutableListOf(9, 34, 59, 84, 109, 134, 159, 160, 184, 185)
        val hArr = mutableListOf(10, 35, 36, 60, 61, 85, 86, 110, 111, 135, 136, 161, 186)
        val iArr = mutableListOf(11, 12, 37, 62, 87, 112, 137, 138, 162, 163)
        val lArr = mutableListOf(13, 38, 63, 64, 88, 89, 113, 114, 139, 164)
        val mArr = mutableListOf(14, 15, 39, 40, 65, 90, 91, 115, 116, 140, 141, 165, 166)
        val nArr = mutableListOf(16, 17, 41, 42, 66, 67, 92, 117, 142, 167)
        val oArr = mutableListOf(18, 19, 43, 44, 68, 69, 93, 94, 118, 119, 143, 144, 168)
        val q1Arr = mutableListOf(20, 21, 45, 46, 70, 95, 120, 145, 169, 170)
        val q2Arr = mutableListOf(22, 47, 71, 72, 96, 97, 121, 122, 146, 171)
        val q3Arr = mutableListOf(23, 24, 48, 73, 98, 123, 147, 148, 172, 173)
        val q4Arr = mutableListOf(25, 49, 50, 74, 75, 99, 100, 124, 125, 149, 150, 174, 175)

        var Ascore = 0
        var Bscore = 0
        var Cscore = 0
        var Escore = 0
        var Fscore = 0
        var Gscore = 0
        var Hscore = 0
        var Iscore = 0
        var Lscore = 0
        var Mscore = 0
        var Nscore = 0
        var Oscore = 0
        var Q1score = 0
        var Q2score = 0
        var Q3score = 0
        var Q4score = 0

        for (index in answers.indices) {
            Ascore += a(aArr, index, answers[index].toInt() + 1)
            Bscore += a(bArr, index, answers[index].toInt() + 1)
            Cscore += a(cArr, index, answers[index].toInt() + 1)
            Escore += a(eArr, index, answers[index].toInt() + 1)
            Fscore += a(fArr, index, answers[index].toInt() + 1)
            Gscore += a(gArr, index, answers[index].toInt() + 1)
            Hscore += a(hArr, index, answers[index].toInt() + 1)
            Iscore += a(iArr, index, answers[index].toInt() + 1)
            Lscore += a(lArr, index, answers[index].toInt() + 1)
            Mscore += a(mArr, index, answers[index].toInt() + 1)
            Nscore += a(nArr, index, answers[index].toInt() + 1)
            Oscore += a(oArr, index, answers[index].toInt() + 1)
            Q1score += a(q1Arr, index, answers[index].toInt() + 1)
            Q2score += a(q2Arr, index, answers[index].toInt() + 1)
            Q3score += a(q3Arr, index, answers[index].toInt() + 1)
            Q4score += a(q4Arr, index, answers[index].toInt() + 1)
            xx += "${answers[index] + 1}|"
        }

        // 标准分
        //A标准分
        val biaozhunA = when (Ascore) {
            in 0..1 -> 1
            in 2..3 -> 2
            in 4..5 -> 3
            6 -> 4
            in 7..8 -> 5
            in 9..11 -> 6
            in 12..13 -> 7
            14 -> 8
            in 15..16 -> 9
            in 17..20 -> 10
            else -> 0
        }
        //B标准分
        val biaozhunB = when (Bscore) {
            in 0..3 -> 1
            4 -> 2
            5 -> 3
            6 -> 4
            7 -> 5
            8 -> 6
            9 -> 7
            10 -> 8
            11 -> 9
            in 12..13 -> 10
            else -> 0
        }
        //C标准分
        val biaozhunC = when (Cscore) {
            in 0..5 -> 1
            in 6..7 -> 2
            in 8..9 -> 3
            in 10..11 -> 4
            in 12..13 -> 5
            in 14..16 -> 6
            in 17..18 -> 7
            in 19..20 -> 8
            in 21..22 -> 9
            in 23..26 -> 10
            else -> 0
        }
        //E标准分
        val biaozhunE = when (Escore) {
            in 0..2 -> 1
            in 3..4 -> 2
            5 -> 3
            in 6..7 -> 4
            in 8..9 -> 5
            in 10..12 -> 6
            in 13..14 -> 7
            in 15..16 -> 8
            in 17..18 -> 9
            in 19..26 -> 10
            else -> 0
        }
        //F标准分
        val biaozhunF = when (Fscore) {
            in 0..3 -> 1
            4 -> 2
            in 5..6 -> 3
            7 -> 4
            in 8..9 -> 5
            in 10..12 -> 6
            in 13..14 -> 7
            in 15..16 -> 8
            in 17..18 -> 9
            in 19..26 -> 10
            else -> 0
        }
        //G标准分
        val biaozhunG = when (Gscore) {
            in 0..5 -> 1
            in 6..7 -> 2
            in 8..9 -> 3
            10 -> 4
            in 11..12 -> 5
            in 13..14 -> 6
            in 15..16 -> 7
            17 -> 8
            18 -> 9
            in 19..20 -> 10
            else -> 0
        }
        //H标准分
        val biaozhunH = when (Hscore) {
            in 0..1 -> 1
            2 -> 2
            3 -> 3
            in 4..6 -> 4
            in 7..8 -> 5
            in 9..11 -> 6
            in 12..14 -> 7
            in 15..16 -> 8
            in 17..19 -> 9
            in 20..26 -> 10
            else -> 0
        }
        //I标准分
        val biaozhunI = when (Iscore) {
            in 0..5 -> 1
            6 -> 2
            in 7..8 -> 3
            9 -> 4
            in 10..11 -> 5
            in 12..13 -> 6
            14 -> 7
            in 15..16 -> 8
            17 -> 9
            in 18..19 -> 10
            else -> 0
        }
        //L标准分
        val biaozhunL = when (Lscore) {
            in 0..3 -> 1
            in 4..5 -> 2
            6 -> 3
            in 7..8 -> 4
            in 9..10 -> 5
            in 11..12 -> 6
            13 -> 7
            in 14..15 -> 8
            16 -> 9
            in 17..20 -> 10
            else -> 0
        }
        //M标准分
        val biaozhunM = when (Mscore) {
            in 0..5 -> 1
            in 6..7 -> 2
            in 8..9 -> 3
            in 10..11 -> 4
            in 12..13 -> 5
            in 14..15 -> 6
            in 16..17 -> 7
            in 18..19 -> 8
            20 -> 9
            in 21..26 -> 10
            else -> 0
        }
        //N标准分
        val biaozhunN = when (Nscore) {
            in 0..2 -> 1
            3 -> 2
            4 -> 3
            in 5..6 -> 4
            in 7..8 -> 5
            in 9..10 -> 6
            11 -> 7
            in 12..13 -> 8
            14 -> 9
            in 15..20 -> 10
            else -> 0
        }
        //O标准分
        val biaozhunO = when (Oscore) {
            in 0..2 -> 1
            in 3..4 -> 2
            in 5..6 -> 3
            in 7..8 -> 4
            in 9..10 -> 5
            in 11..12 -> 6
            in 13..14 -> 7
            in 15..16 -> 8
            in 17..18 -> 9
            in 19..26 -> 10
            else -> 0
        }
        //Q1标准分
        val biaozhunQ1 = when (Q1score) {
            in 0..4 -> 1
            5 -> 2
            in 6..7 -> 3
            8 -> 4
            in 9..10 -> 5
            in 11..12 -> 6
            13 -> 7
            14 -> 8
            15 -> 9
            in 16..20 -> 10
            else -> 0
        }
        //Q2标准分
        val biaozhunQ2 = when (Q2score) {
            in 0..5 -> 1
            in 6..7 -> 2
            8 -> 3
            in 9..10 -> 4
            in 11..12 -> 5
            in 13..14 -> 6
            15 -> 7
            in 16..17 -> 8
            18 -> 9
            in 19..20 -> 10
            else -> 0
        }
        //Q3标准分
        val biaozhunQ3 = when (Q3score) {
            in 0..4 -> 1
            in 5..6 -> 2
            in 7..8 -> 3
            in 9..10 -> 4
            in 11..12 -> 5
            in 13..14 -> 6
            15 -> 7
            in 16..17 -> 8
            18 -> 9
            in 19..20 -> 10
            else -> 0
        }
        //Q4标准分
        val biaozhunQ4 = when (Q4score) {
            in 0..2 -> 1
            in 3..4 -> 2
            in 5..6 -> 3
            in 7..8 -> 4
            in 9..11 -> 5
            in 12..14 -> 6
            in 15..16 -> 7
            in 17..19 -> 8
            in 20..21 -> 9
            in 22..26 -> 10
            else -> 0
        }

        //次元人格因素
        val x1 =
            ((38 + 2 * biaozhunL + 3 * biaozhunO + 4 * biaozhunQ4) - (2 * biaozhunC + 2 * biaozhunH + 2 * biaozhunQ2)) / 10
        val x2 =
            ((2 * biaozhunA + 3 * biaozhunE + 4 * biaozhunF + 5 * biaozhunH) - (2 * biaozhunQ2 + 11)) / 10
        val x3 =
            ((77 + 2 * biaozhunC + 2 * biaozhunE + 2 * biaozhunF + 2 * biaozhunN) - (4 * biaozhunA + 6 * biaozhunI + 2 * biaozhunM)) / 10
        val x4 =
            ((4 * biaozhunE + 3 * biaozhunM + 4 * biaozhunQ1 + 4 * biaozhunQ2) - (3 * biaozhunA + 2 * biaozhunG)) / 10
        val y1 = biaozhunC + biaozhunF + (11 - biaozhunO) + (11 - biaozhunQ4)
        val y2 =
            2 * biaozhunQ3 + 2 * biaozhunG + 2 * biaozhunC + biaozhunE + biaozhunN + biaozhunQ2 + biaozhunQ1
        val y3 =
            (11 - biaozhunA) * 2 + biaozhunB * 2 + biaozhunE + (11 - biaozhunF) * 2 + biaozhunH + 2 * biaozhunI + biaozhunM + (11 - biaozhunN) + biaozhunQ1 + 2 * biaozhunQ2
        val y4 = biaozhunB + biaozhunG + biaozhunQ3 + (11 - biaozhunF)
        val x1y =
            ((38 + 2 * Lscore + 3 * Oscore + 4 * Q4score) - (2 * Cscore + 2 * Hscore + 2 * Q2score)) / 10
        val x2y = ((2 * Ascore + 3 * Escore + 4 * Fscore + 5 * Hscore) - (2 * Q2score + 11)) / 10
        val x3y =
            ((77 + 2 * Cscore + 2 * Escore + 2 * Fscore + 2 * Nscore) - (4 * Ascore + 6 * Iscore + 2 * Mscore)) / 10
        val x4y =
            ((4 * Escore + 3 * Mscore + 4 * Q1score + 4 * Q2score) - (3 * Ascore + 2 * Gscore)) / 10
        val y1y = Cscore + Fscore + (11 - Oscore) + (11 - Q4score)
        val y2y = 2 * Q3score + 2 * Gscore + 2 * Cscore + Escore + Nscore + Q2score + Q1score
        val y3y =
            (11 - Ascore) * 2 + Bscore * 2 + Escore + (11 - Fscore) * 2 + Hscore + 2 * Iscore + Mscore + (11 - Nscore) + Q1score + 2 * Q2score
        val y4y = Bscore + Gscore + Q3score + (11 - Fscore)

        //意见
        val yijian = StringBuffer()
        yijian.append(
            if (biaozhunA <= 3) {
                "【乐群性】倾向于生硬,冷酷,多疑,离群。他对物品比对人更喜爱,常独自做事情,对不同意见不愿和解,按刻板生硬的方式和个人的准则做事。|"
            } else {
                "【乐群性】好脾气,情绪开朗,易合作,心软慈爱,能适应环境。喜欢与人打交道的工作,能迅速与人组成较活跃的集团。对人宽宏大量,不怕批评。|"
            }
        ).append(
            if (biaozhunB <= 3) {
                "【聪慧性】倾向于学习和领悟缓慢,迟钝,对事物多采用具体的和刻板的理解。其迟钝状态可能由智能因素或精神病态引起。|"
            } else {
                "【聪慧性】倾向于能迅速领悟各种观念,学习敏捷而聪颖,其聪明才智与文化水平相一致。可排除病理性心理机能衰退|"
            }
        ).append(
            if (biaozhunC <= 3) {
                "【稳定性】趋向于在困难条件下表现出对挫折的耐受性差,情绪波动明显,疲倦,烦躁,有时显得幼稚。几乎所有的神经症均呈低分,亦见于一些精神病患者。|"
            } else {
                "【稳定性】趋向于情绪成熟,稳定,对生活采取现实态度,做事不慌忙。有时对不能解决的情绪问题采取退出方法,有禁欲倾向,个别人亦有潜在性精神疾患的可能。|"
            }
        ).append(
            if (biaozhunE <= 3) {
                "【恃强性】倾向于谦让,温顺,安于现状,缺乏自信,易依赖别人,对指责易产生焦虑。坦白而通融。|"
            } else {
                "【恃强性】倾向于严厉,独断专横,不友善,对别人责备过分。统治别人,不怕强权。主动且自视很高。|"
            }
        ).append(
            if (biaozhunF <= 3) {
                "【兴奋性】倾向于克制,沉默,内省,有时会郁郁寡欢,抑郁,过分审慎。爱独处,甚至自我压抑。|"
            } else {
                "【兴奋性】倾向于愉悦,健谈,坦白,生气勃勃,富于表情。经常被选为领导,但可能有冲动,易变。|"
            }
        ).append(
            if (biaozhunG <= 3) {
                "【有恒性】倾向于意志易动摇,缺乏奉公守法的精神。由于不受集体约束,故有可能发生反社会行为。但有时这却使他更健康,因为拒绝受规律约束使他在应激中发生较少躯体不适感。|"
            } else {
                "【有恒性】倾向于性格严峻,有强烈责任心,有计划,善宽容。喜欢勤奋聪明的人,有道德,细心周到善始善终。这些特点是出自于内心而迫切需求的,并非在表面上体现追求。|"
            }
        ).append(
            if (biaozhunH <= 3) {
                "【敢为性】倾向于胆小退缩,小心谨慎,喜欢安静,常有自卑感。说话缓慢而口吃,缺乏自信心。特别对异性反应胆小,甚至不感兴趣。|"
            } else {
                "【敢为性】倾向于更好社交,喜欢探求新事物,主动并富于情绪反应。在与异性接触中会给人“脸皮较厚”的印象。常粗心大意,忽视细节。|"
            }
        ).append(
            if (biaozhunI <= 3) {
                "【敏感性】倾于固执任性,注重实际,有时显得冷酷。办事有逻辑性,安于自足,自持其力。|"
            } else {
                "【敏感性】倾于敏感善良,易受感动。但有时过分不讲实际,易感情用事,缺乏耐心与恒心。|"
            }
        ).append(
            if (biaozhunL <= 3) {
                "【怀疑性】倾于依赖随和,安全感强,无嫉妒心,能迎合别人的意见,易相处。但容易受人欺骗而上当。|"
            } else {
                "【怀疑性】倾向于刚愎自用,固执己见,对人不信任,与人交往常斤斤计较,不考虑别人的利益。|"
            }
        ).append(
            if (biaozhunM <= 3) {
                "【幻想性】倾向于注重现实,办事力求稳妥,合乎成规。对生活细节较重视,能考虑自己的行为活动是否合乎社会规范。|"
            } else {
                "【幻想性】倾向于幻想,狂放任性富于想象,往往自得其乐,自我陶醉。通常忽视生活细节和现状,常从自己的动机兴趣出发。|"
            }
        ).append(
            if (biaozhunN <= 3) {
                "【世故性】倾于纯朴,为人坦白,易相信人,但有时处于被动状态。社交上笨拙,甚至对社交回避。|"
            } else {
                "【世故性】倾于世故,为人机灵,办事老练,近乎于狡猾。聪明且善于社交,对别人有洞察力,善精打细算。|"
            }
        ).append(
            if (biaozhunO <= 3) {
                "【忧虑性】倾向于自信,生活自足快乐,安静而镇定,悠然自得,少烦恼。信心充足,乐观安详。|"
            } else {
                "【忧虑性】倾向于自我内疚,常烦恼自扰,甚至沮丧悲观,感到不如人。有时缺乏生活勇气,时有犯罪感。|"
            }
        )


        val ysm =
            "A 乐群性|B 聪慧性|C 稳定性|E 恃强性|F 兴奋性|G 有恒性|H 敢为性|I 敏感性|L 怀疑性|M 幻想性|N 世故性|O 忧虑性|Q1 实验性|Q2 独立性|Q3 自律性|Q4 紧张性|X1 适应与焦虑型|X2 内向与外向型|X3 感情用事与安详机警型|X4 怯懦与果断型|Y1 心理健康因素|Y2 专业有成就者的人格因素|Y3 创造力强者的人格因素|Y4 在新环境中有成长能力的人格因素"
        val bzf = mutableListOf(Ascore, Bscore, Cscore, Escore, Fscore, Gscore, Hscore, Iscore, Lscore, Mscore, Nscore, Oscore, Q1score, Q2score, Q3score, Q4score, x1y, x2y, x3y, x4y, y1y, y2y, y3y, y4y).joinToString("|")
        val jf = mutableListOf(biaozhunA, biaozhunB, biaozhunC, biaozhunE, biaozhunF, biaozhunG, biaozhunH, biaozhunI, biaozhunL, biaozhunM, biaozhunN, biaozhunO, biaozhunQ1, biaozhunQ2, biaozhunQ3, biaozhunQ4, x1, x2, x3, x4, y1, y2, y3, y4).joinToString("|")
        val bzc = "-无"
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = ysm,
            BZF = bzf,
            JF = jf,
            BZC = bzc,
            YJ = yijian.toString(),
            XX = xx
        )
    }


    //AA里面题目选A的记2分 B的1分 C的0分
    //CC里面题目选C的记2分 B的1分 A的0分
    //private List<int> AA = new List<int> { 3, 4, 7, 10, 13, 17, 18, 20, 21, 25, 30, 33, 36,37, 38,
    //    39, 40, 42, 43, 46, 47, 48,49, 50, 52, 55, 56, 58, 59, 65, 69, 70, 71, 72, 73, 74, 88, 91,
    //    98, 99, 101, 104, 105, 109,110, 111, 112, 113, 114, 115, 116, 117, 118, 119,  126, 124,129,  130, 131,
    //    132, 133, 134, 136, 138, 140,142, 143, 145, 146, 147, 148, 149, 150, 155,156,  160, 163, 164, 167, 168, 169,
    //    171, 173, 174, 176, 179, 180, 181, 182, 183, 184, 185, 186 };
    //选A的2分,【反向计分】
    private val AA =
        mutableListOf(3, 4, 7, 10, 13, 17, 18, 20, 21, 25, 30, 33, 36, 38, 39, 40, 42, 43, 46, 47, 49, 50, 52, 55, 56, 58, 59, 65, 69, 70, 71, 72, 73, 74, 88, 91, 98, 99, 101, 104, 105, 106, 109, 111, 112, 114, 115, 116, 117, 118, 119, 126, 130, 131, 132, 133, 134, 136, 138, 142, 145, 146, 147, 148, 149, 150, 155, 160, 163, 164, 167, 168, 169, 171, 174, 176, 179, 180, 181, 182, 183, 184, 185, 186)

    //private List<int> CC = new List<int> {  5,6, 8,9, 11, 12, 14, 15, 16, 19, 22,23, 24, 26, 27, 29, 31, 32, 34,
    //  35, 41, 44, 45,51, 57, 60, 61, 62, 63,64,66, 67, 68,  75, 76, 79,80, 81, 82,83, 84, 85, 86, 87,
    //    89, 90, 92, 93,  94, 95, 96,  97,100, 106, 107, 108, 120, 121, 122, 123, 125, 135, 137, 139, 141,
    //    144, 151, 154, 157, 158, 159, 161, 162, 165, 166,170, 172, 175 };
    //选c的2分,【正向计分】
    private val CC = mutableListOf(6, 9, 11, 12, 14, 15, 16, 23, 24, 26, 27, 29, 31, 32, 35, 41, 44, 45, 57, 60, 61, 62, 63, 66, 68, 75, 76, 79, 81, 82, 84, 85, 86, 87, 89, 90, 92, 93, 95, 96, 100, 107, 108, 120, 121, 122, 123, 125, 135, 137, 139, 141, 144, 151, 154, 157, 158, 161, 162, 165, 166, 172, 175)
    private val LF = mutableListOf(1, 2, 187)//这三题为零分

    //选以下答案的记1分否则为0
    private val aa = mutableListOf(178, 177)//选a为1分,其他0分
    private val bb = mutableListOf(28, 53, 54, 78, 103, 128, 152)//选b为1分,其他0分
    private val cc = mutableListOf(77, 102, 127, 153)//选c为1分,其他0分  ****************

    /**
     * @param listYS 因素
     * @param key    字典K值
     * @param values 字典V值
     * @return
     */
    private fun a(listYS: List<Int?>, key: Int, values: Int): Int {
        var num = 0
        if (listYS.contains(key)) {
            when {
                AA.contains(key) -> {
                    when (values) {
                        1 -> num = 2
                        2 -> num = 1
                        3 -> num = 0
                    }
                }
                CC.contains(key) -> {
                    when (values) {
                        1 -> num = 0
                        2 -> num = 1
                        3 -> num = 2
                    }
                }
                LF.contains(key) -> {
                    when (values) {
                        1, 3, 2 -> num = 0
                    }
                }
                aa.contains(key) -> {
                    when (values) {
                        1 -> num = 1
                        2, 3 -> num = 0
                    }
                }
                bb.contains(key) -> {
                    when (values) {
                        1, 3 -> num = 0
                        2 -> num = 1
                    }
                }
                cc.contains(key) -> {
                    when (values) {
                        1, 2 -> num = 0
                        3 -> num = 1
                    }
                }
            }
        }
        return num
    }
}