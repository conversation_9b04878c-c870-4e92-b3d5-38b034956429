package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils

class ResultUtilBHS {
    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        var count = 0//总分
        val list = mutableListOf(2, 9, 11, 12, 14, 16, 17, 18, 20)//反向计分
        val list1 = mutableListOf(1, 5, 6, 10, 13, 15, 19)//对未来的感觉
        val list2 = mutableListOf(2, 3, 9, 11, 12, 16, 17, 20)//动机的丧失
        val list3 = mutableListOf(4, 7, 8, 14, 18)//对未来的期望
        var l1 = 0//对未来的感觉
        var l2 = 0//动机的丧失
        var l3 = 0//对未来的期望
        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            val data = answers[index].toInt() + 1
            val value = if (list.contains(index)) {
                if (data == 1) 1 else 0
            } else {
                if (data == 1) 0 else 1
            }
            count += value
            if (list1.contains(index)) {
                l1 += value
            }
            if (list2.contains(index)) {
                l2 += value
            }
            if (list3.contains(index)) {
                l3 += value
            }
        }
        val yj =
            "【总分】" + count + "分。|得分范围为0-20分，得分越高表明受测者绝望程度越高。超过5分，建议受测者立即找精神科医生。" +
                    "|【对未来的感觉】" + l1 + "分。得分范围为0—7分，分值越高，对未来越绝望。" +
                    "|【动机的丧失】" + l2 + "分。得分范围为0—8分，分值越高，动机越丧失。" +
                    "|【对未来的期望】" + l3 + "分。得分范围为0—5分，分值越高，对未来的期望值越低。"

        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "总分|对未来的感觉|动机的丧失|对未来的期望",
            BZF = "${count}|${l1}|${l2}|${l3}",
            JF = "",
            YJ = yj,
            BZC = "",
            XX = answers.joinToString("|")
        )
    }
}