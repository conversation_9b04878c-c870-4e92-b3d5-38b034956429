package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import kotlin.math.roundToInt

/** SCL-90症状自评量表 */
class ResultUtilSCL90 {

    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        var xx = ""
        val somatizationList = mutableListOf(1, 4, 12, 27, 40, 42, 48, 49, 52, 53, 56, 58)//躯体化
        val obsessionList = mutableListOf(3, 9, 10, 28, 38, 45, 46, 51, 55, 65)//强迫症
        val personalRelationsList = mutableListOf(6, 21, 34, 36, 37, 41, 61, 69, 73)// 人际关系敏感
        val depressionList = mutableListOf(5, 14, 15, 20, 22, 26, 29, 30, 31, 32, 54, 71, 79)//抑郁
        val anxietyList = mutableListOf(2, 17, 23, 33, 39, 57, 72, 78, 80, 86)// 焦虑
        val hostilityList = mutableListOf(11, 24, 63, 67, 74, 81)// 敌对
        val phobicanxietyList = mutableListOf(13, 25, 47, 50, 70, 75, 82)// 恐怖
        val parnoidIdeationList = mutableListOf(8, 18, 43, 68, 76, 83) // 偏执
        val psychotismList = mutableListOf(7, 16, 35, 62, 77, 84, 85, 87, 88, 90)// 精神病性
        val otherList = mutableListOf(19, 44, 59, 60, 64, 66, 89)// 其它

        var feminine = 0// 阴性
        var somatization = 0//躯体化
        var obsession = 0//强迫症
        var personalRelations = 0// 人际关系敏感
        var depression = 0//抑郁
        var anxiety = 0// 焦虑
        var hostility = 0// 敌对
        var phobicanxiety = 0// 恐怖
        var parnoidIdeation = 0// 偏执
        var psychotism = 0 // 精神病性
        var other = 0//各因素分数总和 // 其它

        var count = 0//总分（所有题目得分之和）

        var OnePoint_Count = 0//阴性
        var GreaterOne_Count = 0//阳性

        for (index in answers.indices) {
            val value = answers[index].toInt() + 1
            count += value
            if (value == 1) OnePoint_Count++ else GreaterOne_Count++
            feminine += if (value == 1) 1 else 0
            //躯体化
            if (somatizationList.contains(index)) somatization += value
            //强迫症
            if (obsessionList.contains(index)) obsession += value
            // 人际关系敏感
            if (personalRelationsList.contains(index)) personalRelations += value
            // 抑郁
            if (depressionList.contains(index)) depression += value
            //焦虑
            if (anxietyList.contains(index)) anxiety += value
            //敌对
            if (hostilityList.contains(index)) hostility += value
            // 恐怖
            if (phobicanxietyList.contains(index)) phobicanxiety += value
            // 偏执
            if (parnoidIdeationList.contains(index)) parnoidIdeation += value
            // 精神病性
            if (psychotismList.contains(index)) psychotism += value
            // 其它
            if (otherList.contains(index)) other += value
            if (hostilityList.contains(index)) hostility += value
            xx += "$value|"

        }
        val avg = count / 90// 均分(所有题目得分/题目数)
        val qthavg = somatization / somatizationList.count()
        val qpzavg = obsession / obsessionList.count()
        val rjgxavg = personalRelations / personalRelationsList.count()
        val yyavg = depression / depressionList.count()
        val jlavg = anxiety / anxietyList.count()
        val ddavg = hostility / hostilityList.count()
        val kjavg = phobicanxiety / phobicanxietyList.count()
        val pzavg = parnoidIdeation / parnoidIdeationList.count()
        val jsbavg = psychotism / psychotismList.count()
        val otheravg = other / otherList.count()
        val js = StringBuffer("")

        if (count < 160) {
            js.append("该测验结果为心理健康。")
        } else {
            js.append("该测验结果总分 $count＞160。")
            if (GreaterOne_Count >= 43) js.append("阳性项目数超过43项。")
            if (qthavg > 2f) js.append("躯体化、")
            if (yyavg > 2f) js.append("抑郁、")
            if ((other / otherList.count()) > 2) js.append("其他")
            if (qthavg > 2f || yyavg > 2f || other / otherList.toTypedArray().count() > 2f
            ) js.append("项目的因子分超过2分。")
        }
        //设置预警等级
        js.append(
            when (qthavg) {
                in 4..Int.MAX_VALUE -> "|【躯体化】被测者有重度的躯体化症状。躯体化是指一个人本来有情绪问题或者心理障碍，但却没有以心理症状表现出来，而转换为各种躯体症状表现出来。主要反映身体不适感，包括心血管、胃肠道、呼吸和其他系统的不适，和头痛、背痛、肌肉酸痛，以及焦虑等躯体不适表现，但经过医院的专业检查，却没有发现明显的器质性病理变化。你可能对这些症状感觉非常熟悉，因为其中某些症状对你的生活造成了非常严重且长远的影响。但这些问题本质上还是反映了你潜意识中的感受到了无法消除的恐惧或者焦虑、或者潜意识中陷入某种矛盾、亦或是有意无意通过这些身体上的不适来获得某种特殊照顾。无论是哪一种情况，都需要及时的寻求心理医生或者心理咨询师的帮助，减少躯体化症状对自己的伤害，帮助自己探寻躯体化症状的本质原因，更好的选择相应的对策来摆脱它的困扰。另一方面，自己应该有意识地保持良好的心态，特别是对于负面的情绪，不要一味地打上不好的、没面子的、不被人接纳的标签，进而排斥、压抑和否认它们，陷入怪圈无法自拔。坏情绪也是我们心理世界的一部分，学着接纳它们的存在，通过运动、倾诉和休闲等正当的方式排解它们。"
                in 3..4 -> "|【躯体化】被测者有中度的躯体化症状。躯体化是指一个人本来有情绪问题或者心理障碍，但却没有以心理症状表现出来，而转换为各种躯体症状表现出来。主要反映身体不适感，包括心血管、胃肠道、呼吸和其他系统的不适，和头痛、背痛、肌肉酸痛，以及焦虑等躯体不适表现，但经过医院的专业检查，却没有发现明显的器质性病理变化。这些躯体的状况可能已经对你的日常学习、工作和生活造成了持久的影响。但这些问题本质上还是反映了你潜意识中的感受到了无法消除的恐惧或者焦虑、或者潜意识中陷入某种矛盾、亦或是有意无意通过这些身体上的不适来获得某种特殊照顾。无论是哪一种情况，都需要及时的寻求心理咨询师或者心理医生的帮助，帮助自己探寻躯体化症状的本质原因，更好的选择相应的对策来摆脱它的困扰。另一方面自己也应该有意识的保持良好的心态，理性面对压力境遇，通过倾诉、运动或者休闲等方式来纾解自己的不愉快。今早摆脱躯体化的影响。"
                in 2..3 -> "|【躯体化】被测者有轻微躯体化症状。躯体化是指一个人本来有情绪问题或者心理障碍，但却没有以心理症状表现出来，而转换为各种躯体症状表现出来。主要反映身体不适感，包括心血管、胃肠道、呼吸和其他系统的不适，和头痛、背痛、肌肉酸痛，以及焦虑等躯体不适表现，且这些症状不能通过生理的角度去解释。出现这些躯体化的症状可能被 “没有休息好”、“最近压力有点大”等理由搪塞过去，虽然短期内并不会带来显著的影响，但是长此以往会慢慢侵蚀我们的身心健康。所以在生活中应该学习保持良好的心理状态，理智看待压力事件，即使有坏情绪，也要学会接受它、排解它，找好朋友倾诉、或者大哭一场，或者到运动场上去挥汗一把，都能起到缓解的作用，逐渐摆脱躯体化的影响。"
                else -> "|【躯体化】被测者没有躯体化症状。躯体化是指一个人本来有情绪问题或者心理障碍，但却没有以心理症状表现出来，而转换为各种躯体症状表现出来。主要反映身体不适感，包括心血管、胃肠道、呼吸和其他系统的不适，和头痛、背痛、肌肉酸痛，以及焦虑等躯体不适表现，且这些症状并不能通过生理的角度去解释。保持良好心态、理智的面对压力境遇、及时合理地宣泄不良情绪能够使我们远离躯体化的困扰，请继续保持！"
            }
        )

        js.append(
            when (qpzavg) {
                in 4..Int.MAX_VALUE -> "|【强迫症状】被测者具有严重的强迫症状。强迫症状是指主要指那些明知没有必要，但又无法摆脱的无意义的思想、冲动和行为。常见的强迫思维有反复怀疑门窗是否关紧，碰到脏的东西会不会得病，太阳为什么从东边升起西边落下，站在阳台上就有往下跳的冲动等；常见的强迫行为有怀疑门窗是否关紧的想法，相应的就会去反复检查门窗确保安全；碰到脏东西怕得病的患者就会反复洗手以保持干净。你或许已经深受某些强迫症状的影响，对学习、工作和生活造成巨大的阻碍。建议寻求心理医生或者心理咨询师的专业援助，对当前的强迫症状做出科学的评估和处理。在生活中有意识地阻断强迫行为或者强迫思维地发生，及时缓解焦虑。不要禁锢于规则和秩序，虽然谨慎是你的优点之一，但是试着接受生活安排的惊喜，也会体会到不同的乐趣。事物并非非黑即白，非好即坏，绝大部分的人和事物都无法单纯的用二分法去判定，这是常态。同样在没有做到“完美的自己“时，也要学着接纳自己和爱自己。"
                in 3..4 -> "|【强迫症状】被测者具有中度的强迫症状。强迫症状是指主要指那些明知没有必要，但又无法摆脱的无意义的思想、冲动和行为。常见的强迫思维有反复怀疑门窗是否关紧，碰到脏的东西会不会得病，太阳为什么从东边升起西边落下，站在阳台上就有往下跳的冲动等；常见的强迫行为有怀疑门窗是否关紧的想法，相应的就会去反复检查门窗确保安全；碰到脏东西怕得病的患者就会反复洗手以保持干净。某些强迫症状以及强迫思维已经影响到了你的日常生活，并且对此你也感到烦恼和焦虑，但是身陷其中无法自拔。建议寻求心理咨询师或者心理医生的专业援助，帮助自己分析强迫症状背后的本质问题。在生活中也要学会从完美主义的禁锢中脱身，首先是悦纳自我，其次是悦纳周围的人和事物。事物发展都有自身的规律，突破这些规律的绝对的“ 完美“本身就是一个伪命题。"
                in 2..3 -> "|【强迫症状】被测者具有轻微的强迫症状。强迫症状是指主要指那些明知没有必要，但又无法摆脱的无意义的思想、冲动和行为。常见的强迫思维有反复怀疑门窗是否关紧，碰到脏的东西会不会得病，太阳为什么从东边升起西边落下，站在阳台上就有往下跳的冲动等；常见的强迫行为有怀疑门窗是否关紧的想法，相应的就会去反复检查门窗确保安全；碰到脏东西怕得病的患者就会反复洗手以保持干净。也许某些强迫症状开始慢慢影响你的正常生活，让自己处于紧张和焦虑的状态，难以得到舒缓和放松。建议在强迫症状出现时，有意识地阻断自己不理性地想法，以及及时通过合理地方式缓解焦虑。在生活中能理性地看待事物发展的规律，接受自己、周围人的不完美，迎接生活带来的惊喜，保持身心平衡。"
                else -> "|【强迫症状】被测者没有强迫症状。强迫症状是指主要指那些明知没有必要，但又无法摆脱的无意义的思想、冲动和行为。常见的强迫思维有反复怀疑门窗是否关紧，碰到脏的东西会不会得病，太阳为什么从东边升起西边落下，站在阳台上就有往下跳的冲动等；常见的强迫行为有怀疑门窗是否关紧的想法，相应的就会去反复检查门窗确保安全；碰到脏东西怕得病的患者就会反复洗手以保持干净。请继续保持良好心态，营造一个稳定、安全、和谐的生活环境。"
            }
        )

        js.append(
            when (rjgxavg) {
                in 4..Int.MAX_VALUE -> "|【人际关系】被测者具有严重的人际关系敏感。人际敏感背身并不是一件坏事，在人机交往中有一定的敏感度可以更好的保持双方的关系，但是当人际敏感过度时，就可能对我们造成一些干扰。这里的人际关系敏感主要是指某些人际的不自在与自卑感，特别是与其他人相比较时更加突出。在人际交往中的自卑感，心神不安，明显的不自在，以及人际交流中的不良自我暗示，消极的期待等是这方面症状的典型原因。这个问题的形成，可能是你在小时候就处于严格、挑剔的环境中，需要不断的察言观色、检视自己，长大之后也就造就了你小心翼翼审视外界的眼光。人际交往等周边事物可能已经深深地影响了你的学习、工作和生活的各个方面。建议主动寻求心理医生或者心理咨询师的专业援助，解决人机关系敏感的问题。可能在你的经历，特别是幼年，处于严苛、挑剔的环境中，时刻需要保持警惕、察言观色。这种经验伴随这你的成长，所以在与外界的沟通中，你亦是小心谨慎，不断检视自我。但是长大的你完全有能力和别人平等的交流，你可以是有价值、被接纳的、被善待的。在生活中应该尝试以更积极的态度来面对人际交往，摆脱过去的思维和习惯，去表达真实的自我。"
                in 3..4 -> "|【人际关系】被测者具有中度的人际关系敏感。人际敏感背身并不是一件坏事，在人机交往中有一定的敏感度可以更好的保持双方的关系，但是当人际敏感过度时，就可能对我们造成一些干扰。这里的人际关系敏感主要是指某些人际的不自在与自卑感，特别是与其他人相比较时更加突出。在人际交往中的自卑感，心神不安，明显的不自在，以及人际交流中的不良自我暗示，消极的期待等是这方面症状的典型原因。目前的人际关系已经影响到了你的学习、工作和生活，与亲友关系紧张，或在集体中与其他人有摩擦，并且对此你感到烦恼和苦闷。建议寻求心理咨询师或者心理医生的专业援助，帮助你了解到自己在人际关系敏感背后的本质原因，并且对症治疗。在生活中同样也要学会换位思考，摆脱单纯的“我“的视角看待外界和他人；愤分清主观想象与客观事实，改变自己的负面认识；多参与活动，在实践中自我修正。"
                in 2..3 -> "|【人际关系】被测者具有轻度的人际关系敏感。人际敏感背身并不是一件坏事，在人机交往中有一定的敏感度可以更好的保持双方的关系，但是当人际敏感过度时，就可能对我们造成一些干扰。这里的人际关系敏感主要是指某些人际的不自在与自卑感，特别是与其他人相比较时更加突出。在人际交往中的自卑感，心神不安，明显的不自在，以及人际交流中的不良自我暗示，消极的期待等是这方面症状的典型原因。可能在你的人际关系中存在一些不如人意的地方，例如在人际关系中会感到不自在，甚至有一点拘束；在团体当中也对于自己的位置和角色感到一些困惑；对自己的学习、工作和生活造成了潜在的影响。建议学习和锻炼符合自己社会角色的交往模式，及时跳出自己对于外界的猜疑、敌对和回避等观点的陷阱，对人际交往更多的是报以积极乐观的态度去对待，沉浸其中并且感受乐趣。"
                else -> "|【人际关系】被测者没有人际关系敏感。人际敏感背身并不是一件坏事，在人机交往中有一定的敏感度可以更好的保持双方的关系，但是当人际敏感过度时，就可能对我们造成一些干扰。这里的人际关系敏感主要是指某些人际的不自在与自卑感，特别是与其他人相比较时更加突出。在人际交往中的自卑感，心神不安，明显的不自在，以及人际交流中的不良自我暗示，消极的期待等是这方面症状的典型原因。当前你在人际互动中的感受是安全的、有价值的、被接纳的以及自在的，无论是在亲密关系或者集体之中，你都能找到适合自己的位置和心态，这样的状态是非常健康的，希望继续保持。"
            }
        )

        js.append(
            when (yyavg) {
                in 4..Int.MAX_VALUE -> "|【抑郁】被测者有重度抑郁。这里的抑郁多以苦闷的情感与心境为代表性症状，还以生活兴趣的减退，动力缺乏，活力丧失等为特征。还有可能表现出失望、悲观以及与抑郁相联系的认知和躯体方面的感受。更为严重的可能还伴随自我伤害的想法或行为。如果你已经长时间处于这样的状态，请让关心你的人知道你的状态，并且陪伴你寻求心理医生或者心理咨询师的专业援助。你可能经历了一些不好的事情，过去的你可能感到痛苦、孤独甚至绝望，我们不能改变过去的经历，但是我们可以改变现在对自己的态度和看法，接纳、善待、拥抱现在的自己。除了寻求专业心理帮助，日常生活中你也可以养成良好的作息时间、在你信任的交际圈里和他人交流、重拾自己的爱好或者长处，进行适当的体育锻炼，这些都能够让你逐渐恢复到正常的生活状态。"
                in 3..4 -> "|【抑郁】被测者有中度抑郁。这里的抑郁多以苦闷的情感与心境为代表性症状，还以生活兴趣的减退，动力缺乏，活力丧失等为特征。还有可能表现出失望、悲观以及与抑郁相联系的认知和躯体方面的感受。更为严重的可能还伴随自我伤害的想法或行为。如果你已经长时间处于这样的状态，请让关心你的人知道你的状态，并且寻求心理咨询师或者心理医生的专业援助。你可能经历了一些不好的事情，我们没有办法改变过去的经历，但是我们能改变现在对自己的态度和看法。停止对自己刻薄、扭曲、攻击性的评价，悦纳现在的自己。你可以在日常生活中养成良好的作息时间，培养个人爱好并且以此表达自己的情绪情感，向亲友或者你信任的人寻求情感上的支持，坚持适量的体育锻炼，都可以帮助你扫清眼前的阴霾，发现生活中的美好。"
                in 2..3 -> "|【抑郁】被测者有轻度抑郁。这里的抑郁多以苦闷的情感与心境为代表性症状，还以生活兴趣的减退，动力缺乏，活力丧失等为特征。还有可能表现出失望、悲观以及与抑郁相联系的认知和躯体方面的感受。也许你这段时间处于情绪低落的状态，对学习、工作或者生活很难打起精神，回避与外界的交往，记忆力下降。这些状态每个人或多或少都有过体验，所以轻度的抑郁并不可怕，都是可以改善的，养成良好的作息时间，面对挑战给自己制定科学有效的短期计划，培养个人爱好并且以此为突破口扩大社交范围，向亲友或者你信任的人寻求情感上的支持，坚持适量的体育锻炼，都可以让你很好的从轻度抑郁的状态中摆脱出来，将自己的生活调节到正常状态。"
                else -> "|【抑郁】被测者没有抑郁症状。这里的抑郁主要指的是苦闷的情感与心境，可能伴有动力缺失、活力丧失等表现。你目前并没有存在以上抑郁症状，总而言之，生活态度乐观积极，充满活力，心境愉快。希望在日后的学习、工作和生活中，要保持积极心态，同时也要培养自己的抗压能力，及时纾解不良情绪，把控好自己的身心状态。"
            }
        )

        js.append(
            when (jlavg) {
                in 4..Int.MAX_VALUE -> "|【焦虑】被测者有重度的焦虑。你可能在学习、工作和生活中，经常有神经过敏、失眠、身体震颤或者极度惊恐的体验，我们称之为焦虑。焦虑并不仅仅是一种负面的情绪，人在应激事件面前，适当的焦虑能够提高自己的身体调动的能力，集中注意力，全力应对面临的情况，是人类的一种正常情绪。但是过度的焦虑和广泛性的焦虑会严重影响我们的日常生活和身心健康。如果你频繁地陷入无法控制的焦虑状态，请及时让关心你的人了解到你的情况，并寻求心理医生或者心理咨询师的帮助，发掘焦虑症状背后的根源性问题。在日常生活中，可以自己每天抽出一定时间配合舒缓的音乐，进行放松训练或者腹式呼吸训练，来帮助自己的身体恢复平衡状态。最重要的还是放松心态，有一颗“为所当为，顺其自然“的心。"
                in 3..4 -> "|【焦虑】被测者有中度的焦虑。你可能在学习、工作和生活中，经常有神经过敏、焦灼烦躁、失眠甚至身体震颤的体验，我们称之为焦虑。焦虑并不仅仅是一种负面的情绪，人在应激事件面前，适当的焦虑能够提高自己的身体调动的能力，集中注意力，全力应对面临的情况，是人类的一种正常情绪。但是过度的焦虑和广泛性的焦虑会严重影响我们的身心健康。如果你体验到了自己无法控制、无法名状的焦虑，请寻求心理咨询师或者心理医生的专业援助。在日常生活中，可以自己每天抽出一定时间配合舒缓的音乐，进行放松训练或者腹式呼吸训练，来帮助自己的身体恢复平衡状态。"
                in 2..3 -> "|【焦虑】被测者有轻度的焦虑。你可能在学习、工作和生活中，偶尔有坐立不安，神经过敏、焦灼烦躁的体验，我们称之为焦虑。但是焦虑并不仅仅是一种负面的情绪，人在应激事件面前，适当的焦虑能够提高自己的身体调动的能力，集中注意力，全力应对面临的情况，是人类的一种正常情绪。如果你体验到了更频繁的焦虑，可以反思一下，自己最近是否面临考试、考评或者生活状态的转折。如果是由于常见问题引起的焦虑，则可以制定科学的计划，战胜当前的挑战，或者自己每天抽出一定时间配合舒缓的音乐，进行放松训练或者腹式呼吸训练，来帮助自己的身体恢复平衡状态，更好的应对挑战。"
                else -> "|【焦虑】被测者没有焦虑症状。在学习、工作和生活中也不易感受到焦虑体验，面对各种情景都能够轻松地表现出安定的状态，心理素质较好。在以后的生活中，应当保持比较稳定的心理状态，但也要注意避免自己在应激事件面前表现出的心理动力不足。"
            }
        )

        js.append(
            when (ddavg) {
                in 4..Int.MAX_VALUE -> "|【敌对】被测者有重度敌对。敌对可能表现在思想、感情及行为各个方面，包括厌烦的感觉，摔物，争论直到不可控制的脾气暴发等各方面。你可能看不惯周围很多人的待人接物的方式，因为他人表达出和你不同的观点立即以牙还牙，时常陷入无意义的争论或者仅仅因为陌生人的超车而气急败坏。敌对带来的影响是方方面面的，不仅容易导致心脏机能的受损，增加冠心病的发病风险，长此以往也会破坏身体免疫力，敌对情绪更是对心理健康造成不小的危害，轻则让自己陷入抱怨连连之中，重则产生报复心理，对自己和他人都造成不可挽回的伤害。建议及时寻求心理医生或者心理咨询师的专业援助，在日常学习、工作和生活当中，首先要学会多站在他人的立场上设身处地的想一想，理解他人；其次，要对外界抱有平和的心态，与人为善，宽容待人；然后，要纠正自己的认知，很多“假想敌”都是自己树立起来的；最后还需要学会控制自己的情绪，并不是一味的忍耐和压制，而是需要通过运动、倾诉等科学的方式宣泄出来。"
                in 3..4 -> "|【敌对】被测者有中度敌对。敌对可能表现在思想、感情及行为各个方面，包括厌烦的感觉，摔物，争论直到不可控制的脾气暴发等各方面。你可能在生活中体验到情绪的失控感，容易和他人意见相左并且引发不愉快的讨论，对外界的普通刺激例如诚恳的建议想象成挖苦和批评。敌对带来的影响是方方面面的，不仅容易导致心脏机能的受损，增加冠心病的发病风险，长此以往也会破坏身体免疫力，敌对情绪更是对心理健康造成不小的危害，可能引起抑郁、焦虑等其他心理障碍。建议寻求心理咨询师或者心理医生的专业援助，在生活中也要督促自己与人为善，宽容待人，不要抱以偏见；要善于控制自己的情绪，合理宣泄不良情绪，理性分析总结。逐渐就可以将不合理的敌对情绪化解于无形。"
                in 2..3 -> "|【敌对】被测者有轻度敌对。敌对可能表现在思想、感情及行为各个方面，包括厌烦的感觉，摔物，争论直到不可控制的脾气暴发等各方面。你可能在生活中经常看不惯别人做的事情，不轻易原谅曾经让你难堪或者伤害过你的人，甚至对别人的超车都比较在意。其实这样的状态，我们每个人都会有过相似的体验，特别是青春期阶段。但是敌对带来的影响是方方面面的，不仅容易导致心脏机能的受损，破坏身体免疫力，敌对情绪更是对心理健康造成不小的危害。建议在日常生活中，要拥有一颗平常心，对他人多有包容的心态，理性的面对外界的刺激，多站在让人的立场上想一想，摘掉心底的有色眼镜。"
                else -> "|【敌对】被测者没有敌对症状。脾气越温和，待人友好，不喜欢争论、无破坏行为。在人际交往中更容易与他人结交友谊或者亲密关系，相处起来也犹如柔风甘雨，受到他人的喜爱和欢迎，为学习、工作和生活营造了健康的人际交往环境。未来请继续保持！"
            }
        )
        js.append(
            when (kjavg) {
                in 4..Int.MAX_VALUE -> "|【恐怖】被测者有重度恐怖。恐怖是指对某些特定的对象或处境产生强烈和不必要的恐惧情绪，而且伴有明显的焦虑或其症状，倾向于采取主动回避的方式来解除这种不安。明知这种恐惧是不合理、不必要的，但仍然无法控制，以至于影响正常学习、工作和生活。恐惧是人类的一种正常的情绪，正是因为恐惧，我们的祖先才能够规避野兽和灾害。但是过度的恐惧也会对我们造成伤害，长期处于惊恐状态会严重影响内分泌及神经系统，诱发躯体上的病症，另外也会影响我们的日常学习、工作和生活。你可能在生活中经常体验到恐怖情绪，可能是针对某种物品，也有可能是针对某种特殊场景，并且对此表现出回避和抗拒，甚至体验过强烈不适，可有胸闷、气透不过来的感觉、心悸、出汗、胃不适、颤抖、手足发麻，甚至是濒死感、要发疯感或失去控制感。建议寻求心理医生或者心理咨询师的专业援助，首先消除过度的恐怖情绪对身体的二次伤害。在日常生活中，也可以反思恐怖的背后是否有着夸张、偏激或者不合理的观点，调整自己的认知模式。或者在亲友陪伴的情况下逐渐暴露在容易引起恐怖的环境之中，削弱这种刺激与恐怖情绪之间的链接。在经历极度的恐惧时，请告诉自己“这种感觉会过去的，我会很安全“，顺从地接受它地发生和消退，减少在这个过程中收到的伤害。"
                in 3..4 -> "|【恐怖】被测者有中度恐怖。恐怖是指对某些特定的对象或处境产生强烈和不必要的恐惧情绪，而且伴有明显的焦虑或其症状，倾向于采取主动回避的方式来解除这种不安。明知这种恐惧是不合理、不必要的，但仍然无法控制，以至于影响正常学习、工作和生活。你可能在生活中经常体验到恐怖情绪，可能是针对某种物品，也有可能是针对某种特殊场景，并且对此表现出回避和抗拒，甚至体验过强烈不适，可有胸闷、气透不过来的感觉、心悸、出汗、胃不适、颤抖、手足发麻。建议寻求心理咨询师或者心理医生的专业援助。自己也可以反思恐怖的背后是否有着夸张、偏激或者不合理的观点，调整自己的认知模式。或者在亲友陪伴的情况下逐渐暴露在容易引起恐怖的环境之中，削弱这种刺激与恐怖情绪之间的链接。在经历短时间的极度恐惧时，告诉自己能够应付这种感觉，顺其自然地接受它的发生直至消失。"
                in 2..3 -> "|【恐怖】被测者有轻度恐怖。恐怖是指对某些特定的对象或处境产生强烈和不必要的恐惧情绪，而且伴有明显的焦虑或其症状，倾向于采取主动回避的方式来解除这种不安。明知这种恐惧是不合理、不必要的，但仍然无法控制，以至于影响正常学习、工作和生活。你在日常生活中可能会表现出对社交环境的恐惧和厌恶，或者是避免进入人流量较大的公共环境，或者对其他某种物体或场所的恐惧。恐惧是人类的一种正常的情绪，正是因为恐惧，我们的祖先才能够规避野兽和灾害。但是过度的恐惧也会对我们造成伤害，长期处于惊恐状态会严重影响内分泌及神经系统，诱发躯体上的病症，另外也会影响我们的日常学习、工作和生活。恐惧是人类的一种正常的情绪，正是因为恐惧，我们的祖先才能够规避野兽和灾害。但是过度的恐惧也会对我们造成伤害，长期处于惊恐状态会严重影响内分泌及神经系统，诱发躯体上的病症，另外也会影响我们的日常学习、工作和生活。建议在日常生活中，首先辨别出诱发自己恐怖情绪的对象，具体到某个事物或者某种情境，再思考是否对其抱有不合理、扭曲或者夸张的认识，从而调整自己的认知模式，或者逐渐暴露于这种环境之中，削弱这种刺激与恐怖情绪之间的链接，最终做到弱化直至摆脱不合理的恐怖情绪。"
                else -> "|【恐怖】被测者没有恐怖症状。恐怖是指对某些特定的对象或处境产生强烈和不必要的恐惧情绪。你不易产生恐怖心理，对外部世界有着较为理性和稳定的理解，能够正常的进行人际交往等活动，也能适应日常学习、工作和生活中的不同场景和境遇。请继续保持良好的作息规律，保持心态平衡。"
            }
        )
        js.append(
            when (pzavg) {
                in 4..Int.MAX_VALUE -> "|【偏执】被测者有重度的偏执。你可能在面对学习、工作和生活经常陷入钻牛角尖，对他人几乎都抱有偏见，无法获得足够的安全感，好与人争斗，甚至总认为自己是对的，善于诡辩，这些表现我们可以称之为偏执。请让关心你的人了解你的状况，并且寻求心理医生或者心理咨询师的专业援助。在日常生活中，要意识到偏执的危害，不仅会使亲情冷淡，同事关系疏远，还得不到他人的理解、支持和同情，使自己陷于孤立的境地，同时还常使自己陷于激愤的情绪之中，影响身体健康。其次要学会思想方法，客观地分析和认识事物，克服主观片面性。对于非原则性问题，要以随和、宽容的态度来对待，不必事事较真，以仁义之心待之，就能协调人际关系。然后要经常不懈地进行自我克制、自我疏导，控制不良情绪发生。最后适当地处理自己的言论，避免与他人发生矛盾。"
                in 3..4 -> "|【偏执】被测者有中度的偏执。你可能在面对学习、工作和生活经常陷入钻牛角尖，容易有偏见，无法获得安全感，好与人争斗，这些表现我们可以称之为偏执。请让关心你的人了解你的状况，并且寻求心理咨询师或者心理医生的专业援助。自己也需要了解到偏执对生活的负面影响，坚持从多个角度看待自己和客观世界，学会信任他人，真诚相待，去接纳和自己不同的世界。"
                in 2..3 -> "|【偏执】被测者有轻度的偏执。你可能在面对学习、工作和生活偶尔陷入固执、钻牛角尖，无法获得安全感，这些表现我们可以归总为偏执。如果自己比其他人更多的体验到偏执，可以做出适当的自我调节。例如从自己信任的亲人或者朋友交流，试着从他们的角度去思考，然后逐渐将这种技巧转移到与其他人或者团体的交往之中，培养自己客观、多维度的看待事物的认知能力。其次要学会信任他人，真诚相待，接受他人与自己的不同之处。通过坚持不懈的训练将会让自己尽快远离偏执的陷阱。"
                else -> "|【偏执】被测者没有偏执症状。个体思维越不易走极端，面对学习、工作和生活中的不同境遇，都能够较为理智、客观的对待。请继续保持。"
            }
        )

        js.append(
            when (jsbavg) {
                in 4..Int.MAX_VALUE -> "|【精神病性】被测者有重度的精神病性症状。这里所说的具有精神病性症状并不等同于精神病，正常人在特殊情况下也有可能出现幻觉和妄想的症状，但能够及时察觉且没有影响日常生活。你可能在日常生活中经常看见别人无法察觉的人，听到脑海里有人与自己对话或是给你下指令，感觉自己的思维可以被其他人察觉或者被控制，并且面对这些情况你完全无法控制。建议让你信任的人了解你的情况，并在他的陪伴下寻求心理医生或者心理咨询师的专业援助。在日常生活中保持良好的作息习惯，身心平衡。"
                in 3..4 -> "|【精神病性】被测者有中度的精神病性症状。这里所说的具有精神病性症状并不等同于精神病，正常人在特殊情况下也有可能出现幻觉和妄想的症状，但能够及时察觉且没有影响日常生活。你可能在日常生活中经常看见别人无法察觉的人，听到脑海里有人与自己对话或是给你下指令，感觉自己的思维可以被其他人察觉，并且面对这些情况你完全无法控制。建议让你信任的人了解你的情况，并寻求心理医生或者心理咨询师的专业援助。在日常生活中保持良好的作息习惯，身心平衡。"
                in 2..3 -> "|【精神病性】被测者有轻度的精神病性症状。这里所说的具有精神病性症状并不等同于精神病，正常人在特殊情况下也有可能出现幻觉和妄想的症状，但能够及时察觉且没有影响日常生活。你可能经常看见别人无法察觉的人，或者听到脑海里有人与自己对话，亦或是命令你，这些情况使你开始对周围事物感到困惑或者焦虑。建议让你信任的人了解你的情况，并寻求心理医生或者心理咨询师的专业援助。在日常生活中，形成良好的作息规律，放松心态，理性对待，帮助自己尽快摆脱这些症状的阴影。"
                else -> "|【精神病性】被测者没有精神病性症状。这里所说的具有精神病性症状并不等同于精神病，正常人在特殊情况下也有可能出现幻觉和妄想的症状，但能够及时察觉且没有影响日常生活。请继续保持良好的作息规律和心理健康。"
            }
        )
        js.append(
            when (otheravg) {
                in 4..Int.MAX_VALUE -> "|【其它】被测者在睡眠和饮食上有严重问题。你可能对于生活中体验到入睡困难、夜间易醒、过度嗜睡、暴饮暴食或者病态节食非常苦恼，这些问题对你的学习、工作和生活造成了严重的影响。请咨询相关科室的医生或者保健医生，来排除器质性的问题。自己也可以尝试探究这些问题背后是否存在潜在的、根深蒂固的想法，如果有夸张、极端或者不合理的想法，可以咨询心理医生或者心理咨询师。同时需要制定合理且科学的睡眠饮食计划，让信任的亲友监督自己逐步调整生活规律，包括睡眠和饮食等习惯。好身体才是好心情地保证。"
                in 3..4 -> "|【其它】被测者在睡眠和饮食上有一些问题。你可能在生活中体验到入睡困难、夜间易醒、过度嗜睡、暴饮暴食或者病态节食，这些问题可能经常困扰你，但是又难以通过自己的努力解除。请咨询相关科室的医生或者保健医生，来排除器质性的问题。自己也可以尝试探究这些问题背后是否存在潜在的、根深蒂固的想法，如果有夸张、极端或者不合理的想法，可以咨询心理咨询师。同时需要监督自己或者让朋友监督自己根据自己的事情情况制定适合自己的作息或饮食计划，并执行下去。好身体才是好心情地保证。"
                in 2..3 -> "|【其它】被测者在睡眠和饮食上有轻度问题。你可能在生活中体验到偶尔的入睡困难、夜间易醒、过度嗜睡、暴饮暴食或者节食，如果这些情况发生的概率不高或者后果不严重，则通过自己的调理就可以恢复正常。在日常生活中要有意识地调节睡眠、饮食地规律，根据自身地实际情况以及科学地建议，制定合理地计划，并且坚持和修正。好身体才是好心情地保证。"
                else -> "|【其它】被测者睡眠、饮食情况良好。请继续保持良好的作息规律，在白天保持旺盛的精力，在晚上能够拥有安稳的睡眠。另一方面也有保持饮食的平衡，荤素搭配，热量合理，保证身体能够获得充足、高质量的能源。"
            }
        )

        val avgGreaterOneCount = ((count - OnePoint_Count) / GreaterOne_Count).toString()//阳性平均数
        val factorAvgList = mutableListOf(
            "-",
            avg.toString(),
            "-",
            "-",
            avgGreaterOneCount,
            (somatization / somatizationList.count()).toString(),
            (obsession / obsessionList.count()).toString(),
            (personalRelations / personalRelationsList.count()).toString(),
            (depression / depressionList.count()).toString(),
            (anxiety / anxietyList.count()).toString(),
            (hostility / hostilityList.count()).toString(),
            (phobicanxiety / phobicanxietyList.count()).toString(),
            (parnoidIdeation / parnoidIdeationList.count()).toString(),
            (psychotism / psychotismList.count()).toString(),
            (other / otherList.count()).toString()
        )  //各因素平均分
        val factorList = mutableListOf(
            "总分",
            "总均分",
            "阴性项目数",
            "阳性项目数",
            "阳性项目平均分",
            "躯体化",
            "强迫症",
            "人际关系敏感",
            "抑郁",
            "焦虑",
            "敌对",
            "恐怖",
            "偏执",
            "精神病性",
            "其它"
        ) //因素
        val factorCountList = mutableListOf(
            count,
            (count / 90f).roundToInt(),
            OnePoint_Count,
            GreaterOne_Count,
            0,
            somatization,
            obsession,
            personalRelations,
            depression,
            anxiety,
            hostility,
            phobicanxiety,
            parnoidIdeation,
            psychotism,
            other
        ) //各因素分数总和
        val standardScoreList = mutableListOf(
            "129.96±38.76",
            "1.44±0.43",
            "24.92±18.41",
            "65.08±18.33",
            "2.60±0.59",
            "1.37±0.48",
            "1.62±0.58",
            "1.65±0.51",
            "1.50±0.59",
            "1.39±0.43",
            "1.48±0.56",
            "1.23±0.41",
            "1.43±0.57",
            "1.29±0.42",
            "0"
        )
        // 参考诊断
        val ckzd = arrayListOf<String>()
        for (i in factorAvgList.indices) {
            val value = factorAvgList[i].toIntOrNull()
            if (value == null || i <= 4) {
                ckzd.add("-")
            } else {
                ckzd.add(
                    when {
                        value < 2 -> "正常"
                        value in 2 until 3 -> "轻度"
                        value in 3 until 4 -> "中度"
                        else -> "重度"
                    }
                )
            }
        }
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = factorList.joinToString("|"),
            BZF = factorCountList.joinToString("|"),
            JF = factorAvgList.joinToString("|"),
            BZC = standardScoreList.joinToString("|"),
            YJ = js.toString(),
            XX = xx,
            cf = ckzd.joinToString("|")
        )

    }
}