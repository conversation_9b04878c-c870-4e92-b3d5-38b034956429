package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils

/**
 * 情绪控制量表(CECS)
 */
class ResultUtilCECSV2 {

    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {

        val zf = QuizUtils.getList(1, 1, 21)
        var count = 0
        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            val value = answers[index].toInt() + 1
            if (index in zf) count += value
        }
        val yj = when (count) {
            in 21..41 -> "您的情绪控制能力有点差,这可能会影响到您的工作,生活和人际交往。建议可适当学习控制自己的情绪,试着在自己愤怒,悲伤和烦恼时有效调节自己的情绪,并将消极情绪有效转变成积极情绪。建议您可以选择以下几种积极的方式调控自己的情绪：①深呼吸法,在愤怒,悲伤和烦恼时试着做几次深呼吸,让自己平静下来；②转移视线法,在愤怒,悲伤和烦恼时试着将自己的思路转移到一些可能令自己开心的事情上；③转化情境法,在愤怒,悲伤和烦恼时尽快离开让自己产生这些情绪的情境；④积极认知法,通过训练使自己习惯于用积极,阳光的方式看待生活中的不幸。"
            in 42..63 -> "您的情绪控制方式相对比较成熟,既不会过分压抑自己的负面情绪,也能够有效疏导自己的负面情绪。建议您可以通过积极认知的方法,通过训练使自己习惯于用积极,阳光的方式看待生活中的挫折和不幸,不断提高自己情绪管理的能力。"
            in 64..84 -> "您可能倾向于或习惯于压抑和过分控制自己的情绪,一方面过分压抑自己的情绪可能会使您经常情绪低落,心态郁闷消极,甚至偶会会觉得生活缺少意义；另一方面,过分压抑和控制自己的情绪会使您感到身心俱疲,久而久之甚至会影响到您的身心健康。当然,也有些人在长期过分压抑自己的情绪后会出现突然的情绪爆发的情况,突然丧失理智,做出令自己后悔莫及的事情。建议您可以选择以下几种积极的方式调控自己的情绪：①向自己觉得值得信任的亲朋好友倾诉；②参加体育锻炼,特别是一些对抗性的体育竞赛,宣泄自己的不良情绪；③学习一些科学情绪调控的方法,比如“咬筷子”法等等"
            else -> ""
        }
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "总分",
            BZF = "$count",
            JF = "",
            BZC = "",
            YJ = yj,
            XX = answers.joinToString("|"),
        )
    }
}