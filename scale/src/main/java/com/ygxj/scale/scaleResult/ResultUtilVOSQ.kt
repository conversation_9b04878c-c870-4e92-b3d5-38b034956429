package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils
import kotlin.random.Random

class ResultUtilVOSQ {
    /**
     *  职业倾向系列问卷
     *  0是  1否
     */
    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        //量表类型
        //因素
        //兴趣倾向分量表 1-48
        val xrList = QuizUtils.getList(1, 6, 8)//R型
        val xiList = QuizUtils.getList(2, 6, 8)//I型
        val xaList = QuizUtils.getList(3, 6, 8) //A型
        val xsList = QuizUtils.getList(4, 6, 8)//s型
        val xeList = QuizUtils.getList(5, 6, 8)//E型
        val xcList = QuizUtils.getList(6, 6, 8)//C型
//		个体经历分问卷 49-96
        val grList = QuizUtils.getList(49, 6, 8)
        val giList = QuizUtils.getList(50, 6, 8)
        val gaList = QuizUtils.getList(51, 6, 8)
        val gsList = QuizUtils.getList(52, 6, 8)
        val geList = QuizUtils.getList(53, 6, 8)
        val gcList = QuizUtils.getList(54, 6, 8)
        // 人格倾向分问卷 97-144
        val rrList = QuizUtils.getList(97, 6, 8)
        val riList = QuizUtils.getList(98, 6, 8)
        val raList = QuizUtils.getList(99, 6, 8)
        val rsList = QuizUtils.getList(100, 6, 8)
        val reList = QuizUtils.getList(101, 6, 8)
        val rcList = QuizUtils.getList(102, 6, 8)
        //兴趣倾向
        var xrCount = 0
        var xiCount = 0
        var xaCount = 0
        var xsCount = 0
        var xeCount = 0
        var xcCount = 0
        //个体经历
        var grCount = 0
        var giCount = 0
        var gaCount = 0
        var gsCount = 0
        var geCount = 0
        var gcCount = 0
        //人格倾向
        var rrCount = 0
        var riCount = 0
        var raCount = 0
        var rsCount = 0
        var reCount = 0
        var rcCount = 0

        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            //兴趣倾向
            if (xrList.contains(index)) xrCount += if (answers[index].toInt() == 0) 1 else 0
            if (xiList.contains(index)) xiCount += if (answers[index].toInt() == 0) 1 else 0
            if (xaList.contains(index)) xaCount += if (answers[index].toInt() == 0) 1 else 0
            if (xsList.contains(index)) xsCount += if (answers[index].toInt() == 0) 1 else 0
            if (xeList.contains(index)) xeCount += if (answers[index].toInt() == 0) 1 else 0
            if (xcList.contains(index)) xcCount += if (answers[index].toInt() == 0) 1 else 0
            //个体经历
            if (grList.contains(index)) grCount += if (answers[index].toInt() == 0) 1 else 0
            if (giList.contains(index)) giCount += if (answers[index].toInt() == 0) 1 else 0
            if (gaList.contains(index)) gaCount += if (answers[index].toInt() == 0) 1 else 0
            if (gsList.contains(index)) gsCount += if (answers[index].toInt() == 0) 1 else 0
            if (geList.contains(index)) geCount += if (answers[index].toInt() == 0) 1 else 0
            if (gcList.contains(index)) gcCount += if (answers[index].toInt() == 0) 1 else 0
            //人格倾向 前42题(97 - 138) 选是得1分 后6(139 - 144) 题选是得0分 选否得1分
            if (index in 97..138) {
                if (rrList.contains(index)) rrCount += if (answers[index].toInt() == 0) 1 else 0
                if (riList.contains(index)) riCount += if (answers[index].toInt() == 0) 1 else 0
                if (raList.contains(index)) raCount += if (answers[index].toInt() == 0) 1 else 0
                if (rsList.contains(index)) rsCount += if (answers[index].toInt() == 0) 1 else 0
                if (reList.contains(index)) reCount += if (answers[index].toInt() == 0) 1 else 0
                if (rcList.contains(index)) rcCount += if (answers[index].toInt() == 0) 1 else 0
            } else if (index in 139..144) {
                if (rrList.contains(index)) rrCount += if (answers[index].toInt() == 1) 1 else 0
                if (riList.contains(index)) riCount += if (answers[index].toInt() == 1) 1 else 0
                if (raList.contains(index)) raCount += if (answers[index].toInt() == 1) 1 else 0
                if (rsList.contains(index)) rsCount += if (answers[index].toInt() == 1) 1 else 0
                if (reList.contains(index)) reCount += if (answers[index].toInt() == 1) 1 else 0
                if (rcList.contains(index)) rcCount += if (answers[index].toInt() == 1) 1 else 0
            }
        }

        //现实型(R) 探索型(I) 艺术型(A) 社会型(S) 管理型(E) 常规型(C)
        //六种类型总分统计
        val rCount = xrCount + grCount + rrCount
        val iCount = xiCount + giCount + riCount
        val aCount = xaCount + gaCount + raCount
        val sCount = xsCount + gsCount + rsCount
        val eCount = xeCount + geCount + reCount
        val cCount = xcCount + gcCount + rcCount
        val countList = mutableListOf(rCount, iCount, aCount, sCount, eCount, cCount)

        val max = countList.max()
        val yjList = mutableListOf<String>()//意见
        //判断属于哪种类型
        if (max == rCount) yjList.add("【现实型】|人格特征：安分随流,直率坦诚,实事求是,循规蹈矩,坚忍不拔,情绪稳定,勤劳节俭。|职业特征：需要进行明确,具体分工的,并有一定程序要求的技术型,技能型工作。")
        if (max == iCount) yjList.add("【探索型】|人格特征：分析能力强,善于内省,独立,好奇心强烈,含蓄。|职业特征：通过观察和科学分析进行系统的创造性活动,一般的研究对象侧重于自然科学而不是社会科学方面。");
        if (max == aCount) yjList.add("【艺术型】|人格特征：独立不倚,创新求异,热衷表现,激情洋溢,感情丰富,理想主义。|职业特征：善于通过非系统化的,自由的活动进行艺术表现,但精细的操作能力较差。");
        if (max == sCount) yjList.add("【社会型】|人格特征：助人为乐,有责任心,热情,开朗,友好,善良,易于合作。|职业特征：从事更多时间与人打交道的说服,教育和治疗工作。");
        if (max == eCount) yjList.add("【管理型】|人格特征：敢做敢为,信心百倍,乐观,冲动,爱自我显示,精力旺盛。|职业特征：适于从事需要胆略,冒风险和承担责任的活动。");
        if (max == cCount) yjList.add("【常规型】|人格特征：循规蹈矩,踏实稳当,忠实可靠,顺从听话。|职业特征：适于从事需要严格按照固定的规则和方法进行的重复性,习惯性的活动,能够较快地见到自己的劳动成果,需要一定的自控能力。");

        val ysm = mutableListOf(
            "现实型(R)", "探索型(I)", "艺术型(A)", "社会型(S)", "管理型(E)", "常规型(C)"
        ).joinToString("|")
        val bzf = countList.joinToString("|")
        val jf = mutableListOf(
            (rCount / 24f).toFloat().toString(),
            (iCount / 24f).toFloat().toString(),
            (aCount / 24f).toFloat().toString(),
            (sCount / 24f).toFloat().toString(),
            (eCount / 24f).toFloat().toString(),
            (cCount / 24f).toFloat().toString()
        ).joinToString("|")
        val yj = yjList[Random.nextInt(yjList.size)]
        val xx = answers.joinToString("|")
        val bzc = ""
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = ysm,
            BZF = bzf,
            JF = jf,
            YJ = yj,
            BZC = bzc,
            XX = xx
        )

    }
}