package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils

class ResultUtilIRIDS {
    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        //该检查表由四部分内容组成。
        //第一部分： 交谈
        //第二部分： 交际与交友
        //第三部分： 待人接物
        //第四部分： 异性交往
        val list_1 = mutableListOf(1, 5, 9, 13, 17, 21, 25)
        val list_2 = mutableListOf(2, 6, 10, 14, 18, 22, 26)
        val list_3 = mutableListOf(3, 7, 11, 15, 19, 23, 27)
        val list_4 = mutableListOf(4, 8, 12, 16, 20, 24, 28)

        var count_1 = 0
        var count_2 = 0
        var count_3 = 0
        var count_4 = 0

        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            val value = answers[index].toInt() + 1
            if (list_1.contains(index))
                count_1 += if (value == 2) 0 else 1
            if (list_2.contains(index))
                count_2 += if (value == 2) 0 else 1
            if (list_3.contains(index))
                count_3 += if (value == 2) 0 else 1
            if (list_4.contains(index))
                count_4 += if (value == 2) 0 else 1
        }

        val YJ_1 = if (count_1 < 3) "较高的交谈能力和技巧。" else if (count_1 in 3..5) "交谈能力一般。" else "不善于交谈。"
        val YJ_2 = when {
            count_2 < 3 -> "对人较为真诚和热情。"
            count_2 in 3..5 -> "你在被动地寻找被人喜欢的突破口。"
            else -> "在社交活动与交友方面存在着较大的行为困扰。"
        }

        val YJ_3 = when {
            count_3 < 3 -> "较尊重别人，敢于承担责任，对环境的适应性强。"
            count_3 in 3..5 -> "是个多侧面的人，可以算是一个较圆滑的人。"
            else -> "缺乏待人接物的机智与技巧。"
        }

        val YJ_4 = when {
            count_4 < 3 -> "表明你懂得如何真确处理异性朋友之间的关系。"
            count_4 in 3..5 -> "与异性同学交往的行为困扰程度一般。"
            else -> "在于异性交往的过程中存在较为严重的困扰。"
        }
        val total = count_1 + count_2 + count_3 + count_4
        val yj = if (total < 9) {
            "你在与朋友相处上的困扰较少。你善于交谈，性格比较开朗，主动，关心别人，你对周围的朋友都比较好，愿意和他们在一起，他们也都喜欢你，你们相处得不错。而且，你能够从与朋友相处中，得到乐趣。你的生活是比较充实而且丰富多彩的，你与异性朋友也相处得比较好。一句话，你不存在或较少存在交友方面的困扰，你善于与朋友相处，人缘很好，获得许多的好感与赞同。"
        } else if (total in 9..14) {
            "你与朋友相处存在一定程度的困扰。你的人缘很一般，换句话说，你和朋友的关系并不牢固，时好时坏，经常处在一种起伏波动之中。"
        } else if (total in 15..20) {
            "你在同朋友相处上的行为困扰较严重不善于交谈，缺乏待人接物的机智与技，在于异性交往、社交活动与交友方面存在着较大的行为困扰。"
        } else {
            "你的人际关系困扰程度很严重，而且在心理上出现较为明显得障碍。你可能不善于交谈，也可能是一个性格孤僻的人，不开朗，或者有明显得自高自大、讨人嫌的行为。"
        } + "[交谈方面]|" + YJ_1 + "[交际方面]|" + YJ_2 + "[待人接物]|" + YJ_3 + "[异性交往]|" + YJ_4

        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "总分|交谈方面|交际方面|待人接物|异性交往",
            BZF = "${total}|${count_1}|${count_2}|${count_3}|${count_4}",
            JF = "",
            YJ = yj,
            BZC = "",
            XX = answers.joinToString("|")
        )
    }
}