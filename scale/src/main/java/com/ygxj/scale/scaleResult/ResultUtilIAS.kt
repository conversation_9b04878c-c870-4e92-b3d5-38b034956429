package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity

//交往焦虑量表(IAS)
class ResultUtilIAS {
    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        val fList = mutableListOf(3, 6, 10, 15)//反向记分
        var count = 0 //总分
        for (index in answers.indices) {
            val value = answers[index].toInt() + 1
            count += if (index in fList) {
                when (value) {
                    5 -> 1
                    4 -> 2
                    3 -> 3
                    2 -> 4
                    1 -> 5
                    else -> 0
                }
            } else value
        }
        val results = mutableListOf(
            "社交焦虑中等程度。|受测者在某些事先不曾做好准备的社交情境中，会有一定的焦虑感出现。若是情境相对于受测者而言越显陌生，受测者越容易感到紧张不安。当受测者对情境渐渐熟悉和具有一定的控制能力后，会感觉好很多。一般而言这种焦虑感不太会影响到受测者的社交生活和对自己社交形象的评价。|测试分数≥29分为异常，分值越高，社交焦虑症状越重。",
            "社交焦虑程度高。|在一般的社交情境中，受测者常常体验到高度的紧张焦虑，有时还伴有心跳加速的反应。受测者对自己身处陌生情境感到非常不自在，很在意他人对外表以及言谈举止的评价，却又没有勇气去探究和证实别人的看法。在这种心理下，受测者容易产生被动应付的行为和希望回避的想法。|测试分数≥29分为异常，分值越高，社交焦虑症状越重。",
            "社交焦虑无。|在一般的社交情境中，受测者通常能够应对自如。面对不太熟悉的人，受测者常常充满自信，态度大方，极少有紧张拘束的感觉。"
        )
        val yj = StringBuffer()
            .append("您本次的得分为${count}分，")
            .append(
                when (count) {
                    in 29..49 -> results[0]
                    in 50..Int.MAX_VALUE -> results[1]
                    in 0..28 -> results[2]
                    else -> ""
                }
            ).toString()
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "分值",
            BZF = count.toString(),
            JF = (count / answers.size).toString(),
            YJ = yj,
            BZC = "",
            XX = answers.joinToString("|")
        )

    }
}