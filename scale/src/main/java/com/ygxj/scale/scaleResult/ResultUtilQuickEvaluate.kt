package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils

/**
 * 快速评估五个问卷,计分方式都是一样的
 * 690	紧张情绪状态评估问卷
 * 691	害怕/恐惧情绪状态评估问卷
 * 692	悲伤情绪状态评估问卷
 * 693	愤怒情绪状态评估问卷
 * 694	无助感状态评估问卷
 */
class ResultUtilQuickEvaluate {

    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        var count = 0//总分

        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            val selectScore = answers[index].toInt()
            count += selectScore
        }
        var result = ""

        when (scaleId) {
            690 -> {
                result = when (count) {
                    in 0..10 -> "正常波动状态"
                    in 11..20 -> "轻度紧张状态"
                    in 21..30 -> "中度紧张状态"
                    in 31..40 -> "高度紧张状态"
                    else -> ""
                }
            }

            691 -> {
                result = when (count) {
                    in 0..12 -> "正常反应状态"
                    in 13..22 -> "恐惧敏感状态"
                    in 23..32 -> "高度恐惧状态"
                    in 33..40 -> "危机状态"
                    else -> ""
                }
            }

            692 -> {
                result = when (count) {
                    in 0..12 -> "情境性悲伤状态"
                    in 13..24 -> "轻度悲伤状态"
                    in 25..34 -> "中度悲伤状态"
                    in 35..40 -> "重度悲伤状态"
                    else -> ""
                }
            }

            693 -> {
                result = when (count) {
                    in 0..8 -> "健康表达状态"
                    in 9..18 -> "易激惹倾向状态"
                    in 19..28 -> "攻击行为风险状态"
                    in 29..40 -> "暴力倾向状态"
                    else -> ""
                }
            }

            694 -> {
                result = when (count) {
                    in 0..10 -> "短暂挫折感状态"
                    in 11..20 -> "习得性无助状态"
                    in 21..30 -> "中度无助状态"
                    in 31..40 -> "重度无助状态"
                    else -> ""
                }
            }
        }

        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "总分",
            BZF = "$count",
            JF = "",
            YJ = result,
            BZC = "",
            XX = answers.joinToString("|")
        )
    }
}