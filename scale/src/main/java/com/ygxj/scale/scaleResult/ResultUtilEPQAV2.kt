package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity

//艾森克人格问卷成人式(EPQA)
class ResultUtilEPQAV2 {

    /**
     * 评分标准
     * EPQ问卷总共有四个分量表（E分量表指内外向，N分量表指情绪稳定性，P分量表指精神质，L分量表指掩饰、虚假），共88道题，每道题的记分方法如下
     */
    fun getResult(
        scaleId: Int,
        answers: List<String>,
        costTime: String,
        userAge: Int,
        userSex: String
    ): ScaleResultEntity {
        //<editor-fold desc="计算出四个分量表的粗分,即根据选项计算出得分">
        // 正数表示回答“是”时记1分，回答“否”时记0分
        // 负数表示回答“否”时记1分，回答“是”时记0分
        // E分量表（共21个项目）：  1  5  －9  13  17  21  25  28  33  37  41  45  －49  57  61  64  71  75  79  83  85
        // N分量表（共24个项目）：  2  6  10  14  18  22  26  29  34  38  42  46  50  52  54  58  60  62  65  68  72  76  81  86
        // P分量表（共23个项目）：  3  7  12  15  19  23  －27  －30  32  35  39  43  47  51  53  55  59  63  －66  69  73  84  88
        // L分量表（共20个项目）： －4  8  －11  －16  －20  24  31  36  －40  44  48  56  －67  70  74  77  78  －80  －82  －87

        // 计算E分量表粗分
        val ePositiveIndex = arrayOf(1, 5, 13, 17, 21, 25, 28, 33, 37, 41, 45, 57, 61, 64, 71, 75, 79, 83, 85)
        val eNegativeIndex = arrayOf(9, 49)
        val eCoarseScore = calculateCoarseScore(ePositiveIndex, eNegativeIndex, answers)

        // 计算N分量表粗分
        val nPositiveIndex = arrayOf(2, 6, 10, 14, 18, 22, 26, 29, 34, 38, 42, 46, 50, 52, 54, 58, 60, 62, 65, 68, 72, 76, 81, 86)
        val nCoarseScore = calculateCoarseScore(nPositiveIndex, emptyArray(), answers)

        // 计算P分量表粗分
        val pPositiveIndex = arrayOf(3, 7, 12, 15, 19, 23, 32, 35, 39, 43, 47, 51, 53, 55, 59, 63, 69, 73, 84, 88)
        val pNegativeIndex = arrayOf(27, 30, 66)
        val pCoarseScore = calculateCoarseScore(pPositiveIndex, pNegativeIndex, answers)

        // 计算L分量表粗分
        val lPositiveIndex = arrayOf(8, 24, 31, 36, 44, 48, 56, 70, 74, 77, 78)
        val lNegativeIndex = arrayOf(4, 11, 16, 20, 40, 67, 80, 82, 87)
        val lCoarseScore = calculateCoarseScore(lPositiveIndex, lNegativeIndex, answers)
        //</editor-fold>

        //<editor-fold desc="根据粗分计算出标准分T">
        val eStandardScore = calculateStandardScore(eCoarseScore, userAge, userSex, "e")
        val nStandardScore = calculateStandardScore(nCoarseScore, userAge, userSex, "n")
        val pStandardScore = calculateStandardScore(pCoarseScore, userAge, userSex, "p")
        val lStandardScore = calculateStandardScore(lCoarseScore, userAge, userSex, "l")

        val eResultText = getResultText("e", eStandardScore)
        val nResultText = getResultText("n", nStandardScore)
        val pResultText = getResultText("p", pStandardScore)
        val lResultText = getResultText("l", lStandardScore)
        val list = arrayListOf<String>()
        list.add(eResultText.joinToString("\n"))
        list.add(nResultText.joinToString("\n"))
        list.add(pResultText.joinToString("\n"))
        list.add(lResultText.joinToString(""))

        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "内外向(E)|神经质(N)|精神质(P)|掩饰性(L)",
            BZF = "${eStandardScore}|${nStandardScore}|${pStandardScore}|${lStandardScore}",
            BZC = "",
            JF = "",
            YJ = list.joinToString("\n"),
            cf = "${eCoarseScore}|${nCoarseScore}|${pCoarseScore}|${lCoarseScore}",
            XX = answers.joinToString("|"),
        )

        //</editor-fold>
    }


    /**
     * 计算粗分
     * @param positionIndex 选择是+1分的题目索引,从1开始计
     * @param negativeIndex 选择否+1分题索引,从1开始计
     * @param answers 用户选项 0代表选择了是,1代表选择了否
     */
    private fun calculateCoarseScore(positionIndex: Array<Int>, negativeIndex: Array<Int>, answers: List<String>): Int {
        var score = 0
        for (i in answers.indices) {
            // 题目序号
            when (i + 1) {
                // 选择'是'+1分的题目
                in positionIndex -> if (answers[i] == "0") score += 1
                // 选择'否'+1分的题目
                in negativeIndex -> if (answers[i] == "1") score += 1
            }
        }
        return score
    }

    /**
     * 计算各分量表标准分T
     * @param coarseScore 分量表粗分
     * @param userAge 用户年龄
     * @param userSex 用户性别
     * @param flag 分量表标识 P,E,N,L
     */
    private fun calculateStandardScore(coarseScore: Int, userAge: Int, userSex: String, flag: String): Int {
        //<editor-fold desc="">
        return when {
            userAge in 16 until 20 -> {
                when (flag.lowercase()) {
                    //<editor-fold desc="p分量表">
                    "p" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                in 1..2 -> 40
                                in 3..5 -> 45
                                in 6..7 -> 50
                                in 8..9 -> 55
                                in 10..11 -> 60
                                in 12..13 -> 65
                                in 14..16 -> 70
                                in 17..18 -> 75
                                in 19..20 -> 80
                                in 21..22 -> 85
                                23 -> 90
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 35
                                2 -> 40
                                3 -> 45
                                in 4..5 -> 50
                                6 -> 55
                                7 -> 60
                                in 8..9 -> 65
                                10 -> 70
                                11 -> 75
                                in 12..13 -> 80
                                14 -> 85
                                in 15..16 -> 90
                                17 -> 95
                                18 -> 100
                                in 19..20 -> 105
                                21 -> 110
                                22 -> 115
                                23 -> 120
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="e分量表">
                    "e" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                in 1..2 -> 25
                                in 3..4 -> 30
                                in 5..6 -> 35
                                in 7..8 -> 40
                                in 9..10 -> 45
                                in 11..12 -> 50
                                in 13..14 -> 55
                                in 15..16 -> 60
                                in 17..18 -> 65
                                in 19..20 -> 70
                                21 -> 75
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 30
                                in 3..4 -> 35
                                in 5..6 -> 40
                                in 7..8 -> 45
                                in 9..10 -> 50
                                in 11..12 -> 55
                                in 13..14 -> 60
                                in 15..16 -> 65
                                in 17..18 -> 70
                                in 19..20 -> 75
                                21 -> 80
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="n分量表">
                    "n" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                1 -> 25
                                in 2..3 -> 30
                                in 4..5 -> 35
                                in 6..8 -> 40
                                in 9..10 -> 45
                                in 11..12 -> 50
                                in 13..15 -> 55
                                in 16..17 -> 60
                                in 18..19 -> 65
                                in 20..22 -> 70
                                in 23..24 -> 75
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 25
                                in 2..3 -> 30
                                in 4..5 -> 35
                                in 6..8 -> 40
                                in 9..10 -> 45
                                in 11..13 -> 50
                                in 14..15 -> 55
                                in 16..17 -> 60
                                in 18..20 -> 65
                                in 21..22 -> 70
                                in 23..24 -> 75
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="l分量表">
                    "l" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                1 -> 30
                                in 2..4 -> 35
                                in 5..6 -> 40
                                in 7..8 -> 45
                                in 9..10 -> 50
                                in 11..12 -> 55
                                in 15..16 -> 60
                                in 17..18 -> 65
                                in 19..20 -> 70
                                21 -> 75
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 20
                                in 2..3 -> 25
                                in 4..5 -> 30
                                in 6..7 -> 35
                                in 8..9 -> 40
                                in 10..11 -> 45
                                in 12..13 -> 50
                                in 14..15 -> 55
                                in 16..17 -> 60
                                in 18..19 -> 65
                                in 20..21 -> 70
                                22 -> 75
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    else -> 0
                }
            }
            userAge in 20 until 30 -> {
                when (flag.lowercase()) {
                    //<editor-fold desc="p分量表">
                    "p" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                1 -> 35
                                in 2..3 -> 40
                                4 -> 45
                                in 5..6 -> 50
                                7 -> 55
                                in 8..9 -> 60
                                10 -> 65
                                11 -> 70
                                in 12..13 -> 75
                                14 -> 80
                                in 15..16 -> 85
                                17 -> 90
                                18 -> 95
                                in 19..20 -> 100
                                21 -> 105
                                in 22..23 -> 110
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                in 1..2 -> 40
                                3 -> 45
                                in 4..5 -> 50
                                6 -> 55
                                in 7..8 -> 60
                                9 -> 65
                                in 10..11 -> 70
                                12 -> 75
                                in 13..14 -> 80
                                15 -> 85
                                in 16..17 -> 90
                                18 -> 95
                                19 -> 100
                                in 20..21 -> 105
                                22 -> 110
                                23 -> 115
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="e分量表">
                    "e" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                in 1..2 -> 30
                                in 3..4 -> 35
                                in 5..6 -> 40
                                in 7..9 -> 45
                                in 10..11 -> 50
                                in 12..13 -> 55
                                in 14..15 -> 60
                                in 16..17 -> 65
                                in 18..20 -> 70
                                21 -> 75
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                in 1..2 -> 35
                                in 3..4 -> 40
                                in 5..7 -> 45
                                in 8..9 -> 50
                                in 10..11 -> 55
                                in 12..13 -> 60
                                in 14..16 -> 65
                                in 17..18 -> 70
                                in 19..20 -> 75
                                21 -> 80
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="n分量表">
                    "n" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                in 1..2 -> 25
                                in 3..4 -> 30
                                in 5..6 -> 35
                                in 7..8 -> 40
                                in 9..10 -> 45
                                in 11..12 -> 50
                                in 13..14 -> 55
                                in 15..16 -> 60
                                in 17..18 -> 65
                                in 19..20 -> 70
                                in 21..22 -> 75
                                in 23..24 -> 80
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                in 1..2 -> 25
                                in 3..4 -> 30
                                in 5..7 -> 35
                                in 8..9 -> 40
                                in 10..11 -> 45
                                in 12..13 -> 50
                                in 14..15 -> 55
                                in 16..18 -> 60
                                in 19..20 -> 65
                                in 21..22 -> 70
                                in 23..24 -> 75
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="l分量表">
                    "l" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                1 -> 20
                                in 2..3 -> 25
                                in 4..5 -> 30
                                in 6..7 -> 35
                                in 8..9 -> 40
                                10 -> 45
                                in 11..12 -> 50
                                in 13..14 -> 55
                                in 15..16 -> 60
                                17 -> 65
                                in 18..19 -> 70
                                21 -> 75
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 15
                                2 -> 20
                                in 3..4 -> 25
                                in 5..6 -> 30
                                in 7..8 -> 35
                                in 9..10 -> 40
                                11 -> 45
                                in 12..13 -> 50
                                in 14..15 -> 55
                                in 16..17 -> 60
                                in 18..19 -> 65
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    else -> 0
                }
            }
            userAge in 30 until 40 -> {
                when (flag.lowercase()) {
                    //<editor-fold desc="p分量表">
                    "p" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                1 -> 35
                                2 -> 40
                                in 3..4 -> 45
                                in 5..6 -> 50
                                7 -> 55
                                in 8..9 -> 60
                                in 10..11 -> 65
                                12 -> 70
                                in 13..14 -> 75
                                in 15..16 -> 80
                                17 -> 85
                                in 18..19 -> 90
                                in 20..21 -> 95
                                22 -> 100
                                23 -> 105
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 40
                                in 2..3 -> 45
                                in 4..5 -> 50
                                6 -> 55
                                in 7..8 -> 60
                                in 9..10 -> 65
                                11 -> 70
                                in 12..13 -> 75
                                in 14..15 -> 80
                                16 -> 85
                                in 17..18 -> 90
                                in 19..20 -> 95
                                21 -> 100
                                in 22..23 -> 105
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="e分量表">
                    "e" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                in 1..2 -> 30
                                in 3..4 -> 35
                                in 5..6 -> 40
                                in 7..8 -> 45
                                in 9..10 -> 50
                                in 11..12 -> 55
                                in 13..14 -> 60
                                in 15..16 -> 65
                                in 17..18 -> 70
                                in 19..20 -> 75
                                21 -> 80
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                in 1..2 -> 35
                                in 3..5 -> 40
                                in 6..7 -> 45
                                in 8..9 -> 50
                                in 10..11 -> 55
                                in 12..14 -> 60
                                in 15..16 -> 65
                                in 17..18 -> 70
                                in 19..20 -> 75
                                21 -> 80
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="n分量表">
                    "n" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                1 -> 25
                                in 2..3 -> 30
                                in 4..5 -> 35
                                in 6..8 -> 40
                                in 9..10 -> 45
                                in 11..12 -> 50
                                in 13..14 -> 55
                                in 15..17 -> 60
                                in 18..19 -> 65
                                in 20..21 -> 70
                                in 22..24 -> 75
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                in 1..2 -> 30
                                in 3..5 -> 35
                                in 6..7 -> 40
                                in 8..10 -> 45
                                in 11..12 -> 50
                                in 13..15 -> 55
                                in 16..17 -> 60
                                in 18..20 -> 65
                                in 21..22 -> 70
                                in 23..24 -> 75
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="l分量表">
                    "l" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                1 -> 20
                                in 2..3 -> 25
                                in 4..5 -> 30
                                in 6..7 -> 35
                                8 -> 40
                                in 9..10 -> 45
                                in 11..12 -> 50
                                in 13..14 -> 55
                                in 15..16 -> 60
                                in 17..18 -> 65
                                in 19..20 -> 70
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 15
                                in 2..3 -> 20
                                in 4..5 -> 25
                                in 6..7 -> 30
                                in 8..9 -> 35
                                10 -> 40
                                in 11..12 -> 45
                                in 13..14 -> 50
                                in 15..16 -> 55
                                in 17..18 -> 60
                                in 19..20 -> 65
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>
                    else -> 0
                }
            }
            userAge in 40 until 50 -> {
                when (flag.lowercase()) {
                    //<editor-fold desc="p分量表">
                    "p" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                in 1..2 -> 35
                                3 -> 40
                                4 -> 45
                                5 -> 50
                                in 6..7 -> 55
                                8 -> 60
                                9 -> 65
                                10 -> 70
                                in 11..12 -> 75
                                13 -> 80
                                14 -> 85
                                in 15..16 -> 90
                                17 -> 95
                                18 -> 100
                                19 -> 105
                                in 20..21 -> 110
                                22 -> 115
                                23 -> 120
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 40
                                2 -> 45
                                in 3..4 -> 50
                                5 -> 55
                                6 -> 60
                                7 -> 65
                                8 -> 70
                                in 9..10 -> 75
                                11 -> 80
                                12 -> 85
                                13 -> 90
                                14 -> 95
                                in 15..16 -> 100
                                17 -> 105
                                18 -> 110
                                19 -> 115
                                20 -> 120
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="e分量表">
                    "e" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                in 1..3 -> 35
                                in 4..5 -> 40
                                in 6..8 -> 45
                                in 9..10 -> 50
                                in 11..12 -> 55
                                in 13..15 -> 60
                                in 16..17 -> 65
                                in 18..19 -> 70
                                in 20..21 -> 75
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                in 1..2 -> 35
                                in 3..4 -> 40
                                in 5..6 -> 45
                                in 7..9 -> 50
                                in 10..11 -> 55
                                in 12..13 -> 60
                                in 14..15 -> 65
                                in 16..17 -> 70
                                in 18..19 -> 75
                                in 20..21 -> 80
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="n分量表">
                    "n" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                in 1..3 -> 35
                                in 4..5 -> 40
                                in 6..8 -> 45
                                in 9..10 -> 50
                                in 11..13 -> 55
                                in 14..15 -> 60
                                in 16..18 -> 65
                                in 19..21 -> 70
                                in 22..23 -> 75
                                24 -> 80
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 30
                                in 2..4 -> 35
                                in 5..7 -> 40
                                in 8..10 -> 45
                                in 11..13 -> 50
                                in 14..16 -> 55
                                in 17..18 -> 60
                                in 19..21 -> 65
                                in 22..24 -> 70
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="l分量表">
                    "l" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                1 -> 15
                                in 2..3 -> 20
                                in 4..5 -> 25
                                6 -> 30
                                in 7..8 -> 35
                                in 9..10 -> 40
                                in 11..12 -> 45
                                13 -> 50
                                in 14..15 -> 55
                                in 16..17 -> 60
                                in 18..19 -> 65
                                in 20..21 -> 70
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 5
                                2 -> 10
                                in 3..4 -> 15
                                5 -> 20
                                in 6..7 -> 25
                                in 8..9 -> 30
                                10 -> 35
                                in 11..12 -> 40
                                13 -> 45
                                in 14..15 -> 50
                                in 16..17 -> 55
                                18 -> 60
                                in 19..20 -> 65
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    else -> 0
                }
            }
            userAge in 50 until 60 -> {
                when (flag.lowercase()) {
                    //<editor-fold desc="p分量表">
                    "p" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                1 -> 35
                                in 2..3 -> 40
                                4 -> 45
                                in 5..6 -> 50
                                in 7..8 -> 55
                                9 -> 60
                                in 10..11 -> 65
                                in 12..13 -> 70
                                14 -> 75
                                in 15..16 -> 80
                                in 17..18 -> 85
                                19 -> 90
                                in 20..21 -> 95
                                22 -> 100
                                23 -> 105
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 40
                                2 -> 45
                                in 3..4 -> 50
                                5 -> 55
                                in 6..7 -> 60
                                8 -> 65
                                in 9..10 -> 70
                                11 -> 75
                                in 12..13 -> 80
                                14 -> 85
                                15 -> 90
                                in 16..17 -> 95
                                18 -> 100
                                in 19..20 -> 105
                                21 -> 110
                                22 -> 115
                                23 -> 120
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="e分量表">
                    "e" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                1 -> 30
                                in 2..3 -> 35
                                in 4..5 -> 40
                                in 6..7 -> 45
                                in 8..9 -> 50
                                10 -> 55
                                in 11..12 -> 60
                                in 13..14 -> 65
                                in 15..16 -> 70
                                in 17..18 -> 75
                                in 19..20 -> 80
                                21 -> 85
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 30
                                in 2..3 -> 35
                                in 4..5 -> 40
                                in 6..7 -> 45
                                in 8..9 -> 50
                                in 10..11 -> 55
                                in 12..14 -> 60
                                in 15..16 -> 65
                                in 17..18 -> 70
                                in 19..20 -> 75
                                21 -> 80
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="n分量表">
                    "n" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                1 -> 30
                                in 2..3 -> 35
                                in 4..6 -> 40
                                in 7..9 -> 45
                                in 10..11 -> 50
                                in 12..14 -> 55
                                in 15..17 -> 60
                                in 18..19 -> 65
                                in 20..22 -> 70
                                in 23..24 -> 75
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 30
                                in 2..4 -> 35
                                in 5..6 -> 40
                                in 8..10 -> 45
                                11 -> 50
                                in 12..14 -> 55
                                in 15..17 -> 60
                                in 18..19 -> 65
                                in 20..22 -> 70
                                in 23..24 -> 75
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="l分量表">
                    "l" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                1 -> 15
                                in 2..3 -> 20
                                4 -> 25
                                in 5..6 -> 30
                                in 7..8 -> 35
                                in 9..10 -> 40
                                in 11..12 -> 45
                                in 13..14 -> 50
                                in 15..16 -> 55
                                17 -> 60
                                in 18..19 -> 65
                                20 -> 70
                                21 -> 75
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                in 1..2 -> 20
                                in 3..4 -> 25
                                in 5..6 -> 30
                                in 7..8 -> 35
                                in 9..10 -> 40
                                in 11..12 -> 45
                                in 13..14 -> 50
                                in 15..16 -> 55
                                in 17..18 -> 60
                                in 19..20 -> 65
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    else -> 0
                }
            }
            userAge >= 60 -> {
                when (flag.lowercase()) {
                    //<editor-fold desc="p分量表">
                    "p" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                1 -> 35
                                2 -> 40
                                3 -> 45
                                4 -> 50
                                5 -> 55
                                6 -> 60
                                in 7..8 -> 65
                                9 -> 70
                                10 -> 75
                                11 -> 80
                                12 -> 85
                                13 -> 90
                                in 14..15 -> 95
                                16 -> 100
                                17 -> 105
                                18 -> 110
                                19 -> 115
                                20 -> 120
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 40
                                2 -> 45
                                3 -> 50
                                in 4..5 -> 55
                                6 -> 60
                                7 -> 65
                                8 -> 70
                                in 9..10 -> 75
                                11 -> 80
                                12 -> 85
                                13 -> 90
                                14 -> 95
                                in 15..16 -> 100
                                17 -> 105
                                18 -> 110
                                19 -> 115
                                20 -> 120
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="e分量表">
                    "e" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                in 1..3 -> 35
                                in 4..5 -> 40
                                in 6..8 -> 45
                                in 9..10 -> 50
                                in 11..12 -> 55
                                in 13..15 -> 60
                                in 16..17 -> 65
                                in 18..19 -> 70
                                in 20..21 -> 75
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 30
                                in 2..3 -> 35
                                in 4..5 -> 40
                                in 6..7 -> 45
                                in 8..9 -> 50
                                in 10..12 -> 55
                                in 13..14 -> 60
                                in 15..16 -> 65
                                in 17..18 -> 70
                                in 19..20 -> 75
                                21 -> 80
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="n分量表">
                    "n" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                in 1..2 -> 35
                                in 3..5 -> 40
                                in 6..7 -> 45
                                in 8..9 -> 50
                                in 10..11 -> 55
                                in 12..14 -> 60
                                in 15..16 -> 65
                                in 17..18 -> 70
                                in 19..21 -> 75
                                in 22..24 -> 80
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                in 1..2 -> 30
                                in 3..4 -> 35
                                in 5..7 -> 40
                                in 8..9 -> 45
                                in 10..12 -> 50
                                in 13..14 -> 55
                                in 15..17 -> 60
                                in 18..19 -> 65
                                in 20..22 -> 70
                                23 -> 75
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    // <editor-fold desc="l分量表">
                    "l" -> {
                        if (userSex == "男") {
                            return when (coarseScore) {
                                1 -> 0
                                in 2..3 -> 5
                                4 -> 10
                                in 5..6 -> 15
                                7 -> 20
                                8 -> 25
                                in 9..10 -> 30
                                11 -> 35
                                12 -> 40
                                in 13..14 -> 45
                                15 -> 50
                                16 -> 55
                                in 17..18 -> 60
                                19 -> 65
                                20 -> 70
                                else -> 0
                            }
                        } else {
                            return when (coarseScore) {
                                1 -> 15
                                in 2..3 -> 20
                                in 4..5 -> 25
                                in 6..7 -> 30
                                in 8..9 -> 35
                                10 -> 40
                                in 11..12 -> 45
                                in 13..14 -> 50
                                in 15..16 -> 55
                                in 17..18 -> 60
                                in 19..20 -> 65
                                else -> 0
                            }
                        }
                    }
                    //</editor-fold>

                    else -> 0
                }
            }
            else -> 0
        }
        //</editor-fold>
    }


    /**
     * 根据标准分,得出结论
     * @param flag 分量表, e,n,a,l
     * @param score 标准分
     */
    private fun getResultText(flag: String, score: Int): List<String> {
        val result = arrayListOf<String>()
        when (flag.lowercase()) {
            "p" -> {
                result.add("【精神质(P)】${score}分。")
                when {
                    score <= 38 -> result.add("热情奔放，为人热心，善解人意。富有同情感，对环境适应性强，容易与他人相处！有同情心；对他人有爱心，能遵守社会规范。")
                    score in 39..43 -> result.add("性情较开朗，能与人相处，态度温和，不粗暴，关心他人，较有同情心，易适应环境，易与人相处")
                    score in 44..57 -> result.add("中间型。性格没有明显的神经质倾向，与人交往平淡，没有特别的敌意、也没有特别的热情。 ")
                    score in 58..61 -> result.add("性情较孤独古怪，容易引起麻烦，对别人关注较少，兴趣有些与众不同，对事情爱抱挑剔态度，环境适应性稍有不足！")
                    else -> result.add("P量表得分高。独身、不关心人；常有麻烦，在哪里都不合适；可能是残忍的、不人道的；缺乏同情心、感觉迟钝；对他人抱有敌意，即使对亲友也如此；进攻，即使是喜爱的人；喜欢一些古怪的不平常的事情。不惧危险，喜欢恶作剧，总要捣乱。")

                }
                result.add("P量表主要用于测潜在的精神特质，也称倔强性，并非暗指精神病，它在所有人身上都存在，只是程度不同。但如果某人表现出明显程度，则易发展成行为异常。如果P分特高，则考虑有精神病倾向，应作进一步检查，以明确诊断。该量表为单极量表，即：只有P分高时才有意义，P分低被认为是正常。")
            }
            "e" -> {
                result.add("【内外向(E)】${score}分。")
                when {
                    score <= 38 -> result.add("性格内向。喜静，保守，离群，与人保持一定距离（除非挚友），喜欢有秩序的生活方式，很少进攻，情绪比较稳定。倾向于事前有计划，做事观前顾后，不凭一时冲动，踏实可靠，多少有些悲观。")
                    score in 39..43 -> result.add("性格内倾。不擅交际，不太喜欢和别人相处，爱静不爱动，喜欢幻想，不喜欢兴奋的事，日常生活有规律，严谨。很少进攻行为，遇事主动性不强。")
                    score in 44..57 -> result.add("中间型。即不外向也不内向。不太爱交际或参加联欢会；朋友不是很多，不太喜欢冒险；不太随和、乐观；既可以动也可以静；可以控制自己的情绪，还算塌实。")
                    score in 58..61 -> result.add("性格外倾。比较爱交际，容易和朋友/同学相处，有时爱炫耀自己，喜欢引起他人注意，行为容易受外界环境影响。个体性格发展比较好，社会成熟度比较高。")
                    else -> result.add("典型外向。爱交际，喜欢参加联欢会，朋友多，渴望兴奋的事，喜欢冒险，行动受一时冲动的影响；随和、乐观，喜欢谈笑，宁愿动而不愿静，倾向进攻。总的来说是情绪失控的人，不是一个很踏实的人。")
                }
            }
            "n" -> {
                result.add("【神经质(N)】${score}分。")
                when {
                    score <= 38 -> result.add("情绪反应微弱而缓慢。平静而有节制，很少冲动、焦虑和紧张。通常是平静的，即使生点气也是有节制的，并且不紧张。通常表现得比较稳重，性情温和，善于自我控制。个体情绪发展良好。")
                    score in 39..43 -> result.add("情绪反应较缓慢而弱。遇事较冷静而有节制，冲动、焦虑和紧张情绪较少发生。个体情绪发展良好。")
                    score in 44..57 -> result.add("中间型。情绪即不特别容易激动也不十分平静，情绪反应适中；平时不受焦虑、紧张、易怒的困扰。")
                    score in 58..61 -> result.add("情绪欠稳定。有时伴焦虑紧张，易烦恼，常常感到苦恼，不快乐，对各种刺激反应较强，在与外向结合时，这种人是容易冒火的和不休息的，以致激动，进攻。处理问题有时欠理智！")
                    else -> result.add("情绪不稳。焦虑，紧张，易怒，常忧郁不乐，由于强烈的情绪反应而影响了他的正常反应。不可理喻，有时走上危险道路。对各种刺激反应强烈，缺乏理智，易患神经症。")
                }
                result.add("测量个体的神经质－情绪稳定程度，神经质反应个体的情绪不稳定程度，每个人的情绪都有不同程度的稳定性，越神经质的个体，情绪越不稳定。神经质并不是指个体患有神经病，但如果某人表现出明显程度，则易发展成行为异常。如果N分特高，则考虑有神经病倾向，应作进一步检查，以明确诊断。")
            }
            "l" -> {
                result.add("【掩饰性(L)】${score}分。")
                when {
                    score <= 38 -> result.add("本测验可靠性较高，测验有效！")
                    score in 39..43 -> result.add("回答真实；比较淳朴、直率。测验有效。")
                    score in 44..57 -> result.add("回答比较真实；测验结果可供参考。")
                    score in 58..61 -> result.add("提示测试回答中可能有掩饰现象，本测试在有效范围内！")
                    else -> result.add("提示个性清高，过于严格要求自己，或有心理掩饰现象，测试回答欠真实性，本测试的可靠性不高！")
                }
                result.add("原本为一个效度量表，测量回答问题的真实性，同时，它本身也代表一种稳定的人格功能，如：可测量社会纯朴性。L分的高低与许多因素有关，如：年龄（成人：随年龄增加而升高，儿童：随年龄增加而下降），性别：女性偏高；与民族有关等。")
            }
        }
        return result
    }
}
