package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils
import kotlin.math.roundToInt

/**
 * 家庭教育方式综合测评
 */
class ResultUtilJTJY {

    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {

        //变量定义
        val gtlist = mutableListOf(1, 2, 3, 4, 5)//沟通程度
        val zylist = mutableListOf(6, 7, 8)//自由程度
        val halist = mutableListOf(9, 10, 11, 12)//和蔼程度
        val dllist = mutableListOf(13, 14, 15, 16, 19)//独立程度
        val fslist = mutableListOf(17, 18, 20)//放手程度
        val pdlist = mutableListOf(21, 22, 23, 24)//平等程度
        val ahlist = mutableListOf(25, 26, 27, 28)//爱护程度
        val ghlist = mutableListOf(29, 30, 31, 32)//关怀程度
        val gllist = mutableListOf(33, 34, 35, 36)//鼓励程度
        val qmlist = mutableListOf(37, 38, 39, 40)//亲密程度
        val zrlist = mutableListOf(41, 42, 43, 44)//责任程度
        val qdlist = mutableListOf(45, 46, 47, 48, 54)//期待程度
        val glllist = mutableListOf(49, 50, 51, 52, 56)// 管理程度
        val zglist = mutableListOf(53, 55)//照顾程度

        val flist = mutableListOf(
            1,
            3,
            5,
            7,
            9,
            11,
            13,
            15,
            17,
            19,
            21,
            23,
            25,
            27,
            29,
            31,
            33,
            35,
            37,
            39,
            41,
            43,
            45,
            47,
            49,
            51,
            53,
            55
        )

        var gtcount = 0
        var zycount = 0
        var hacount = 0
        var dlcount = 0
        var fscount = 0
        var pdcount = 0
        var ahcount = 0
        var ghcount = 0
        var glcount = 0
        var qmcount = 0
        var zrcount = 0
        var qdcount = 0
        var gllcount = 0
        var zgcount = 0

        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            val value = answers[index].toInt() + 1
            gtcount += if (gtlist.contains(index)) getValue(index, flist, value) else 0
            zycount += if (zylist.contains(index)) getValue(index, flist, value) else 0
            hacount += if (halist.contains(index)) getValue(index, flist, value) else 0
            dlcount += if (dllist.contains(index)) getValue(index, flist, value) else 0
            fscount += if (fslist.contains(index)) getValue(index, flist, value) else 0
            pdcount += if (pdlist.contains(index)) getValue(index, flist, value) else 0
            ahcount += if (ahlist.contains(index)) getValue(index, flist, value) else 0
            ghcount += if (ghlist.contains(index)) getValue(index, flist, value) else 0
            glcount += if (gllist.contains(index)) getValue(index, flist, value) else 0
            qmcount += if (qmlist.contains(index)) getValue(index, flist, value) else 0
            zrcount += if (zrlist.contains(index)) getValue(index, flist, value) else 0
            qdcount += if (qdlist.contains(index)) getValue(index, flist, value) else 0
            gllcount += if (glllist.contains(index)) getValue(index, flist, value) else 0
            zgcount += if (zglist.contains(index)) getValue(index, flist, value) else 0
        }
        val count =
            gtcount + zycount + hacount + dlcount + fscount + pdcount + ahcount + ghcount + glcount + qmcount + zrcount + qdcount + gllcount + zgcount

        val Zgtcount = (gtcount / 5f).roundToInt()
        val Zzycount = (zycount / 3f).roundToInt()
        val Zhacount = (hacount / 4f).roundToInt()
        val Zdlcount = (dlcount / 5f).roundToInt()
        val Zfscount = (fscount / 3f).roundToInt()
        val Zpdcount = (pdcount / 4f).roundToInt()
        val Zahcount = (ahcount / 4f).roundToInt()
        val Zghcount = (ghcount / 4f).roundToInt()
        val Zglcount = (glcount / 4f).roundToInt()
        val Zqmcount = (qmcount / 4f).roundToInt()
        val Zzrcount = (zrcount / 4f).roundToInt()
        val Zqdcount = (qdcount / 5f).roundToInt()
        val Zgllcount = (gllcount / 5f).roundToInt()
        val Zzgcount = (zgcount / 2f).roundToInt()
        val Zcount = (count / 56f).roundToInt()
        val jieguolist = mutableListOf(
            "家庭教育方式存在严重问题|" +
                    "【沟通程度】家长与和孩子之间的交流与沟通关系非常不好。 |【自由程度】家长给孩子自由做主的空间太小。|【和蔼程度】家长教育孩子时特别没有耐心。|【独立程度】家长不允许孩子有独立处理问题的机会。|【放手程度】家长对孩子生活、学习、行为等各个方面都要管理。|【平等程度】家长不尊重孩子，将自己置于很权威的地位。" +
                    "|【爱护程度】家长不太喜欢孩子。|【关怀程度】家长对孩子的生活、学习、行为等的关怀程度非常不够。|【鼓励程度】家长对孩子的优点不能给予及时鼓励。|【亲密程度】家长与孩子之间的信任程度以及关系亲密程度不高。|【责任程度】家长对孩子未来的行为发展要求不高。|【期待程度】家长对孩子未来的整体发展不寄予希望。" +
                    "|【管理程度】家长对孩子的言行、举止不能做到及时指导。|【照顾程度】家长不太考虑让孩子舒适生活。" +
                    "|家教指导建议：|被测试者的家庭教育方式存在严重问题。被测试者对孩子没有尽到基本的责任，不太关心和爱护自己的孩子。在教育方式上存在严重问题，在此给予被测试者如下建议：" +
                    "|1、改变教育方式。粗暴的教育方式只会让孩子离你越来越远。家长既想管孩子，又说不了三句，就句句是恶言！你对孩子没有耐心，孩子对外界的事物也没有耐心，你永远是拿你的行为在影响孩子。" +
                    "|2、尊重孩子。家长应当与孩子保持平等的地位，家长的威严并不来源于“天赋”的权威性。只有家长愿意以平等的姿态与孩子相处，才能真正得到孩子的尊重和信任。摆谱只能说你不自信。" +
                    "|3、平等沟通。将自己的位置放低，变成孩子的朋友，真正的去了解他（她），读懂他（她）。只有真正了解孩子，了解孩子的需要和想法，才能有的放矢地给予孩子引导和教育。家长在与孩子沟通的过程中不要把自己看作是“无事不通的专家”，不断的向孩子提出指导和要求。沟通必须是双向的，否则，家长单方面向孩子输入的信息一定是无效的，甚至是错误的。" +
                    "|4、允许孩子保持自己的个性。每个人的性格无疑都是独一无二的，性格的形成与人的遗传因素和环境因素有密切的关系。每一种性格也都有各自的优缺点，家长不要对孩子的个性特点妄加指责。即使孩子的一些个性特点不利于其适应社会，那么社会实践的经历会促使他进行修正，家长过分的干涉既会影响亲子关系又容易伤害孩子的自尊、自信。",
            "家庭教育方式存在问题。| 【 沟通程度 】 家长与和孩子之间的交流与沟通关系有点差 。 |【自由程度】家长给孩子自由做主的空间不大。|【和蔼程度】家长教育孩子时有时缺乏耐心。|【独立程度】家长允许孩子独立处理问题的机会不太多，有时喜欢包办。|【放手程度】家长对孩子生活、学习、行为等各个方面管束的程度有点高。|【平等程度】家长不尊重孩子，将自己置于相对权威的地位，不太顾及孩子的感受。|【爱护程度】家长对孩子的喜欢，不会在孩子面前很好的表现出来。|【关怀程度】家长对孩子的生活、学习、行为等的关注程度不高。|【鼓励程度】家长对孩子的优点不能及时给予鼓励。|【亲密程度】家长与孩子之间的信任程度以及关系亲密程度不太高，孩子不太喜欢与家长说心里话。|【责任程度】家长对孩子未来行为发展要求不高。|【期待程度】家长对孩子将来的发展寄予希望比较少。|【管理程度】家长对孩子的言行、举止缺乏做到及时指导。|【照顾程度】家长对孩子生活考虑不太周到。|家教指导建议：|被测试者的家庭教育方式存在问题。被测试者对孩子教育方式较单一、粗暴，不太关心和爱护自己的孩子。在教育方式上存在问题，在此给予被测试者如下家庭指导建议：|1、改变教育方式。粗暴的教育方式只会让孩子离你越来越远。家长既想管孩子，又说不了三句，就句句是恶言！你对孩子缺乏耐心，同时也不太关注孩子的优点，这样孩子会越来越自卑。改变教育方式，给孩子自信吧！|2、尊重孩子。家长应当与孩子保持平等的地位，家长的威严并不来源于“天赋”的权威性。只有家长愿意以平等的姿态与孩子相处，才能真正得到孩子的尊重和信任。摆谱只能说你不自信。|3、平等沟通。将自己的位置放低，变成孩子的朋友，真正的去了解他（她），读懂他（她）。只有真正了解孩子，了解孩子的需要和想法，才能有的放矢地给予孩子引导和教育。家长在与孩子沟通的过程中不要把自己看作是“无事不通的专家”，不断的向孩子提出指导和要求。沟通必须是双向的，否则，家长单方面向孩子输入的信息一定是无效的，甚至是错误的。|4、允许孩子保持自己的个性。每个人的性格无疑都是独一无二的，性格的形成与人的遗传因素和环境因素有密切的关系。每一种性格也都有各自的优缺点，家长不要对孩子的个性特点妄加指责。即使孩子的一些个性特点不利于其适应社会，那么社会实践的经历会促使他进行修正，家长过分的干涉既会影响亲子关系又容易伤害孩子的自尊、自信。",
            "家庭教育方式一般。|【沟通程度】家长与和孩子之间的交流与沟通关系一般。 |【自由程度】家长一般能给孩子自由做主的空间。|【和蔼程度】家长教育孩子时一般都有耐心。|【独立程度】家长一般能允许孩子独立处理问题的机会。|【放手程度】家长对孩子生活、学习、行为等各个方面管束的程度一般。|【平等程度】家长在尊重孩子方面表现一般。|【爱护程度】家长对孩子的喜欢，一般能在孩子面前表现出来。|【关怀程度】家长对孩子的生活、学习、行为等的关注程度一般。|【鼓励程度】家长对孩子的优点给予及时鼓励方面表现一般。|【亲密程度】家长与孩子之间的信任程度以及关系的亲密程度一般，孩子不太喜欢与家长说心里话。|【责任程度】家长对孩子未来行为发展要求不高。|【期待程度】家长对孩子将来的发展寄予希望一般。|【管理程度】家长对孩子的言行、举止一般能做到及时指导。|【照顾程度】家长对孩子生活考虑表现一般。|家教指导建议：|被测试者的家庭教育方式一般。一般情况下，都能与孩子进行友好沟通，但是如果改进一点教育方式，会给孩子更好的明天。在此给予被测试者如下家庭指导建议：|1、增加和孩子相处的时间。陪伴在孩子身边，有时不需要多说，只是孩子在学习时你拿本书默默的在身边共同学习！这就是对孩子最好的心理支持！你的支持会给孩子信心，同时，你也会发现越来越能懂孩子，你们之间的交流越来越多了。|2、尊重孩子。家长应当与孩子保持平等的地位，家长的威严并不来源于“天赋”的权威性。只有家长愿意以平等的姿态与孩子相处，才能真正得到孩子的尊重和信任。摆谱只能说你不自信。|3、平等沟通。将自己的位置放低，变成孩子的朋友，真正的去了解他（她），读懂他（她）。只有真正了解孩子，了解孩子的需要和想法，才能有的放矢地给予孩子引导和教育。家长在与孩子沟通的过程中不要把自己看作是“无事不通的专家”，不断的向孩子提出指导和要求。沟通必须是双向的，否则，家长单方面向孩子输入的信息一定是无效的，甚至是错误的。|4、允许孩子保持自己的个性。每个人的性格无疑都是独一无二的，性格的形成与人的遗传因素和环境因素有密切的关系。每一种性格也都有各自的优缺点，家长不要对孩子的个性特点妄加指责。即使孩子的一些个性特点不利于其适应社会，那么社会实践的经历会促使他进行修正，家长过分的干涉既会影响亲子关系又容易伤害孩子的自尊、自信。",
            "家庭教育方式优良。|【沟通程度】家长与和孩子之间的交流与沟通关系非常好。孩子可以敞开心扉与你谈知心话。|【自由程度】家长充分给予孩子自由做主的空间。|【和蔼程度】家长教育孩子有足够的耐心。|【独立程度】家长允许孩子独立处理问题。|【放手程度】家长对孩子生活、学习、行为等各个方面给予放手处理机会。|【平等程度】家长充分尊重孩子，不会将自己置于相对权威的地位，绝大多数时间都顾及孩子的感受。|【爱护程度】家长对孩子喜欢，孩子在身边表现出心情好、很开心。|【关怀程度】家长对孩子的生活、学习、行为等的很关注。|【鼓励程度】家长对孩子的优点能及时给予鼓励。|【亲密程度】家长与孩子之间信任，并且关系很亲密。|【责任程度】家长对孩子未来行为发展有目标。|【期待程度】家长对孩子将来的发展寄予希望。|【管理程度】家长对孩子的言行、举止能做到及时指导。|【照顾程度】家长对孩子生活照顾周到。|家教指导建议：|被测试者的家庭教育方式优良。被测者与孩子之间的交流与沟通关系非常好，孩子可以敞开心扉与你谈知心话。同时对孩子很好耐心。很不错的家庭教育方式，下面您这些好的教育方式希望可以与其他家长共享：|1、对待孩子有耐心。孩子其实就是父母的影子，你以怎样的态度对她，这种态度也会潜移默化成孩子的性格的一部分。家长都不希望自己的孩子成为一个没有耐心的、甚至是粗暴的人。|2、你与孩子平等沟通。你变成孩子的朋友，真正的去了解他（她），读懂他（她）。只有真正了解孩子，了解孩子的需要和想法，才能有的放矢地给予孩子引导和教育。|3、对孩子的言行、举止能做到及时指导。孩子经常通过家长对自己的行为做出的反应来判断‘什么是可以做的’、‘什么是不可以做的’。家长通过对孩子言行举止的及时掌握，发现他们身上存在的问题，以做到及时教育，及时纠偏，不让问题过夜，培养孩子好的行为习惯。",
            "家庭教育方式非常科学|【 沟通程度 】 家长与和孩子之间的交流与沟通关系非常棒 ， 孩子有什么都会和家长及时沟通 。 |【自由程度】家长高度给予孩子做主的空间。|【和蔼程度】家长教育孩子非常有耐心。|【独立程度】家长在培养孩子独立处理问题能力上，很有方法。|【放手程度】家长对孩子生活、学习、行为等各个方面给予放手处理机会。|【平等程度】家长充分尊重孩子，不会将自己置于相对权威的地位，时刻能顾及孩子的感受。|【爱护程度】家长对孩子非常喜欢，孩子在身边表现出心情好、很开心。|【关怀程度】家长对孩子的生活、学习、行为等的非常关注，并能及时给予指点。|【鼓励程度】家长能及时看到孩子身上的优点及长处，并能及时给予鼓励。|【亲密程度】家长与孩子之间非常信任，并且关系很亲密。|【责任程度】家长对孩子未来行为发展目标明确。|【期待程度】家长对孩子将来的发展寄予很深期望。|【管理程度】家长对孩子的言行、举止能做到及时指导。|【照顾程度】家长对孩子生活照顾非常周到。|家教指导建议：|被测试者的家庭教育方式非常优秀而且科学。被测者与孩子之间的交流与沟通关系非常棒，孩子有什么都会和家长及时沟通。同时对孩子非常有耐心，家长对孩子生活、学习、行为等各个方面给予放手处理机会。非常棒的家庭教育方式，下面您这些好的教育方式希望可以与其他家长共享：|1、对待孩子有耐心。孩子其实就是父母的影子，你以怎样的态度对她，这种态度也会潜移默化成孩子的性格的一部分。家长都不希望自己的孩子成为一个没有耐心的、甚至是粗暴的人。|2、你与孩子平等沟通。你变成孩子的朋友，真正的去了解他（她），读懂他（她）。只有真正了解孩子，了解孩子的需要和想法，才能有的放矢地给予孩子引导和教育。|3、对孩子的言行、举止能做到及时指导。孩子经常通过家长对自己的行为做出的反应来判断‘什么是可以做的’、‘什么是不可以做的’。家长通过对孩子言行举止的及时掌握，发现他们身上存在的问题，以做到及时教育，及时纠偏，不让问题过夜，培养孩子好的行为习惯。"
        )
        val Tcount = Zcount + Zgtcount + Zzycount + Zhacount + Zdlcount + Zfscount + Zpdcount + Zahcount + Zghcount + Zglcount + Zqmcount + Zzrcount + Zqdcount + Zgllcount + Zzgcount

        val yj = StringBuffer("您的测试结果如下：|${Tcount}分，")
            .append(
                when {
                    Tcount < -1 -> jieguolist[0]
                    Tcount >= -1 && Tcount < 0 -> jieguolist[1]
                    Tcount in 0..0 -> jieguolist[2]
                    Tcount in 1..1 -> jieguolist[3]
                    Tcount == 2 -> jieguolist[4]
                    else -> ""
                }
            ).toString()

        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "总分|沟通程度|自由程度|和蔼程度|独立程度|放手程度|平等程度|爱护程度|关怀程度|鼓励程度|亲密程度|责任程度|期待程度|管理程度|照顾程度",
            BZF = mutableListOf(
                Zcount,
                Zgtcount,
                Zzycount,
                Zhacount,
                Zdlcount,
                Zfscount,
                Zpdcount,
                Zahcount,
                Zghcount,
                Zglcount,
                Zqmcount,
                Zzrcount,
                Zqdcount,
                Zgllcount,
                Zzgcount
            ).joinToString("|"),
            JF = "",
            BZC = "",
            YJ = yj,
            XX = answers.joinToString("|"),
        )
    }

    private fun getValue(index: Int, flist: MutableList<Int>, value: Int): Int {
        return if (index !in flist) {
            if (value == 1) -2 else if (value == 2) -1 else if (value == 3) 0 else if (value == 4) 1 else 2
        } else {
            if (value == 1) 2 else if (value == 2) 1 else if (value == 3) 0 else if (value == 4) -1 else -2
        }
    }
}
