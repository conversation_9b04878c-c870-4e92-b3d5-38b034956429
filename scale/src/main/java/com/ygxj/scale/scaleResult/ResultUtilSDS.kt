package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils
import kotlin.math.roundToInt

//抑郁自评量表(SDS)
class ResultUtilSDS {

    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        var count = 0
        val fxlist = mutableListOf(2, 5, 6, 11, 12, 14, 16, 17, 18, 20)//反向计分
        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            val value = answers[index].toInt() + 1
            count += if (index in fxlist) QuizUtils.score(value, 4) else value
        }
        val bzf = (count * 1.25f).roundToInt()
        val yj = StringBuffer()
            .append("您本次的得分为${bzf}分,")
            .append(
                when (bzf) {
                    in 0..52 -> "无抑郁。|结果释义：|该测试者具有良好的心态，能够以积极的态度接受新的环境或者新的人际关系，适应性良好。困扰较少。面对重大生活事件，也能够积极接受，并寻求合理的解决方法，复原力良好。"
                    in 53..61 -> "轻度抑郁。|分值越高，抑郁症状越重。|结果释义：|该测试者有轻微抑郁的心态，在接受新的环境和新的人际关系上可能会出现退缩等情况，适应性较差。平时思虑较多，可能对于某些小问题，自己比较钻牛角尖，徒感悲伤。面对重大生活事件，复原力欠佳，可能需要外界的引导和帮助，从事件中走出来。"
                    in 63..72 -> "中度抑郁。|分值越高，抑郁症状越重。|结果释义：|该测试者有抑郁的心态，面对新的环境和新的人际关系，适应性不佳，可能采取回避的态度。平时忧虑较多，经常以负面的视角看待事物，并且难以自拔。对于重大生活事件，复原力较差，可能使其感受到巨大的压力和悲伤，需要外界专业人士主动干预，防止抑郁情绪加深。"
                    in 73..Int.MAX_VALUE -> "重度抑郁。|分值越高，抑郁症状越重。|结果释义：|该测试者有较多的抑郁心态，适应性不佳，对于新的环境和人际关系可能感到恐惧和回避。困扰多来自于自己长期负面、弥漫性的想法，徒感悲伤。重大生活事件，学生的复原力差，有可能成为压倒骆驼的最后一根稻草，所以需要专业精神卫生者主动干预。"
                    else -> ""
                }
            ).toString()
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "粗分|标准分",
            BZF = "$count|$bzf",
            JF = "",
            BZC = "",
            YJ = yj,
            XX = answers.joinToString("|"),
        )
    }
}