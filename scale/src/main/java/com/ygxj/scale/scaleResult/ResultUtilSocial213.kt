package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils

class ResultUtilSocial213 {
    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        val zx = mutableListOf(1, 3, 5, 7, 9, 12, 14, 16, 18, 20, 22, 24, 27, 29)
        val fx = mutableListOf(2, 4, 6, 8, 10, 11, 13, 15, 17, 19, 21, 23, 25, 26, 28, 30)
        var zxcount = 0
        var fxcount = 0
        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            val value = answers[index].toInt() + 1
            if (zx.contains(index)) {
                zxcount += value - 1
            }
            if (fx.contains(index)) {
                fxcount += QuizUtils.score(value, 5) - 1
            }
        }
        val count = zxcount + fxcount
        val yj =
            if (count >= 101) "被测者适应性很强。" else if (count in 81..100) "被测者适应性较强。" else if (count in 61..80) "被测者适应性一般。" else if (count in 31..60) "被测者适应性较差。" else "被测者适应性很差。"
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "总分",
            BZF = "$count",
            JF = "",
            BZC = "",
            YJ = yj,
            cf = "",
            XX = answers.joinToString("|")
        )
    }
}