package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils
import kotlin.math.roundToInt

class ResultUtilSPM {

    //瑞文标准推理测验(SPM)
    fun getResult(scaleId: Int, answers: List<String>, costTime: String, age: Float): ScaleResultEntity {
        val data = mutableListOf(
            4, 5, 1, 2, 6, 3, 6, 2, 1, 3, 4, 5, 2, 6, 1, 2, 1, 3, 5, 6, 4, 3, 4, 4, 8, 2, 3, 8, 7, 4, 5, 1, 7,
            6, 1, 2, 3, 4, 3, 7, 8, 6, 5, 4, 1, 2, 5, 6, 7, 6, 8, 2, 1, 5, 1, 6, 3, 2, 4, 5
        )
        var count = 0
        var z1 = 0
        var z2 = 0
        var z3 = 0
        var z4 = 0
        var z5 = 0
        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            val value = answers[index].toInt() + 1
            if (value == data[index]) {
                when (index) {
                    in 0..11 -> z1++
                    in 12..23 -> z2++
                    in 24..35 -> z3++
                    in 36..47 -> z4++
                    in 48..59 -> z5++
                }
            }
            count += if (value == data[index]) 1 else 0
        }
        val bzf = ZL(age, count)//智力商数赋固定值,登录时重新获得年龄
        val level = Level(age, count)//级别赋固定值,登录时重新获得年龄
        val lev = when (level) {
            in 97.8f..Float.MAX_VALUE -> "一级"
            in 91.1f..97.8f -> "二级"
            in 75.0f..91.1f -> "三级"
            in 50.0f..75.0f -> "四级"
            in 25.0f..50.0f -> "五级"
            in 8.9f..25.0f -> "六级"
            in 2.2f..8.9f -> "七级"
            else -> "七级"
        }
        val yj = StringBuffer()
            .append("【正确题目】：")
            .append(z1 + z2 + z3 + z4 + z5)
            .append("|【   IQ   】：")
            .append(bzf.roundToInt())
            .append("|【百分等级】：")
            .append(level.roundToInt())
            .append("|【智力等级】：")
            .append(
                when (bzf) {
                    in 130f..Float.MAX_VALUE -> "超优"
                    in 120f..130f -> "优秀"
                    in 110f..120f -> "中上（聪明）"
                    in 90f..110f -> "中等"
                    in 80f..90f -> "中下（迟钝）"
                    in 70f..80f -> "低能边缘"
                    in 0f..70f -> "缺陷"
                    else -> ""
                }
            )
            .append("|【级    别】：")
            .append(lev)
            .toString()

        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "第一组|第二组|第三组|第四组|第五组",
            BZF = "$z1|$z2|$z3|$z4|$z5",
            JF = "",
            BZC = "",
            YJ = yj,
            XX = answers.joinToString("|"),
        )
    }

    val dt = mutableListOf<HashMap<String, Float>>()
    private fun ZL(age: Float, value: Int): Float {
        dt.clear()
        addRowZL(
            -1f, 14f, 15f, 16f, 17f, 18f, 19f, 20f, 21f, 22f, 23f, 24f, 25f, 26f, 27f, 28f, 29f, 30f, 31f, 32f,
            33f, 34f, 35f, 36f, 37f, 38f, 39f, 40f, 41f, 42f, 43f, 44f, 45f, 46f, 47f, 48f, 49f, 50f, 51f, 52f, 53f,
            54f, 55f, 56f, 57f, 58f, 59f, 60f, 9f, 10f, 11f, 12f, 13f
        )
        //60~64
        addRowZL(
            60f, 72f, 72f, 72f, 72f, 72f, 72f, 74f, 75f, 77.5f, 78f, 79f, 80f, 81f, 82f, 82.5f, 84f, 85f, 87f, 88f,
            89f, 90f, 93f, 96f, 100f, 102f, 104f, 106f, 108f, 109f, 111f, 112f, 113f, 115f, 118f, 120f, 122f, 122.5f,
            124f, 125f, 129f, 130f, 131f, 133f, 134f, 135f, 135f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //55~59
        addRowZL(
            55f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 74f, 75f, 77.5f, 78f, 79f, 80f, 81f, 81f, 82.5f, 83f, 84f, 85f,
            86f, 87f, 89f, 90f, 91f, 94f, 97f, 100f, 103f, 105f, 108f, 109f, 111f, 112.5f, 115f, 118f, 121f, 122f,
            123f, 125f, 129f, 130f, 131f, 133f, 134f, 135f, 135f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //50~54
        addRowZL(
            50f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 74f, 75f, 77.5f, 78f, 79f, 80f, 80f, 81f, 82f, 82.5f, 83f,
            84f, 85f, 86f, 87f, 89f, 90f, 91f, 94f, 97f, 100f, 103f, 106f, 108f, 111f, 113f, 115f, 119f, 122f, 123f,
            125f, 129f, 130f, 131f, 132f, 133f, 135f, 135f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //45~49
        addRowZL(
            45f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 74f, 75f, 77.5f, 78f, 79f, 80f, 80f, 81f,
            82f, 82.5f, 83f, 84f, 85f, 87f, 88f, 89f, 90f, 93f, 96f, 100f, 104f, 109f, 112f, 115f, 119f, 122f, 123f, 125f,
            128f, 129f, 130f, 131f, 132f, 133f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //40~44
        addRowZL(
            40f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 74f, 75f, 77.5f, 78f, 79f,
            80f, 80f, 81f, 82f, 82.5f, 83f, 84f, 85f, 87f, 88f, 89f, 92f, 96f, 100f, 106f, 111f, 115f, 119f, 122f, 124f,
            125f, 128f, 129f, 130f, 132f, 134f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //35~39
        addRowZL(
            35f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 73f, 74f, 74f, 75f, 75f,
            77.5f, 78f, 79f, 80f, 81f, 81f, 82.5f, 83f, 84f, 85f, 87f, 89f, 90f, 95f, 100f, 106f, 110f, 114f, 119f,
            122f, 123f, 125f, 129f, 130f, 131f, 133f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //30~34
        addRowZL(
            30f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 73f, 73f, 74f, 74f, 75f,
            75f, 75f, 75f, 77.5f, 78f, 79f, 80f, 82f, 82.5f, 84f, 85f, 87f, 90f, 94f, 100f, 104f, 109f, 112f, 115f,
            120f, 122.5f, 125f, 129f, 130f, 131f, 133f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //25~29
        addRowZL(
            25f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 73f, 73f, 74f, 74f, 75f,
            75f, 75f, 75f, 77.5f, 78f, 79f, 80f, 82f, 82.5f, 84f, 85f, 87f, 90f, 94f, 100f, 104f, 109f, 112f, 115f,
            120f, 122.5f, 125f, 129f, 130f, 131f, 133f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //20~24
        addRowZL(
            20f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f,
            72f, 74f, 75f, 75f, 77.5f, 79f, 80f, 81f, 82.5f, 84f, 85f, 87f, 88f, 89f, 92f, 96f, 100f, 104f, 109f, 112f,
            115f, 122f, 125f, 130f, 130f, 132f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //18.5
        addRowZL(
            18.5f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f,
            72f, 74f, 75f, 75f, 77.5f, 79f, 80f, 81f, 82.5f, 84f, 85f, 87f, 88f, 89f, 92f, 96f, 100f, 104f, 109f, 112f,
            115f, 122f, 125f, 130f, 131f, 132f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //17.5
        addRowZL(
            17.5f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f,
            72f, 72f, 72f, 72f, 72f, 74f, 75f, 77.5f, 78f, 79f, 80f, 82f, 82.5f, 84f, 85f, 87f, 89f, 90f, 95f, 100f,
            106f, 111f, 115f, 122f, 125f, 130f, 132f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //17
        addRowZL(
            17f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f,
            72f, 72f, 72f, 72f, 74f, 75f, 77.5f, 78f, 79f, 80f, 82f, 82.5f, 84f, 85f, 87f, 89f, 90f, 95f, 100f, 106f,
            111f, 115f, 122f, 125f, 130f, 135f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //16.5
        addRowZL(
            16.5f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f,
            72f, 72f, 72f, 72f, 73f, 75f, 75f, 77.5f, 79f, 81f, 82.5f, 85f, 87f, 90f, 94f, 100f, 104f, 109f, 112f, 115f,
            120f, 122.5f, 125f, 130f, 130f, 132f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //16
        addRowZL(
            16f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f,
            72f, 72f, 72f, 73f, 74f, 75f, 75f, 77.5f, 80f, 82.5f, 85f, 87f, 89f, 90f, 95f, 100f, 104f, 109f, 112f, 115f,
            120f, 122.5f, 125f, 130f, 132f, 133f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //15.5
        addRowZL(
            15.5f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f,
            72f, 73f, 73f, 74f, 75f, 75f, 75f, 77.5f, 81f, 85f, 87f, 88f, 89f, 92f, 96f, 100f, 106f, 111f, 115f, 120f,
            122.5f, 125f, 129f, 130f, 132f, 134f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //15
        addRowZL(
            15f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f,
            75f, 77.5f, 78f, 79f, 80f, 82f, 82.5f, 84f, 85f, 87f, 89f, 90f, 95f, 100f, 106f, 111f, 115f, 120f, 122.5f, 125f,
            128f, 129f, 130f, 132f, 134f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //14.5
        addRowZL(
            14.5f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f,
            72f, 75f, 77.5f, 78f, 79f, 80f, 82f, 82.5f, 84f, 85f, 87f, 89f, 90f, 95f, 100f, 106f, 111f, 115f, 122f, 125f,
            129f, 130f, 130f, 131f, 132f, 135f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //14
        addRowZL(
            14f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f,
            72f, 75f, 77.5f, 78f, 79f, 80f, 82f, 82.5f, 84f, 85f, 87f, 89f, 90f, 95f, 100f, 109f, 115f, 122f, 125f,
            128f, 129f, 130f, 130f, 131f, 132f, 135f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //13.5
        addRowZL(
            13.5f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f,
            74f, 75f, 77.5f, 78f, 79f, 80f, 82f, 82.5f, 84f, 85f, 87f, 90f, 94f, 100f, 104f, 109f, 112f, 115f, 122f,
            125f, 129f, 130f, 130f, 131f, 132f, 135f, 135f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //13
        addRowZL(
            13f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 73f, 74f, 75f,
            75f, 77.5f, 79f, 80f, 82f, 83f, 85f, 87f, 89f, 90f, 95f, 100f, 103.5f, 107f, 110f, 112.5f, 115f, 122f, 125f,
            130f, 130f, 131f, 132f, 135f, 135f, 135f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //12.5
        addRowZL(
            12.5f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 73f, 74f, 75f, 75f,
            77.5f, 78f, 79f, 80f, 82f, 82.5f, 84f, 85f, 87f, 89f, 90f, 95f, 100f, 103.5f, 107f, 110f, 112.5f, 115f, 122f,
            125f, 130f, 130f, 131f, 132f, 135f, 135f, 135f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //12
        addRowZL(
            12f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 72f, 73f, 74f, 74f, 75f, 75f, 77.5f, 78f, 79f, 80f, 80f,
            81f, 82f, 82.5f, 83f, 84f, 85f, 87f, 89f, 90f, 95f, 100f, 104f, 109f, 112f, 115f, 119f, 122f, 123f,
            125f, 128f, 129f, 130f, 130f, 131f, 132f, 135f, 135f, 135f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //11.5
        addRowZL(
            11.5f, 72f, 72f, 72f, 72f, 72f, 72f, 73f, 74f, 74f, 75f, 75f, 77.5f, 78f, 79f, 80f, 80f, 81f, 82f, 82.5f,
            83f, 84f, 85f, 87f, 88f, 89f, 90f, 93f, 96f, 100f, 106f, 111f, 115f, 118f, 121f, 122f, 123f, 125f, 128f,
            129f, 130f, 130f, 131f, 132f, 135f, 135f, 135f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //11
        addRowZL(
            11f, 72f, 72f, 72f, 72f, 72f, 72f, 73f, 74f, 74f, 75f, 75f, 77.5f, 78f, 79f, 80f, 81f, 82f, 82.5f, 84f,
            85f, 87f, 88f, 90f, 92f, 96f, 100f, 104f, 109f, 112f, 115f, 117f, 119f, 121f, 122f, 123f, 124f, 125f, 129f,
            130f, 130f, 131f, 132f, 135f, 135f, 135f, 135f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //10.5
        addRowZL(
            10.5f, 72f, 72f, 72f, 72f, 72f, 73f, 73f, 74f, 75f, 75f, 75f, 77.5f, 78f, 79f, 80f, 82f, 82.5f, 84f,
            85f, 87f, 88f, 89f, 90f, 93f, 96f, 100f, 106f, 111f, 115f, 117f, 119f, 121f, 122f, 123f, 124f, 125f, 130f,
            131f, 132f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //10
        addRowZL(
            10f, 73f, 75f, 75f, 77.5f, 78f, 79f, 80f, 80f, 81f, 82f, 82.5f, 83f, 84f, 85f, 86f, 87f, 89f, 91f, 94f, 97f,
            100f, 103f, 105f, 108f, 109f, 111f, 113f, 115f, 118f, 120f, 122f, 122.5f, 124f, 125f, 129f, 130f, 130f,
            131f, 132f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //9.5
        addRowZL(
            9.5f, 73f, 75f, 75f, 77.5f, 78f, 79f, 80f, 80f, 81f, 82f, 82.5f, 83f, 84f, 85f, 86f, 87f, 89f, 91f, 94f,
            97f, 100f, 104f, 109f, 112f, 115f, 117f, 119f, 121f, 122f, 122f, 123f, 124f, 125f, 128f, 129f, 130f, 130f,
            131f, 132f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 72f, 72f, 72f, 72f, 72f
        )
        //9
        addRowZL(
            9f, 77.5f, 78f, 79f, 79f, 80f, 81f, 81f, 82.5f, 82.5f, 83f, 84f, 85f, 86f, 87f, 89f, 90f, 91f, 94f, 97f,
            100f, 104f, 109f, 112f, 115f, 118f, 120f, 122f, 122.5f, 124f, 125f, 126f, 129f, 129f, 130f, 130f, 131f,
            132f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 72f, 72f, 72f, 72f, 75f
        )
        //8.5
        addRowZL(
            8.5f, 77.5f, 79f, 80f, 81f, 82.5f, 84f, 85f, 86f, 87f, 88f, 89f, 90f, 92f, 94f, 97f, 100f, 104f, 109f,
            112f, 114f, 117f, 119f, 121f, 122f, 123f, 124f, 125f, 126f, 128f, 129f, 129f, 130f, 130f, 131f, 132f, 135f,
            135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 72f, 72f, 72f, 72f, 75f
        )
        //8
        addRowZL(
            8f, 81f, 85f, 86f, 87f, 89f, 90f, 91f, 94f, 97f, 100f, 102f, 104f, 106f, 109f, 110f, 112f, 113f, 115f,
            117f, 119f, 121f, 122f, 122f, 123f, 124f, 125f, 126f, 128f, 129f, 129f, 130f, 130f, 131f, 132f, 135f, 135f, 135f, 135f,
            135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 72f, 72f, 73.5f, 75f, 77.5f
        )
        //7.5
        addRowZL(
            7.5f, 86f, 87f, 89f, 90f, 91f, 94f, 97f, 100f, 102f, 103.5f, 106f, 107f, 109f, 110f, 111f, 112.5f, 114f,
            115f, 117f, 119f, 121f, 122f, 123f, 124f, 125f, 126f, 128f, 128f, 129f, 129f, 130f, 130f, 131f, 132f, 135f,
            135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 72f, 72f, 75f, 77.5f, 85f
        )
        //7
        addRowZL(
            7f, 87f, 88f, 89f, 92f, 96f, 100f, 103f, 106f, 109f, 111f, 113f, 115f, 116f, 118f, 119f, 120f, 121f, 122f,
            122.5f, 123f, 123f, 124f, 125f, 126f, 126f, 127.5f, 129f, 129f, 130f, 130f, 131f, 132f, 135f, 135f, 135f,
            135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 72f, 72f, 75f, 77.5f, 85f
        )
        //6.5
        addRowZL(
            6.5f, 87f, 89f, 90f, 95f, 100f, 103f, 105f, 108f, 109f, 111f, 113f, 115f, 118f, 120f, 122f, 122.5f, 124f,
            125f, 126f, 128f, 129f, 129f, 130f, 130f, 131f, 132f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f,
            135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 72f, 72f, 75f, 77.5f, 85f
        )
        //6
        addRowZL(
            6f, 87f, 90f, 94f, 100f, 102f, 104f, 106f, 109f, 111f, 112f, 113f, 115f, 118f, 120f, 122f, 122.5f, 124f, 125f,
            126f, 128f, 129f, 129f, 130f, 130f, 131f, 132f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f,
            135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 72f, 73.5f, 75f, 77.5f, 85f
        )
        //5.5
        addRowZL(
            5.5f, 88f, 92f, 100f, 102f, 104f, 106f, 108f, 109f, 111f, 112f, 113f, 115f, 119f, 122f, 123f, 125f, 126f,
            128f, 129f, 129f, 130f, 130f, 132f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f,
            135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 135f, 72f, 73.5f, 75f, 77.5f, 85f
        )

        var res = 0f
        for (item in dt) {
            var minAge = 0f
            var maxAge = 0f
            item["age"]?.let {
                when (it) {
                    17.5f -> {
                        minAge = 17.5f
                        maxAge = 18.5f
                    }
                    18.5f -> {
                        minAge = 18.5f
                        maxAge = 20f
                    }
                    in 20f..Float.MAX_VALUE -> {
                        minAge = it
                        maxAge = minAge + 5f
                    }
                    else -> {
                        minAge = it
                        maxAge = minAge + 0.5f
                    }
                }

                if (minAge <= age && age < maxAge) {
                    for ((keys, data) in item) {
                        val v = dt[0][keys]
                        if (value < 14) {
                            res = item["a1"]!!
                            break
                        } else if (value > 72) {
                            res = item["a59"]!!
                            break
                        } else {
                            if (v == value.toFloat()) {
                                res = data
                                break
                            }
                        }
                    }
                }
            }
            if (minAge <= age && age < maxAge) {
                break
            }
        }
        return res
    }

    private fun Level(age: Float, value: Int): Float {
        dt.clear()
        addRowZL(
            -1f, 14f, 15f, 16f, 17f, 18f, 19f, 20f, 21f, 22f, 23f, 24f, 25f, 26f, 27f, 28f, 29f, 30f, 31f, 32f, 33f,
            34f, 35f, 36f, 37f, 38f, 39f, 40f, 41f, 42f, 43f, 44f, 45f, 46f, 47f, 48f, 49f, 50f, 51f, 52f, 53f, 54f,
            55f, 56f, 57f, 58f, 59f, 60f, 9f, 10f, 11f, 12f, 13f
        )
        //60~64
        addRowZL(
            60f, 5f, 5f, 5f, 5f, 5f, 5f, 7f, 8f, 10f, 12f, 14f, 16f, 17.5f, 19f, 21f, 23f, 25f, 29f, 32f, 36f, 39f,
            43f, 46f, 50f, 53f, 56f, 58f, 61f, 64f, 67f, 69f, 72f, 75f, 77.5f, 80f, 82.5f, 85f, 87.5f, 90f, 92.5f,
            95f, 96f, 96f, 97f, 97f, 98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //55~59
        addRowZL(
            55f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 7f, 8f, 10f, 12f, 13f, 15f, 17f, 18f, 20f, 22f, 23f, 25f, 28f, 31f, 34f,
            37.5f, 41f, 44f, 47f, 50f, 54f, 57f, 61f, 64f, 68f, 71f, 75f, 78f, 81f, 84f, 87f, 90f, 92.5f, 95f, 96f,
            96f, 97f, 97f, 98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //50~54
        addRowZL(
            50f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 7f, 8f, 10f, 11.5f, 13f, 14.5f, 16f, 17.5f, 19f, 20.5f, 22f, 23.5f,
            25f, 28f, 31f, 34f, 37.5f, 41f, 44f, 47f, 50f, 54f, 58f, 62f, 67f, 71f, 75f, 79f, 82.5f, 86f, 90f, 92.5f,
            95f, 96f, 96f, 97f, 97f, 98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //45~49
        addRowZL(
            45f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 7f, 8f, 10f, 11.5f, 13f, 14.5f, 16f, 17.5f, 19f, 20.5f,
            22f, 23.5f, 25f, 29f, 32f, 36f, 39f, 43f, 46f, 50f, 56f, 62.5f, 69f, 75f, 79f, 82.5f, 86f, 90f, 92f, 93f,
            95f, 96f, 97f, 98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //40~44
        addRowZL(
            40f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 7f, 8f, 10f, 11.5f, 13f, 14.5f, 16f, 17.5f,
            19f, 20.5f, 22f, 23.5f, 25f, 29f, 33f, 37f, 42f, 46f, 50f, 58f, 67f, 75f, 79f, 82.5f, 86f, 90f, 92f, 93f, 95f,
            96f, 97f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //35~39
        addRowZL(
            35f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 6f, 7f, 7f, 8f, 9f, 10f, 12f, 13f, 15f, 17f,
            18f, 20f, 22f, 23f, 25f, 30f, 35f, 40f, 45f, 50f, 58f, 66f, 75f, 79f, 82.5f, 86f, 90f, 92.5f, 95f,
            96f, 97f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //30~34
        addRowZL(
            30f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 6f, 6f, 7f, 7f, 8f, 8f, 9f, 9f, 10f, 12f,
            14f, 16f, 19f, 21f, 23f, 25f, 31f, 37.5f, 44f, 50f, 56f, 62.5f, 69f, 75f, 80f, 85f, 90f, 92.5f, 95f, 96f,
            97f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //25~29
        addRowZL(
            25f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 6f, 6f, 7f, 7f, 8f, 8f, 9f, 9f, 10f, 12f,
            14f, 16f, 19f, 21f, 23f, 25f, 31f, 37.5f, 44f, 50f, 56f, 62.5f, 69f, 75f, 80f, 85f, 90f, 92.5f, 95f, 96f,
            97f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //20~24
        addRowZL(
            20f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 6f, 7f, 8f, 9f, 10f, 12.5f,
            15f, 17.5f, 20f, 22.5f, 25f, 29f, 33f, 37f, 42f, 46f, 50f, 56f, 62.5f, 69f, 75f, 82.5f, 90f, 95f, 96f, 97f,
            98f, 5f, 5f, 5f, 5f, 5f
        )
        //18.5
        addRowZL(
            18.5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 6f, 7f, 8f, 9f, 10f,
            12.5f, 15f, 17.5f, 20f, 22.5f, 25f, 29f, 33f, 37f, 42f, 46f, 50f, 56f, 62.5f, 69f, 75f, 82.5f, 90f, 95f,
            96f, 97f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //17.5
        addRowZL(
            17.5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 7f,
            8f, 10f, 12f, 14f, 16f, 19f, 21f, 23f, 25f, 30f, 35f, 40f, 45f, 50f, 58f, 67f, 75f, 82.5f, 90f, 95f, 97f, 98f,
            5f, 5f, 5f, 5f, 5f
        )
        //17
        addRowZL(
            17f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 7f,
            8f, 10f, 12f, 14f, 16f, 19f, 21f, 23f, 25f, 30f, 35f, 40f, 45f, 50f, 58f, 67f, 75f, 82.5f, 90f, 93f, 96f,
            98f, 5f, 5f, 5f, 5f, 5f
        )
        //16.5
        addRowZL(
            16.5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f,
            6f, 7.5f, 9f, 10f, 14f, 17.5f, 21f, 25f, 31f, 37.5f, 44f, 50f, 56f, 62.5f, 69f, 75f, 80f, 85f, 90f, 92f, 94f,
            96f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //16
        addRowZL(
            16f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 6f, 7f,
            8f, 9f, 10f, 15f, 20f, 25f, 30f, 35f, 40f, 45f, 50f, 56f, 62.5f, 69f, 75f, 80f, 85f, 90f, 92f, 94f, 96f,
            98f, 5f, 5f, 5f, 5f, 5f
        )
        //15.5
        addRowZL(
            15.5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 6f, 6f, 7f,
            8f, 9f, 9f, 10f, 17.5f, 25f, 29f, 33f, 37f, 42f, 46f, 50f, 58f, 67f, 75f, 80f, 85f, 90f, 92f, 94f, 96f,
            98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //15
        addRowZL(
            15f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 7.5f, 10f, 12f,
            14f, 16f, 19f, 21f, 23f, 25f, 30f, 35f, 40f, 45f, 50f, 58f, 67f, 75f, 80f, 85f, 90f, 95f, 93f, 95f, 97f,
            98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //14.5
        addRowZL(
            14.5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 7.5f, 10f, 12f,
            14f, 16f, 19f, 21f, 23f, 25f, 30f, 35f, 40f, 45f, 50f, 58f, 67f, 75f, 82.5f, 90f, 92.5f, 95f, 96f, 97f, 98f,
            98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //14
        addRowZL(
            14f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 7.5f, 10f, 12f,
            14f, 16f, 19f, 21f, 23f, 25f, 30f, 35f, 40f, 45f, 50f, 63f, 75f, 82.5f, 90f, 92f, 93f, 95f, 96f, 97f, 98f,
            98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //13.5
        addRowZL(
            13.5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 7f, 9f, 10f, 12f, 14f,
            16f, 19f, 21f, 23f, 25f, 31f, 37.5f, 44f, 50f, 56f, 62.5f, 69f, 75f, 82.5f, 90f, 92.5f, 95f, 96f, 97f,
            98f, 98f, 98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //13
        addRowZL(
            13f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 6f, 7f, 8f, 9f, 10f, 13f, 16f,
            19f, 22f, 25f, 30f, 35f, 40f, 45f, 50f, 55f, 60f, 65f, 70f, 75f, 82.5f, 90f, 95f, 96f, 97f, 98f, 98f, 98f,
            98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //12.5
        addRowZL(
            12.5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 6f, 7f, 8f, 9f, 10f, 12f, 14f, 16f, 19f,
            21f, 23f, 25f, 30f, 35f, 40f, 45f, 50f, 55f, 60f, 65f, 70f, 75f, 83f, 90f, 95f, 96f, 97f, 98f, 98f, 98f,
            98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //12
        addRowZL(
            12f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f, 6f, 7f, 7f, 8f, 9f, 10f, 11.5f, 13f, 14.5f, 16f, 17.5f, 19f, 20.5f, 22f,
            23.5f, 25f, 30f, 35f, 40f, 45f, 50f, 56f, 62.5f, 69f, 75f, 79f, 82.5f, 86f, 90f, 92f, 93f, 95f, 96f, 97f,
            98f, 98f, 98f, 98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //11.5
        addRowZL(
            11.5f, 5f, 5f, 5f, 5f, 5f, 5f, 6f, 7f, 7f, 8f, 9f, 10f, 11.5f, 13f, 14.5f, 16f, 17.5f, 19f, 20.5f, 22f, 23.5f,
            25f, 29f, 32f, 36f, 39f, 43f, 46f, 50f, 58f, 67f, 75f, 78f, 81f, 84f, 97f, 90f, 92f, 93f, 95f, 96f, 97f, 98f,
            98f, 98f, 98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //11
        addRowZL(
            11f, 5f, 5f, 5f, 5f, 5f, 5f, 6f, 7f, 7f, 8f, 9f, 10f, 12f, 14f, 16f, 17.5f, 19f, 21f, 23f, 25f, 29f, 33f, 37.5f,
            42f, 46f, 50f, 56f, 62.5f, 69f, 75f, 77f, 79f, 81f, 84f, 86f, 88f, 90f, 92.5f, 95f, 96f, 97f, 98f, 98f, 98f,
            98f, 98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //10.5
        addRowZL(
            10.5f, 5f, 5f, 5f, 5f, 5f, 6f, 6f, 7f, 8f, 9f, 9f, 10f, 12f, 14f, 16f, 19f, 21f, 23f, 25f, 29f, 32f, 36f,
            39f, 43f, 46f, 50f, 58f, 67f, 75f, 77f, 79f, 81f, 84f, 86f, 88f, 90f, 95f, 96f, 97f, 98f, 98f, 98f, 98f,
            98f, 98f, 98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //10
        addRowZL(
            10f, 6f, 7.5f, 9f, 10f, 11.5f, 13f, 14.5f, 16f, 17.5f, 19f, 20.5f, 22f, 23.5f, 25f, 28f, 31f, 34f, 37.5f, 41f,
            44f, 47f, 50f, 54f, 57f, 61f, 64f, 68f, 71f, 75f, 77.5f, 80f, 82.5f, 85f, 87.5f, 90f, 92.5f, 95f, 96f, 97f,
            98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //9.5
        addRowZL(
            9.5f, 6f, 7.5f, 9f, 10f, 11.5f, 13f, 14.5f, 16f, 17.5f, 19f, 20.5f, 22f, 23.5f, 25f, 28f, 31f, 34f, 37.5f,
            41f, 44f, 47f, 50f, 56f, 62.5f, 69f, 75f, 77f, 79f, 81f, 82.5f, 84f, 86f, 88f, 90f, 92f, 93f, 95f, 96f, 97f,
            98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 5f, 5f, 5f, 5f, 5f
        )
        //9
        addRowZL(
            9f, 10f, 11f, 13f, 14f, 15f, 17f, 18f, 20f, 21f, 22f, 24f, 25f, 28f, 31f, 34f, 37.5f, 41f, 44f, 47f, 50f,
            56f, 62.5f, 69f, 75f, 77.5f, 80f, 82.5f, 85f, 87.5f, 90f, 91f, 93f, 94f, 95f, 96f, 97f, 98f, 98f, 98f, 98f,
            98f, 98f, 98f, 98f, 98f, 98f, 98f, 5f, 5f, 5f, 5f, 7.5f
        )
        //8.5
        addRowZL(
            8.5f, 10f, 12.5f, 15f, 17.5f, 20f, 22.5f, 25f, 28f, 31f, 33f, 36f, 39f, 42f, 44f, 47f, 50f, 56f, 62.5f, 69f, 75f, 77f, 79f, 81f, 84f, 86f, 88f, 90f, 91f, 92f, 93f, 94f, 95f, 96f, 97f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 5f, 5f, 5f, 5f, 7.5f
        )
        //8
        addRowZL(
            8f, 17.5f, 25f, 28f, 31f, 34f, 37.5f, 41f, 44f, 47f, 50f, 53f, 56f, 59f, 62.5f, 66f, 69f, 72f, 75f, 77f, 79f, 81f, 82.5f, 84f, 86f, 88f, 90f, 91f, 92f, 93f, 94f, 95f, 96f, 97f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 5f, 5f, 7f, 8f, 10f
        )
        //7.5
        addRowZL(
            7.5f, 28f, 31f, 34f, 37.5f, 41f, 44f, 47f, 50f, 52.5f, 55f, 57.5f, 60f, 62.5f, 65f, 67.5f, 70f, 72.5f, 75f, 77f, 79f, 81f, 84f, 86f, 88f, 90f, 91f, 92f, 92f, 93f, 94f, 95f, 96f, 97f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 5f, 5f, 7.5f, 10f, 25f
        )
        //7
        addRowZL(
            7f, 29f, 33f, 37f, 42f, 46f, 50f, 54f, 58f, 63f, 67f, 71f, 75f, 76f, 78f, 79f, 80f, 82f, 83f, 85f, 86f, 87f, 89f, 90f, 91f, 91f, 92f, 93f, 94f, 94f, 95f, 96f, 97f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 98f, 5f, 5f, 7.5f, 10f, 25f
        )
        //6.5
        addRowZL(
            6.5f,
            30f,
            35f,
            40f,
            45f,
            50f,
            54f,
            57f,
            61f,
            64f,
            68f,
            72f,
            75f,
            77.5f,
            80f,
            82.5f,
            85f,
            87.5f,
            90f,
            91f,
            92f,
            93f,
            93f,
            94f,
            95f,
            96f,
            97f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            5f,
            5f,
            7.5f,
            10f,
            25f
        )
        //6
        addRowZL(
            6f,
            31f,
            37.5f,
            44f,
            50f,
            53f,
            56f,
            59f,
            62.5f,
            67f,
            69f,
            72f,
            75f,
            77.5f,
            80f,
            82.5f,
            85f,
            87.5f,
            90f,
            91f,
            92f,
            93f,
            94f,
            95f,
            96f,
            97f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            5f,
            7f,
            8f,
            10f,
            25f
        )
        //5.5
        addRowZL(
            5.5f,
            33f,
            42f,
            50f,
            53f,
            56f,
            58f,
            61f,
            64f,
            67f,
            70f,
            72f,
            75f,
            79f,
            82.5f,
            86f,
            90f,
            91f,
            92f,
            93f,
            94f,
            95f,
            95f,
            97f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            98f,
            5f,
            7f,
            8f,
            10f,
            25f
        )

        var res = 0f
        for (item in dt) {
            var minage = 0f
            var maxage = 0f
            item["age"]?.let {
                when (it) {
                    17.5f -> {
                        minage = 17.5f
                        maxage = 18.5f
                    }
                    18.5f -> {
                        minage = 18.5f
                        maxage = 20f
                    }
                    in 20f..Float.MAX_VALUE -> {
                        minage = it
                        maxage = minage + 5f
                    }
                    else -> {
                        minage = it
                        maxage = minage + 0.5f
                    }
                }
                if (minage <= age && age < maxage) {
                    for ((keys, data) in item) {
                        val v = dt[0][keys]
                        if (value < 14) {
                            res = item["a1"]!!
                            break
                        } else if (value > 72) {
                            res = item["a59"]!!
                            break
                        } else {
                            if (v == value.toFloat()) {
                                res = data
                                break
                            }
                        }
                    }
                }
            }
            if (minage <= age && age < maxage) {
                break
            }
        }
        return res
    }

    private fun addRowZL(vararg value: Float) {
        val map = HashMap<String, Float>()
        map["age"] = value[0]
        for (i in 1 until value.size) {
            val key = "a$i"
            map[key] = value[i]
        }
        dt.add(map)
    }
}