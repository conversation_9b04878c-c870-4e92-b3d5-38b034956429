package com.ygxj.scale.scaleResult.util;

public class QuizEntity {
    private String YSM = "";// 因素名
    private String BZF = ""; // 总分标准分
    private String JF = "";//均分
    private String BZC = ""; //标准差
    private String YJ = "";//意见
    private String XX = ""; // 用户选择题目及答案

    public QuizEntity(
            String YSM,
            String BZF,
            String JF,
            String BZC,
            String YJ,
            String XX
    ) {
        this.YSM = YSM;
        this.BZF = BZF;
        this.JF = JF;
        this.BZC = BZC;
        this.YJ = YJ;
        this.XX = XX;
    }

    public String getYSM() {
        return YSM;
    }

    public void setYSM(String YSM) {
        this.YSM = YSM;
    }

    public String getBZF() {
        return BZF;
    }

    public void setBZF(String BZF) {
        this.BZF = BZF;
    }

    public String getJF() {
        return JF;
    }

    public void setJF(String JF) {
        this.JF = JF;
    }

    public String getBZC() {
        return BZC;
    }

    public void setBZC(String BZC) {
        this.BZC = BZC;
    }

    public String getYJ() {
        return YJ;
    }

    public void setYJ(String YJ) {
        this.YJ = YJ;
    }

    public String getXX() {
        return XX;
    }

    public void setXX(String XX) {
        this.XX = XX;
    }

    @Override
    public String toString() {
        return "QuizEntity{" +
                "YSM='" + YSM + '\'' +
                ", BZF='" + BZF + '\'' +
                ", JF='" + JF + '\'' +
                ", BZC='" + BZC + '\'' +
                ", YJ='" + YJ + '\'' +
                ", XX='" + XX + '\'' +
                '}';
    }
}
