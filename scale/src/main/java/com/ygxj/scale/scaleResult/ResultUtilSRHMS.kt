package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils

class ResultUtilSRHMS {
    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
//身体症状与器官功能
        //正向评分条目有：1,2,3,6
        //反向评分条目有：4,5,7

        val list_A_F = mutableListOf(1, 2, 3, 6)
        val list_A_R = mutableListOf(4, 5, 7)
        //日常生活功能
        //正向评分条目有：8,9,10,11,12
        val list_B_F = mutableListOf(8, 9, 10, 11, 12)
        //身体活动功能
        //正向评分条目有：13,14,15,16,17
        val list_C_F = mutableListOf(13, 14, 15, 16, 17)
        //正向情绪
        //正向评分条目有：19,20,21,22,23
        val list_D_F = mutableListOf(19, 20, 21, 22, 23)
        //心理症状与负向情绪
        //反向评分条目有：24,25,26,27,28,29,30
        val list_E_R = mutableListOf(24, 25, 26, 27, 28, 29, 30)
        //认知功能
        //正向评分条目有：31,32,33
        val list_F_F = mutableListOf(31, 32, 33)
        //角色活动与社会适应
        //正向评分条目有：35,36,37,38
        val list_G_F = mutableListOf(35, 36, 37, 38)
        //社会资源与社会接触
        //正向评分条目有：39,40,41,42,43
        val list_H_F = mutableListOf(39, 40, 41, 42, 43)
        //社会支持
        //正向评分条目有：44,45,46
        val list_I_F = mutableListOf(44, 45, 46)
        //健康总体自测
        //正向评分条目有：18,34,47,48
        val list_J_F = mutableListOf(18, 34, 47, 48)

        var count_A = 0
        var count_B = 0
        var count_C = 0
        var count_D = 0
        var count_E = 0
        var count_F = 0
        var count_G = 0
        var count_H = 0
        var count_I = 0
        var count_J = 0//总分

        val _XX = mutableListOf<String>()

        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            val value = answers[index].toInt() + 1
            if (list_A_F.contains(index) || list_A_R.contains(index))
                count_A += if (list_A_R.contains(index)) QuizUtils.score(value, 11) else value
            else if (list_B_F.contains(index))
                count_B += value
            else if (list_C_F.contains(index))
                count_C += value
            else if (list_D_F.contains(index))
                count_D += value
            else if (list_E_R.contains(index))
                count_E += QuizUtils.score(value, 11)
            else if (list_F_F.contains(index))
                count_F += value
            else if (list_G_F.contains(index))
                count_G += value
            else if (list_H_F.contains(index))
                count_H += value
            else if (list_I_F.contains(index))
                count_I += value
            else if (list_J_F.contains(index))
                count_J += value

            _XX.add(value.toString())
        }
        val sum =
            count_A + count_B + count_C + count_D + count_E + count_F + count_G + count_H + count_I + count_J

        val SCount = count_C + count_B + count_A
        val XCount = count_D + count_E + count_F
        val Count = count_G + count_H + count_I
        val yj = StringBuffer("您的测试结果如下：|总分${sum}分，")
            .append(if (sum <= 220) "自测结果显示您健康存在一些问题，请及时联系医生，做进一步检查与指导。" else "自测结果显示您健康状况总体良好。")
            .append("|【自测生理健康】 ${SCount}分，")
            .append(if (SCount <= 85) "测试结果显示您生理健康状况存在一些问题，请及时联系医生，做进一步检查。" else "测试结果显示您生理健康状况总体良好。")
            .append("|【自测心理健康】 ${XCount}分，")
            .append(if (XCount <= 75) "测试结果显示您心理健康状况存在一些问题，请及时联系心理医生，做进一步诊断及指导。" else "测试结果显示您心理健康状况总体良好。")
            .append("|【自测社会健康】 ${Count}分，")
            .append(if (Count <= 60) "测试结果显示您社会健康存在一些问题，请及时联系心理医生，做进一步检查与指导。" else "测试结果显示您社会健康状况总体良好。")
            .append("|各维度具体自测结果：|【身体症状与器官功能】 ${count_A}分，")
            .append(if (count_A <= 35) "测试结果显示您这方面相关的健康状况存在一些问题，请及时联系医生，做进一步检查。" else "测试结果显示您这方面相关的健康状况总体良好。")
            .append("|【日常生活功能】 ${count_B}分，")
            .append(if (count_B <= 25) "测试结果显示您的日常生活自理存在一些困难。" else "测试结果显示您的日常生活自理基本无困难。")
            .append("|【身体活动功能】 ${Count}分，")
            .append(if (count_C <= 25) "测试结果显示您的身体活动功能一些问题，请及时联系医生，做进一步检查。" else "测试结果显示您的身体活动功能总体良好。")
            .append("|【正向情绪】 ${count_D}分，")
            .append(if (count_D <= 25) "测试结果显示您有消极悲观情绪， 必要时可以与心理医生聊聊心里话。" else "测试结果显示您对生活积极、乐观，情绪状态良好。")
            .append("|【心理症状与负向情绪】 ${count_E}分，")
            .append(if (count_E <= 35) "测试结果显示您的情绪状态存在一些问题，请及时联系心理医生，做进一步诊断。" else "测试结果显示您的情绪状态良好。")
            .append("|【认知功能】 ${count_F}分，")
            .append(if (count_F <= 15) "测试结果显示您的记忆力、注意力等认知功能方面存在一些问题，请及时联系心理医生，做进一步检查。" else "测试结果显示您的记忆力、注意力等认知功能状态良好。")
            .append("|【角色活动与社会适应】 ${count_G}分，")
            .append(if (count_G <= 20) "测试结果显示您此相关项存在一些问题，请及时联系心理老师，做进一步心理指导。" else "测试结果显示您此相关项功能良好。")
            .append("|【社会资源与社会接触】 ${count_H}分，")
            .append(if (count_H <= 25) "测试结果显示您该类别存在一些问题，需要您主动结交朋友、与人交往，必要时可以接受心理指导，提升人际交往能力。" else "测试结果显示您该类别功能良好。")
            .append("|【社会支持】 ${count_I}分，")
            .append(if (count_I <= 15) "测试结果显示您的社会存在一些问题，请及时联系心理医生，做进一步心理指导。" else "测试结果显示您的社会支持体系良好。")
            .append("|【健康总体自测】 ${count_J}分，")
            .toString()
        val ysm = "身体症状与器官功能|日常生活功能|身体活动功能|正向情绪|心理症状与负向情绪|认知功能|角色活动与社会适应|社会资源与社会接触|社会支持|健康总体自测"
        val bzf = mutableListOf(
            count_A,
            count_B,
            count_C,
            count_D,
            count_E,
            count_F,
            count_G,
            count_H,
            count_I,
            count_J
        ).joinToString("|")
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = ysm,
            BZF = bzf,
            JF = "",
            YJ = yj,
            BZC = "",
            XX = answers.joinToString("|")
        )
    }
}