package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils

class ResultUtilBPRS {

    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        val jlyylist = mutableListOf(1, 2, 5, 9)//焦虑忧郁
        val qfhllist = mutableListOf(3, 13, 16, 18)//缺乏活力
        val swzalist = mutableListOf(4, 8, 12, 15)//思维障碍
        val jhxlist = mutableListOf(6, 7, 17)//激活性
        val ddcylist = mutableListOf(10, 11, 14)//敌对猜疑
        var count = 0
        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            val value = answers[index].toInt()
            if (jlyylist.contains(index)) {
                count += value
            }
            if (qfhllist.contains(index)) {
                count += value
            }
            if (swzalist.contains(index)) {
                count += value
            }
            if (jhxlist.contains(index)) {
                count += value
            }
            if (ddcylist.contains(index)) {
                count += value
            }
        }
        val yj = when {
            count > 107 -> "测试结果如下：被测试者本次总分为${count}分，测试结果显示具有极重精神病性症状。"
            count > 89 -> "测试结果如下：被测试者本次总分为${count}分，测试结果显示具有重度精神病性症状。"
            count > 71 -> "测试结果如下：被测试者本次总分为${count}分，测试结果显示具有偏重精神病性症状。"
            count > 53 -> "测试结果如下：被测试者本次总分为${count}分，测试结果显示具有中度精神病性症状。"
            count > 35 -> "测试结果如下：被测试者本次总分为${count}分，测试结果显示具有轻度精神病性症状。"
            else -> "测试结果如下：被测试者本次总分为${count}分，测试结果显示正常，无精神病性症状。"
        }


        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "总分",
            BZF = "$count",
            JF = "",
            BZC = "",
            YJ = yj,
            XX = answers.joinToString("|"),
        )
    }

}