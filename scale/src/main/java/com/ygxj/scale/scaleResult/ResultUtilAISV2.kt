package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils

class ResultUtilAISV2 {

    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        var count = 0
        for (item in answers) {
            if (!QuizUtils.isInteger(item)) continue
            count += item.toInt()
        }
        val yj =
            "您的测试结果如下：" + count + "分，" + (
                    when {
                        count > 6 -> "测试结果显示经常处于失眠状态。"
                        count in 4..6 -> "测试结果显示可能存在失眠情况。"
                        else -> "测试结果显示无睡眠障碍"
                    })
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "分值",
            BZF = "$count",
            JF = "",
            BZC = "",
            YJ = yj,
            XX = answers.joinToString("|"),
        )
    }

}