package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import kotlin.math.roundToInt

//贝克焦虑量表(BAI)
class ResultUtilBAIV2 {
    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        //结果统计
        var count = 0 //粗分
        for (item in answers) {
            count += item.toInt()
        }
        val bzf = "%.1f".format(1.19 * count)
        // bzf 保留1位小数
        val results = mutableListOf(
            "无焦虑。|在日常生活中受测者的心态很平稳，不会无缘无故地担心害怕什么，很少出现心悸、发抖、呼吸困难等身体反应。",
            "轻度焦虑。|在多数时候，受测者的心态是比较平稳的，但有时会感到莫名的担忧和害怕，面对压力时受测者可能会感到有点紧张，出现心悸、发抖、呼吸困难等身体反应。",
            "中度焦虑。|受测者时常会感到莫名的担忧和害怕，这使受测者烦躁不安、心神不宁。很多时候受测者比较紧张，难以放松，经常出现心悸、发抖、呼吸困难等身体反应，这些症状可能影响了受测者的正常生活。",
            "重度焦虑。|受测者终日忧心忡忡、心神不宁、烦躁不安，总是保持在一种高度紧张的状态中，无法放松。由于过度紧张，受测者经常出现心悸、发抖、呼吸困难等身体反应，严重时甚至晕厥。这些症状时刻困扰着受测者，使受测者难以正常地生活。"
        )
        val yj = StringBuffer()
            .append("您的测试结果如下：|本次得分为${bzf}分,")
            .append(
                when (bzf.toFloat().roundToInt()) {
                    in 0..14 -> results[0]
                    in 15..25 -> results[1]
                    in 26..35 -> results[2]
                    else -> results[3]
                }
            )
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "总分",
            BZF = count.toString(),
            JF = bzf,
            YJ = yj.toString(),
            BZC = "",
            XX = answers.joinToString("|")
        )
    }
}