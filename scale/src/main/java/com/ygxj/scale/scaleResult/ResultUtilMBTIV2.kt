package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity

//MBTI人格量表
class ResultUtilMBTIV2 {

    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        // E:外向 I:内向
        // S:感觉 N:直觉
        // T:思维 F:情感
        // J:判断 P:知觉

        // 3·7·10·19·23·32·62·74·79·81·83题中,选了几个A(即0)就在E维度上加几分
        val eScoreIndexA = arrayListOf(3, 7, 10, 19, 23, 32, 62, 74, 79, 81, 83)
        // 13·16·26·38·42·57·68·77·85·91题中,选了几个B(即1)就在E维度上加几分
        val eScoreIndexB = arrayListOf(13, 16, 26, 38, 42, 57, 68, 77, 85, 91)

        // 2·9·25·30·34·39·50·52·54·60·63·73·92 选了几个A(即0)就在S维度上加几分
        val sScoreIndexA = arrayListOf(2, 9, 25, 30, 34, 39, 50, 52, 54, 60, 63, 73, 92)
        // 5·11·18·22·27·44·46·48·65·67·69·71·82 选了几个B(即1)就在S维度上加几分
        val sScoreIndexB = arrayListOf(5, 11, 18, 22, 27, 44, 46, 48, 65, 67, 69, 71, 82)

        // 31·33·35·43·45·47·49·56·58·61·66·75·87 选了几个A(即0)就在T维度上加几分
        val tScoreIndexA = arrayListOf(31, 33, 35, 43, 45, 47, 49, 56, 58, 61, 66, 75, 87)
        // 6·15·21·29·37·40·51·53·70·72·89 选了几个B(即1)就在T维度上加几分
        val tScoreIndexB = arrayListOf(6, 15, 21, 29, 37, 40, 51, 53, 70, 72, 89)

        // 1·4·12·14·20·28·36·41·64·76·86 选了几个A(即0)就在J维度上加几分
        val jScoreIndexA = arrayListOf(1, 4, 12, 14, 20, 28, 36, 41, 64, 76, 86)
        // 8·17·24·55·59·78·80·84·88·90·93 选了几个B(即1)就在J维度上加几分
        val jScoreIndexB = arrayListOf(8, 17, 24, 55, 59, 78, 80, 84, 88, 90, 93)

        val eOriginScore = getOriginScore(eScoreIndexA, eScoreIndexB, answers)
        val sOriginScore = getOriginScore(sScoreIndexA, sScoreIndexB, answers)
        val tOriginScore = getOriginScore(tScoreIndexA, tScoreIndexB, answers)
        val jOriginScore = getOriginScore(jScoreIndexA, jScoreIndexB, answers)

        // 根据E S T J,计算出I N F P得分,这几个维度两两对应为一组
        val iOriginScore = 21 - eOriginScore
        val nOriginScore = 26 - sOriginScore
        val fOriginScore = 24 - tOriginScore
        val pOriginScore = 22 - jOriginScore

        // ===== 获取结果描述
        val resultList = arrayListOf<String>()
        val summaryLetter = StringBuilder()
        val summaryText = StringBuilder()
        // 第一组 E:外向 I:内向
        if (eOriginScore > iOriginScore) {
            resultList.add("【外向——内向（E—I）】E=${eOriginScore}分，明显偏向外向型。")
            summaryLetter.append("E")
            summaryText.append("外向+")
        } else {
            resultList.add("【外向——内向（E—I）】I=${iOriginScore}分，明显偏向内向型。")
            summaryLetter.append("I")
            summaryText.append("内向+")
        }
        // 第二组 S:感觉 N:直觉
        if (sOriginScore > nOriginScore) {
            resultList.add("【感觉——直觉（S—N）】S=${sOriginScore}分，明显偏向感觉型。")
            summaryLetter.append("S")
            summaryText.append("感觉+")
        } else {
            resultList.add("【感觉——直觉（S—N）】N=${nOriginScore}分，明显偏向直觉型。")
            summaryLetter.append("N")
            summaryText.append("直觉+")
        }
        // 第三组 T:思维 F:情感
        if (tOriginScore > fOriginScore) {
            resultList.add("【思维——情感（T—F）】T=${tOriginScore}分，明显偏向思维型。")
            summaryLetter.append("T")
            summaryText.append("思维+")
        } else {
            resultList.add("【思维——情感（T—F）】F=${fOriginScore}分，明显偏向情感型。")
            summaryLetter.append("F")
            summaryText.append("情感+")
        }
        // 第四组 J:判断 P:知觉
        if (jOriginScore > pOriginScore) {
            resultList.add("【判断——知觉（J—P）】J=${jOriginScore}分，明显偏向判断型。")
            summaryLetter.append("J")
            summaryText.append("判断")
        } else {
            resultList.add("【判断——知觉（J—P）】P=${pOriginScore}分，明显偏向知觉型。")
            summaryLetter.append("P")
            summaryText.append("知觉")
        }
        // ===== 获取总结
        resultList.add("总结：您性格类型倾向为“ $summaryLetter ”(${summaryText})")
        // ===== 根据性格类型倾向,获取描述和适合职业
        val descAndCareer = getMBTIDescAndCareer(summaryLetter.toString())
        if (descAndCareer != null) {
            resultList.add(descAndCareer.desc)
            resultList.add(descAndCareer.career)
        }
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "外向|内向|感觉|直觉|思维|情感|判断|知觉",
            BZF = "${eOriginScore}|${iOriginScore}|${sOriginScore}|${nOriginScore}|${tOriginScore}|${fOriginScore}|${jOriginScore}|${pOriginScore}",
            JF = "",
            BZC = "",
            cf = "",
            XX = answers.joinToString("|"),
            YJ = resultList.joinToString("\n")
        )
    }


    /**
     * 计算各维度原始得分
     * @param questionIndexA 选A(0)时加分的题目索引,从1开始记
     * @param questionIndexB 选B(1)时加分的题目索引,从1开始记
     * @param answers 用户选项
     */
    private fun getOriginScore(questionIndexA: List<Int>, questionIndexB: List<Int>, answers: List<String>): Int {
        var score = 0
        for (i in answers.indices) {
            // 题目索引
            when (i + 1) {
                in questionIndexA -> if (answers[i] == "0") score += 1
                in questionIndexB -> if (answers[i] == "1") score += 1
            }
        }
        return score
    }

    /**
     * 获取性格类型倾向的描述和适合职业
     * @param flag 四个维度组合的标识,如:INTP,INFP等
     */
    fun getMBTIDescAndCareer(flag: String): MBTIDescAndCareer? {
        val data = arrayListOf(
            MBTIDescAndCareer(
                "ISTJ",
                "【ISTJ】安静、严肃，通过全面性和可靠性获得成功。实际，有责任感。决定有逻辑性，并一步步地朝着目标前进，不易分心。喜欢将工作、家庭和生活都安排得井井有条。重视传统和忠诚。",
                "【适合职业】商业·审计员·公司经理·会计·管理者／监督人·文字信息处理专家·效率专家／效率分析者·保险业主(保险商)·后勤供给经理·制定规章制度的官员·信息总管·会计／保险统计员/销售／服务·警察局高级职员／侦探·情报检索服务社代理人·政府雇员·陆军军官·管教人员·房地产代理商·体育设备／商品销售商·教养所所长/金融·银行查帐员·投资担保人·税收监察员·预算分析员·股票经纪人·房地产策划·信贷分析员/教育·学校校长·教师：技术／工业／数学／物理·图书管理员·管理者/法律／技术·法律调查员·律师秘书·电工·工程师·机械师·计算机程序设计员·科学作家·律师秘书／律师专职助手·药品经销商／调查员·脑电图技术专家／技师·地质学家·气象学家·航空机械师·机械／工业／电子工程师·农业科学家/保健·兽医·普通外科医师·牙医·护理指导员·保健指导员·药剂师·实验室技术人员·医学研究者·最初保健护理医师·生物学和医学技术专家·运动生理学家·药剂师／配药技术员/ 销售／服务・警察局高级职员／侦探・情报检索服务社代理人・政府雇员・陆军军官・管教人员・房地产代理商・体育设备／商品销售商・教养所所长 / 金融・银行查帐员・投资担保人・税收监察员・预算分析员・股票经纪人・房地产策划・信贷分析员 / 教育・学校校长・教师：技术／工业／数学／物理・图书管理员・管理者 / 法律／技术・法律调查员・律师秘书・电工・工程师・机械师・计算机程序设计员・科学作家・律师秘书／律师专职助手・药品经销商／调查员・脑电图技术专家／技师・地质学家・气象学家・航空机械师・机械／工业／电子工程师・农业科学家 / 保健・兽医・普通外科医师・牙医・护理指导员・保健指导员・药剂师・实验室技术人员・医学研究者・最初保健护理医师・生物学和医学技术专家・运动生理学家・药剂师／配药技术员"
            ),
            MBTIDescAndCareer(
                "ISFJ",
                "【ISFJ】安静、友好、有责任感和良知。坚定地致力于完成他们的义务。全面、勤勉、精确，忠诚、体贴，留心和记得他们重视的人的小细节，关心他们的感受。努力把工作和家庭环境营造得有序而温馨。",
                "【适合职业】健康护理部分·牙医·家庭运动医生·护士·医务技术专家·理疗法专家·医疗设备推销·提供健康护理人员·饮食专家／营养学家·眼科大夫·医务记录管理人员·药剂师／药剂技术人员·放射专家·呼吸系统专家·兽医·有执照行医护士·初级保健大夫·家庭保健助理·医务／牙科助理/社会服务部门／教育部门·学前教育／初级学校教师·图书馆员／建筑师·教育管理人员·社会福利工作人员·咨询服务人员·个人咨询人员·宗教教育者·言语病理学家·家庭健康社会服务人员·儿童福利咨询人员·戒酒和戒毒咨询人员·小学教师·图书管理员／档案保管员·特殊教育老师·(博物馆、美术馆、图书馆等的)馆长·家谱学家·教育行政人员·咨询服务人员·社会工作者(老年服务)·神学教育者·(博物馆、图书馆、美术馆等的)馆长·社会工作人员(老年和儿童每日看护问题)/商业·秘书·员工监督人·顾客服务代表·人事管理人员·计算机操作者·记帐员·信用顾问·律师的专职助手·家用保健品推销/创造性／技术性·内部装饰人员·电工·零售商·旅店老板·艺术家·音乐家·优先顾客销售代理人·商业计划者·不动产经纪人/ 社会服务部门／教育部门・学前教育／初级学校教师・图书馆员／建筑师・教育管理人员・社会福利工作人员・咨询服务人员・个人咨询人员・宗教教育者・言语病理学家・家庭健康社会服务人员・儿童福利咨询人员・戒酒和戒毒咨询人员・小学教师・图书管理员／档案保管员・特殊教育老师・(博物馆、美术馆、图书馆等的) 馆长・家谱学家・教育行政人员・咨询服务人员・社会工作者 (老年服务)・神学教育者・(博物馆、图书馆、美术馆等的) 馆长・社会工作人员 (老年和儿童每日看护问题)/ 商业・秘书・员工监督人・顾客服务代表・人事管理人员・计算机操作者・记帐员・信用顾问・律师的专职助手・家用保健品推销 / 创造性／技术性・内部装饰人员・电工・零售商・旅店老板・艺术家・音乐家・优先顾客销售代理人・商业计划者・不动产经纪人"
            ),
            MBTIDescAndCareer(
                "INFJ",
                "【INFJ】寻求思想、关系、物质等之间的意义和联系。希望了解什么能够激励人，对人有很强的洞察力。有责任心，坚持自己的价值观。对于怎样更好的服务大众有清晰的远景。在对于目标的实现过程中有计划而且果断坚定。",
                "【适合职业】—般职业咨询／教育职业·职业咨询顾问·心理学家·教育顾问·图书管理员·特殊教育老师·双语教育老师·早期教育老师·雇员帮助顾问·儿童福利顾问·酒精和毒品禁戒顾问·教师：高中或大学英语、艺术、音乐、社会科学·社会：工作人员(老人与儿童口常护理问题)戏剧/家教职业·牧师／教士／修道士／修女·家教工作者·家教教育指导者/创造性职业·艺术家·小说家·设计师·通用设计建筑师·编辑、艺术指导(杂志)·剧作家·诗人·信息制图设计者·自由宣传媒介设计人·家谱学家(家族研究者)/健康护理／社会服务职业·保健管理人·调解人／冲突解决人·社会工作人员·饮食学家／营养学家·全面健康医生·职业治疗医生·社会服务代理人·社会科学家·心理健康顾问·语言病理学家／听觉学家·信息治疗医生/商业·人事资源经理·组织机构的发展顾问·职业分析家·公司／工作小组培训人·商业销售计划人·口译／翻译·市场人员(提供服务或点子)·雇员帮助方案的协调者／顾问·人事资源多样化管理人·优先顾客销售代表·环境法律师/ 家教职业・牧师／教士／修道士／修女・家教工作者・家教教育指导者 / 创造性职业・艺术家・小说家・设计师・通用设计建筑师・编辑、艺术指导 (杂志)・剧作家・诗人・信息制图设计者・自由宣传媒介设计人・家谱学家 (家族研究者)/ 健康护理／社会服务职业・保健管理人・调解人／冲突解决人・社会工作人员・饮食学家／营养学家・全面健康医生・职业治疗医生・社会服务代理人・社会科学家・心理健康顾问・语言病理学家／听觉学家・信息治疗医生 / 商业・人事资源经理・组织机构的发展顾问・职业分析家・公司／工作小组培训人・商业销售计划人・口译／翻译・市场人员 (提供服务或点子)・雇员帮助方案的协调者／顾问・人事资源多样化管理人・优先顾客销售代表・环境法律师"
            ),
            MBTIDescAndCareer(
                "INTJ",
                "【INTJ】在实现自己的想法和达成自己的目标时有创新的想法和非凡的动力。能很快洞察到外界事物间的规律并形成长期的远景计划。一旦决定做一件事就会开始规划并直到完成为止。多疑、独立，对于自己和他人能力和表现的要求都非常高。",
                "【适合职业】商业／金融·电信安全·经济学家·国际银行家·药物研究员·财务计划人·投资银行家·管理顾问：电脑／信息服务，营销，机构重组/科技·科学家／科学研究员·技术人员：电力／电子·设计工程师·宇航员·电脑程序员·环境规划人·生物医学研究员／工程师·电脑系统分析员·操作研究分析员·信息服务开发人·软件和系统研究员和开发员·网络一体化专家(电信业)·信息服务——新业务开发人/教育·教师：大学·学习课程设计人·行政管理人·数学家/健康护理／医药·精神病学家·心理学家·神经病学家·生物医学工程师·心病学家·药物学家·药物研究员·生物医学研究员/专门职业·代理人：行政管理／诉讼·管理顾问·战略设计人·投资／商业分析家·经理·法官·新闻分析员／撰稿人·工程师·冶金工程师·知识产权律师·土木工程师/创造性职业·作家／社论作家·艺术家·发明家·设计人·建筑师·整体设计建筑师·信息制图设计师·自由媒体设计人·编辑／艺术指导"
            ),
            MBTIDescAndCareer(
                "ISTP",
                "【ISTP】灵活、忍耐力强，是个安静的观察者直到有问题发生，就会马上行动，找到实用的解决方法。分析事物运作的原理，能从大量的信息中很快的找到关键的症结所在。对于原因和结果感兴趣，用逻辑的方式处理问题，重视效率。",
                "【适合职业】销售部门／服务部门／劳改部门·警员或劳教人员·赛车手·飞机驾驶员·武器操作员·猎人·情报人员·司法官·消防员·调查员·运动器材商品推销·药品推销·私人调查或私人侦探/技术部门·电器设备专家·信息产业开发人员·技术培训人员——一对一设置的·后勤和供给制造商或经理·网络调查专家(通讯部门)·电脑程序设计师·海洋生物学家·电器工程师、机械工程师、土木工程师/健康护理业·心电图专家或技师·透射技师·急救医生·运动保健医生·牙医牙科助理/商业／金融业·证券分析家·采购员·办公室管理人员·银行家·经济学家·法律顾问·业务顾问·律师帮办/贸易部门·计算机维修·飞机技师·农场人员·教练·木工·汽车部件零售商·商业家"
            ),
            MBTIDescAndCareer(
                "ISFP",
                "【ISFP】安静、友好、敏感、和善。享受当前。喜欢有自己的空间，喜欢能按照自己的时间表工作。对于自己的价值观和自己觉得重要的人非常忠诚，有责任心。不喜欢争论和冲突。不会将自己的观念和价值观强加到别人身上。",
                "【适合职业】工艺部门·时装设计·木匠·珠宝商·园艺匠·织毯工人·陶工·漆匠·舞蹈员·设计人员：内容／背景·厨师长/健康护理部门·上门服务护士·运动专家·按摩医生·放射技师·医务助理·牙科助理／保健医师·兽医助理·动物护理人员／训练人员·家庭保健助理·初级保健大夫·饮食专家／营养学家·验光师／配镜师·运动心理专家·职业疗法医师·工艺师·药剂师·呼吸系统专家·执行医护士/技术工作·调查员·计算机操作人员·林务员·植物学家·地质学家·技师·海洋生物学家/销售／服务业·教师：初级(自然科学／艺术)·警察／劳改官员·紧急热线电话操作员·清洁服务人员·仓库保管员·侍者·美容师·旅行用品推销·优质用户销售代表·商业计划人员·体育设备推销·家用保健品推销·家庭保健护理人员·儿童福利咨询人员·戒酒戒毒顾问·社会工作人员/商业·记帐员·司法员·打字员·职员监督人员·经理人员·律师帮办"
            ),
            MBTIDescAndCareer(
                "INFP",
                "【INFP】理想主义，对于自己的价值观和自己觉得重要的人非常忠诚。希望外部的生活和自己内心的价值观是统一的。好奇心重，很快能看到事情的可能性，能成为实现想法的催化剂。寻求理解别人和帮助他们实现潜能。适应力强，灵活，善于接受，除非是有悖于自己的价值观的。",
                "【适合职业】创造性职业／艺术·艺术家·作家：诗人／小说家·记者·娱乐人士·建筑师·演员·编辑·音乐家·信息制图设计师·编辑／艺术指导(杂志)/教育／咨询职业·大学教授：人文／艺术·调研员·心理学家·顾问·社会工作者·图书管理员.教育顾问·特殊教育老师·双语种教育老师.儿童早期教育老师·雇员帮助顾问·儿童福利顾问·酒精和毒品禁戒顾问·翻译／口译·法律调停人·社会工作者(老年人和儿童日常护理问题)/宗教职业·牧师·宗教教育工作者·传教士·教堂工作人员/医疗保健·饮食学家／营养学家·理疗医生·家庭健康社会工作者·职业治疗医生·按摩专家·全面健康医生·语言病理学家／听觉病理学家/机构发展·雇员发展专家·人力资源开发·社会科学家·多样化管理人·顾问工作组构建／冲突解决"
            ),
            MBTIDescAndCareer(
                "INTP",
                "【INTP】对于自己感兴趣的任何事物都寻求找到合理的解释。喜欢理论性的和抽象的事物，热衷于思考而非社交活动。安静、内向、灵活、适应力强。对于自己感兴趣的领域有超凡的集中精力深度解决问题的能力。多疑，有时会有点挑剔，喜欢分析。",
                "【适合职业】计划和开发·计算机软件设计员·计算机程序员·系统分析人员／数据库管理人员·调查开发专家·战略策划者·新市场或新产品开发者·网络一体化专家(电信专家)·转换管理方式的咨询·财政计划者·投资银行家·信息服务开发者——计算机程序设计·信息服务——新的商业开发领域·管理咨询：计算机／信息服务，销售，重组/保健／技术·神经病学家·物理学家·整形外科医生·药剂师·科学家：化学／生物·药品调查员·生物工程学家·兽医/专门领域·律师·经济学家·心理学家／精神分析学家·金融分析家·建筑师·侦探·知识产权代理人·法律调解人·公司财务代理人·精神病医生/学术领域·数学家·考古学家·历史学家·哲学家·大学里的高级教师·学术研究者·逻辑学家·大学行政官员·经济学家·翻译/创造性的职业·摄影师·富有创造力的作家·艺术家·演员／舞蹈家·音乐家·代理人·发明家·信息图表设计者"
            ),
            MBTIDescAndCareer(
                "ESTP",
                "【ESTP】灵活、忍耐力强，实际，注重结果。觉得理论和抽象的解释非常无趣。喜欢积极地采取行动解决问题。注重当前，自然不做作，享受和他人在一起的时刻。喜欢物质享受和时尚。学习新事物最有效的方式是通过亲身感受和练习。",
                "【适合职业】销售／服务／“活动”·警察·消防队员·护理人员·侦探·领航员·调查研究者·管教罪犯的人员·房地产经纪人·急诊医士·运动生理学家／运动医学家·呼吸治疗专家·空中服务员·体育用品销售·调查保险诈骗人员·私人侦探/金融·个人财务计划者·审计员·股票经纪人·银行业者·投资者·保险推销·预算分折员·保险代理人／经纪人(推销商)/娱乐业／体育运动·播送体育节目·新闻报道员·承办人·旅游代理人·舞蹈演员·酒吧间侍员·拍卖商·职业运动员／教练·体能指导员／训练者/技术的／商贸·木匠·工匠／手艺人·农民·总承包商· 建筑工人·厨师长／厨师·电气工程师·电子专家·集成网络专家(电信学)·工业／机械工程师·勘测员·脑电图专家／技术员·放射学专业人员·飞机修理工·海洋生物学家·技术培训者——课堂环景·后勤和供给经营人——制造业 ·土木工程师/商业·房地产经纪人·中间商·房地产投资开发者·批发商·零售商·汽车销售商·业务顾问(业务活动)·特许经营者"
            ),
            MBTIDescAndCareer(
                "ESFP",
                "【ESFP】外向、友好、接受力强。热爱生活、人类和物质上的享受。喜欢和别人一起将事情做成功。在工作中讲究常识和实用性，并使工作显得有趣。灵活、自然不做作，对于新的任何事物都能很快地适应。学习新事物最有效的方式是和他人一起尝试。",
                "【适合职业】教育及社会服务部门·教师：早期儿童教育及初级教育·儿童护理员。·家庭保健人员·体育教练·特殊教育老师·酗酒，吸毒劝戒人员·儿童福利顾问·海洋生物学家/健康护理·应急家庭护士·社会福利工作人员·训犬员·医务助理·牙科专家或牙科助理·领照实习护士·运动专家·最初保健护理医生·家庭保健服务人员·按摩专家·饮食专家或营养学家·紧急医务工作人员·运动生理学家·药剂师·放射专家·呼吸系统专家·营养专家·职业疗法医师/娱乐业·旅行代办人／旅游组织人员·摄像师·电影制片人·音乐家·承办人员·特殊事件统筹人员·表演艺术人员、舞蹈员、喜剧演员/商业／推销业·零售商或零售策划人员·公共关系专家·资金筹措者·劳资关系调查人员·接待员·商业计划人员·多样化管理者：人力资源·小组协同培训·旅行推销人员·保险代办人经纪人(健康或人寿)·房地产经纪人·体育设备推销／买卖·零售推销·家用保键用品推销/服务业·飞机服务员·秘书／接待员·侍者·旅店老板·花卉设计·警察／劳改服务人员/接待员·办公室机器操作员·簿记员·打字员"
            ),
            MBTIDescAndCareer(
                "ENFP",
                "【ENFP】热情洋溢、富有想象力。认为人生有很多的可能性。能很快地将事情和信息联系起来，然后很自信地根据自己的判断解决问题。总是需要得到别人的认可，也总是准备着给与他人赏识和帮助。灵活、自然不做作，有很强的即兴发挥的能力，言语流畅。",
                "【适合职业】创造性职业·记者·编剧或剧作家·专栏作家·性格演员·音乐家或作曲家·新闻广播员·室内装潢人·卡通制作人·艺术家·报道人或编辑·杂志报道人或编辑·信息图片设计师/营销／计划·公关专家·营销顾问·广告业务经理·广告撰稿人或公共写作人·广告创意指导·战略策划人·报刊宣传员·调研助理·编辑或艺术指导(杂志)/教育／咨询·特殊教育老师·双语种教育老师·早期儿童教育老师·艺术戏剧、音乐及英语老师·儿童福利顾问·酒精毒品禁戒顾问·社会工作者(老年人及儿童日常照顾问题)·发展指导·职业顾问·住宅安居指挥·民意调查员·主教顾问·康复中心工作人员·社会学家·心理学家/健康护理／社会服务·营养学家·语言病理学家或听觉学家·全面健康医生·按摩治疗专家·雇员辅助计划顾问·理疗专家·法律调停人/企业家／商业·顾问·发明家·无形商品或点子的销售·人力资源经理·人力资源发展训练人·会议安排人·雇佣发展专家·饭店老板·管理顾问：转变管理体制或合作组构建或管理多样化·公司或小组培训人·人力资源多样化管理人·广告业务管理人或经理·公关专家·营销主管：广播或电视或有线转播业·调职顾问·环境法律师"
            ),
            MBTIDescAndCareer(
                "ENTP",
                "【ENTP】反应快、睿智，有激励别人的能力，警觉性强、直言不讳。在解决新的、具有挑战性的问题时机智而有策略。善于找出理论上的可能性，然后再用战略的眼光分析。善于理解别人。不喜欢例行公事，很少会用相同的方法做相同的事情，倾向于一个接一个的发展新的爱好。",
                "【适合职业】企业家/商人／企业家·发明家·管理顾问(销售学/重组/补偿贸易)·风险资本家·摄影师·新闻记者·演员·大学校长·财务经理(商业/住宅)·诉讼代理人·保险/商品经销人·城市规划者·证券分析家/销售/创作·广告创意人·公共关系专家·营销调研/策划/电视/电台现场访谈节目主持人·制片人·美编(杂志/期刊)·信息制图设计师·专栏作家·记者·通讯员/计划和开发 ／房地产代理商/开发商·后勤顾问(生产)·网络一体化专家/电信专家·财政计划者·投资银行家·城市规划师/政治 ／政治家·行政管理人员·社会科学家/其他职业／环境科学家·教育心理学家·运动员教练·犯罪学家·侦探"
            ),
            MBTIDescAndCareer(
                "ESTJ",
                "【ESTJ】实际、现实主义。果断，一旦下决心就会马上行动。善于将项目和人组织起来将事情完成，并尽可能用最有效率的方法得到结果。注重日常的细节。有一套非常清晰的逻辑标准，有系统性地遵循，并希望他人也同样遵循。在实施计划时强而有力。",
                "【适合职业】销售／服务·保险代理·丧葬承办者·厨师·陆军军官·教师：贸易、工业、技术·政府雇员·保安人员·体育商品／设备经销商·药品经销商·电信防护员·警察／监护官／管教官·销售(有形的东西)：计算机、不动产/科技／物理·计算机系统分析家·审计员·总承包商·农场主·建筑工人·药剂师·临床医师·会计学内部审计员·技术教员·脑电图技术专家／技师·工程师：机械领域／应用领域·律师帮办管理·银行高级职员／贷款员·项目经理·职员总管·行政官员·工厂监督人·数据库经营者·购物代理人·信贷分析员·制定规章制度的官员·预算分析员·管理人：社会保健服务·信息主管·管理顾问：企业运行·后勤供给经理·银行经理／贷款员·信贷分析员／顾问/专门领域·牙医·内科医生：普通医学·股票经纪人·法官·行政领导·教师：技术／贸易·公司财务律师·电气工程师·最初保健护理医生·工业工程师·律师帮办·药剂师·土木／机械／冶金工程师"
            ),
            MBTIDescAndCareer(
                "ESFJ",
                "【ESFJ】热心肠、有责任心、合作。希望周边的环境温馨而和谐，并为此果断地执行。喜欢和他人一起精确并及时地完成任务。事无巨细都会保持忠诚。能体察到他人在日常生活中的所需并竭尽全力帮助。希望自己和自己的所为能受到他人的认可和赏识。",
                "【适合职业】保健·医师助理／牙医助理·言语病理学家·运动生理学家·家庭医生·护士·牙医·医用秘书·验光师·饮食学家／营养学家·按摩治疗专家·验光师／配镜技师·药剂师／制药技师·呼吸系统治疗专家·兽医·领照实习护士·家庭健康护理·最初保健护理医师·理疗专家·家庭健康社会工作者/教育·小学教师·特殊教育工怍者·儿童照管人员·家庭经济教师·运动教练·双语教学老师/社会服务／咨询·社会工作者·社区福利工作者·专业自愿者·宗教教育者·顾问·律师帮办·女雇员问题咨询顾问·儿童福利顾问·戒毒和戒酒咨询顾问·社会工作者·牧师／神父／拉比/商务·公关业务经理·私人银行家·销售代表(有形商品)·电话推销员·办公室经理·零售商·接待员·保险代理(家庭)·管理顾问：人力资源／培训·信贷顾问·经营策划者/销售／服务·飞机服务员·顾客服务代表·殡仪馆管理人员·高级理发师／美容师·旅店老板／老板娘·酒席承办者·资金筹集人·旅行推销员·环境旅游专家·不动产代理／经纪人·翻译·系谱学家·家庭保健用品销售·体育设备／商品销售·营销经理：无线电／电视／广播电缆工业/职员工作·秘书·接待员·办公室机器操作员·簿记员·打字员"
            ),
            MBTIDescAndCareer(
                "ENFJ",
                "【ENFJ】热情、为他人着想、易感应、有责任心。非常注重他人的感情、需求和动机。善于发现他人的潜能，并希望能帮助他们实现。能成为个人或群体成长和进步的催化剂。忠诚，对于赞扬和批评都会积极地回应。友善、好社交。在团体中能很好地帮助他人，并有鼓舞他人的领导能力。",
                "【适合职业】交流性职业·广告销售主管·公共关系专家·对外交流董事·作家／新闻工作者·娱乐表演者／艺术家·资金筹备人·招聘人员·娱乐业导演·电视制片人·新闻广播员·政客·信息制图设计人·营销经理(电台、电视、有线播放行业)·编辑(杂志)/咨询顾问·心理医生·职业顾问·牧师／教士·翻译／口译·雇员帮助顾问·便利促进人·私人顾问·公司公共活动顾问·酒精和毒品戒禁顾问/教育／社会服务职业·教师：卫生健康／艺术／戏剧／英文·学生的系主任·大学教授：人文学科·儿童福利工作者·图书馆管理员·社会工作者·特殊教育教师·双语种教育老师·老年人社会工作者·住宅安居指导·非盈利性组织的指导者·早期教育教师/健康护理职业·全面健康医生(可替代药物)·饮食学家/营养学家·语言障碍病理学家/听觉病理学家·职业治疗医生/商业/咨询职业·开发人力资源的培训员·推销培训员·招聘人员·旅游代理人·小型企业经理·项目设计人·销售经理·调职顾问·公司/工作小组的培训员·生态旅行专家·管理顾问"
            ),
            MBTIDescAndCareer(
                "ENTJ",
                "【ENTJ】坦诚、果断，有天生的领导能力。能很快看到公司/组织程序和政策中的不合理性和低效能性，发展并实施有效和全面的系统来解决问题。善于做长期的计划和目标的设定。通常见多识广，博览群书，喜欢拓广自己的知识面并将此分享给他人。在陈述自己的想法时非常强而有力。",
                "【适合职业】商业·经理·高级主管·办公室经理·行政管理人·人事经理·销售经理·营销经理·网络一体化专家(电讯)·技术培训人员·信息服务——新业务开发人·后勤顾问(生产)·广告业务经理·管理顾问：电脑／信息服务，营销，机构重组·营销经理：广播／电视／有线播放行业·媒体策划／买主·国际销售和营销·特许权所有人·销售经理：制药业·管理人：健康服务"
            )
        )

        for (item in data) {
            if (item.flag.lowercase() == flag.lowercase()) {
                return item
            }
        }
        return null
    }

}

data class MBTIDescAndCareer(
    val flag: String,
    val desc: String,
    val career: String
)