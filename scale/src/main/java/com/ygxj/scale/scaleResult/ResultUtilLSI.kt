package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils

class ResultUtilLSI {
    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {


        val hy = mutableListOf(1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41)
        val gw = mutableListOf(2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42)
        val sj = mutableListOf(3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43)
        val xl = mutableListOf(4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44)
        var ahy = 0
        var bhy = 0
        var agw = 0
        var bgw = 0
        var asj = 0
        var bsj = 0
        var axl = 0
        var bxl = 0
        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            val value = answers[index].toInt() + 1
            if (index in hy) if (value == 1) ahy++ else if (value == 2) bhy++
            if (index in gw) if (value == 1) agw++ else if (value == 2) bgw++
            if (index in sj) if (value == 1) asj++ else if (value == 2) bsj++
            if (index in xl) if (value == 1) axl++ else if (value == 2) bxl++

        }
        val bzf = mutableListOf(
            if (bhy == 0) 0 else ahy / bhy,
            if (bgw == 0) 0 else agw / bgw,
            if (bsj == 0) 0 else asj / bsj,
            if (bxl == 0) 0 else axl / bxl,
        )
        val yj = StringBuffer()
            .append(if (ahy in (bhy + 1)..4) "你具有活跃型学习者的倾向。对于自己做过的事情,你往往能记得很好。你也愿意与同学讨论学习中遇到的问题|" else "")
            .append(if (ahy > bhy && ahy >= 5 && ahy < 9) "基本上属于活跃型学习者。做作业时,你喜欢拿起笔就写。在与同学讨论时,你总是积极发言|" else "")
            .append(if (ahy > bhy && ahy >= 9) "你属于非常典型的活跃型学习者。你性格外向,非常喜欢集体工作,有许多朋友。在遇到新问题时,你经常会说：“来,我们试试看,看看会怎样”|" else "")
            .append(if ((bhy in (ahy + 1)..4)) "你具有沉思型学习者的倾向。你喜欢自己一个人独立工作,不喜欢热闹的场合|" else "")
            .append(if (bhy > ahy && bhy >= 5 && bhy < 9) "基本上属于沉思型学习者。在与同学讨论时,你通常发言较少,愿意多听他人的意见。做事情也比较谨慎,总是想好了再做,不会贸然行事|" else "")
            .append(if (bhy > ahy && bhy >= 9) "你属于非常典型的沉思型学习者。你性格内向,喜欢安静地思考问题。在遇到新问题时,你经常会说：“让我好好想想吧”|" else "")
            .append(if (agw in (bgw + 1)..4) "你具有感悟型学习者的倾向。你不喜欢与现实生活没有明显联系的课程|" else "")
            .append(if (agw > bgw && agw >= 5 && agw < 9) "基本上属于感悟型学习者。你对细节很有耐心,擅长记忆事实和做一些有固定规则的工作,并能做得很仔细。你也喜欢与那些做事认真细致的人打交道|" else "")
            .append(if (agw > bgw && agw >= 9) "你属于非常典型的感悟型学习者。你喜欢学习确定的事实,不喜欢复杂情况和突发情况,痛恨测试一些在课堂里没有明确讲解过的内容|" else "")
            .append(if (bgw > agw && bhy < 5) "你具有直觉型学习者的倾向。你不喜欢那些包括许多需要记忆和进行烦琐计算的课程,喜欢与想象力丰富的人打交道|" else "")
            .append(if (bgw > agw && bgw >= 5 && bgw < 9) "基本上属于直觉型学习者。你擅长于掌握新概念,能很好地理解抽象的数学公式|" else "")
            .append(if (bgw > agw && bgw >= 9) "你属于非常典型的直觉型学习者。你喜欢发现某种可能性和事物间的关系,喜欢革新,不喜欢重复。你做事的速度更快,并具有创新性|" else "")
            .append(if (asj in (bsj + 1)..4) "你具有视觉型学习者的倾向。你喜欢看电视,喜欢看地图,喜欢图文并茂的讲解和图书|" else "")
            .append(if (asj > bsj && asj >= 5 && asj < 9) "基本上属于视觉型学习者。你喜欢通过照片,图像,插图,图表等获得信息,而不是阅读和听讲|" else "")
            .append(if (asj > bsj && asj >= 9) "你属于非常典型的视觉型学习者。在你回忆经历过的事情时,总是能浮现出许多生动的画面；对于形象的事物,尤其是他人的相貌,你也总是能记得很清楚|" else "")
            .append(if (bsj in (asj + 1)..4) "你具有言语型学习者的倾向。你喜欢听人演讲,不喜欢看画面很绚丽的电视节目|" else "")
            .append(if (bsj > asj && bsj >= 5 && bsj < 9) "基本上属于言语型学习者。你喜欢阅读,不喜欢有太多图片的书籍。在阅读时,你通常能准确地记住书中的文字表述|" else "")
            .append(if (bsj > asj && bsj >= 9) "你属于非常典型的言语型学习者。在你回忆经历过的事情时,能清楚地记得自己和他人说过的话。在了解一个人时,你更愿意看有关他的文字资料|" else "")
            .append(if (axl in (bxl + 1)..4) "你具有序列型学习者的倾向。在学习中,你经常会对某一主题的细节知道得许多,但在分析这一主题和其他知识的关联时,就会觉得很困难|" else "")
            .append(if (axl > bxl && axl >= 5 && axl < 9) "基本上属于序列型学习者。在许多情况下,你可能没有完全理解所学的知识内容,但你也能顺利完成作业,并在考试中取得好成绩|" else "")
            .append(if (axl > bxl && axl >= 9) "你属于非常典型的序列型学习者。你习惯于按线性步骤理解问题,按部就班地寻找答案,每一步都合乎逻辑地紧跟前一步|" else "")
            .append(if (bxl in (axl + 1)..4) "你具有综合型学习者的倾向。在学习时,你喜欢寻求知识之间的联系。在解决问题时,你经常能够抓住事物的主要部分,然后用新奇的方式将它们组合起来,形成一个有创意的解决方案|" else "")
            .append(if (bxl > axl && bxl >= 5 && bxl < 9) "基本上属于综合型学习者。在阅读时,你总是习惯于先看文章的结构,再有选择性地看细节。在做题时,你也喜欢从总体上思考解题方法,然后设法得出具体的解题步骤|" else "")
            .append(if (bxl > axl && bxl >= 9) "你属于非常典型的综合型学习者。你习惯快速地,广泛地学习有关知识,然后从总体上概括地把握知识。因此,你可能对知识只有大概的了解,对细节并不清楚|" else "")
            .toString()
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "活跃/沉思|感悟/直觉|视觉/言语|序列/综合",
            BZF = bzf.joinToString("|"),
            JF = "",
            YJ = yj,
            BZC = "",
            XX = answers.joinToString("|")
        )
    }
}