package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils
import kotlin.math.abs
import kotlin.math.roundToInt

class ResultUtilMMPI354 {
    fun getResult(scaleId: Int, answers: List<String>, costTime: String, sex: String, age: Int): ScaleResultEntity {
        //   分量表
        /*   14个分量表
         * L：说谎分数
         * F：诈病分数
         * K：校正分数
         * Hs：疑病  分男女M F
         * D：抑郁   男女M F
         * Hy：癔病  男女M F
         * Pd：精神病态
         * Mf-m: 男子气
         * Mf-f：女子气
         * Pa：妄想症
         * Pt：精神衰弱   男女M F
         * Sc：精神分裂症  男女M F
         * Ma：轻躁狂
         * Si：社会内向
         * Q: 无法作答
         */

        // 各分量表所包含的题目号
        val lf = mutableListOf(15, 30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 195, 225, 255, 285) //说谎分数 否   //-150   //2016-05-27 杨
        val ft = mutableListOf(14, 27, 31, 34, 35, 40, 42, 48, 49, 50, 53, 56, 66, 85, 121, 123, 139, 146, 151, 156, 168, 184, 197, 200, 202, 205, 206, 209, 210, 211, 215, 218, 227, 245, 246, 247, 252, 256, 269, 275, 286, 288, 291, 293) //诈病分数  是
        val ff = mutableListOf(17, 20, 54, 65, 75, 83, 112, 113, 115, 164, 169, 177, 185, 196, 199, 220, 257, 258, 272, 276) //诈病 否得

        val kt = mutableListOf(96) //校正分数  是
        val kf = mutableListOf(30, 39, 71, 89, 124, 129, 134, 138, 142, 148, 160, 170, 171, 180, 183, 217, 234, 267, 272, 296, 316, 322, 368, 370, 372, 373, 375, 386, 394) //校正分数 是

        val hyt = mutableListOf(10, 23, 32, 43, 44, 47, 76, 114, 179, 186, 189, 238, 253) //癔病 是
        val hyf = mutableListOf(2, 3, 6, 7, 8, 9, 12, 26, 30, 51, 55, 71, 89, 93, 103, 107, 109, 124, 128, 129, 136, 137, 141, 147, 153, 160, 163, 170, 172, 174, 175, 180, 188, 190, 192, 201, 213, 230, 234, 243, 265, 267, 274, 279, 289, 292) //意病 否

        val dt = mutableListOf(5, 32, 41, 43, 52, 67, 86, 104, 130, 138, 142, 158, 159, 182, 189, 193, 236, 259, 288, 290) //抑郁 是
        val df = mutableListOf(2, 8, 9, 18, 30, 36, 39, 46, 51, 57, 58, 64, 80, 88, 89, 95, 98, 107, 122, 131, 145, 152, 153, 154, 155, 160, 178, 191, 207, 208, 233, 241, 242, 248, 263, 270, 271, 272, 285, 296) //抑郁 否

        val hst = mutableListOf(23, 29, 43, 62, 72, 108, 114, 125, 161, 189, 273) //疑病 是
        val hsf = mutableListOf(2, 3, 7, 9, 18, 51, 55, 63, 68, 103, 130, 153, 155, 163, 175, 188, 190, 192, 230, 243, 274, 281) // 疑病 否

        val pdt = mutableListOf(16, 21, 24, 32, 33, 35, 38, 42, 61, 67, 84, 94, 102, 106, 110, 118, 127, 215, 216, 224, 239, 244, 245, 284) // 精神病态 是
        val pdf = mutableListOf(8, 20, 37, 82, 91, 96, 107, 134, 137, 141, 155, 170, 171, 173, 180, 183, 201, 231, 235, 237, 248, 267, 286, 289, 294, 296) //精神病态 否

        val mf_mt = mutableListOf(1, 25, 69, 70, 74, 77, 78, 87, 92, 126, 132, 134, 140, 149, 179, 187, 203, 204, 217, 226, 231, 239, 261, 278, 282, 295, 297, 299) //男子气 是
        val mf_mf = mutableListOf(1, 19, 26, 28, 79, 80, 81, 89, 99, 112, 115, 116, 117, 120, 133, 144, 176, 198, 213, 214, 219, 221, 223, 229, 249, 254, 260, 262, 264, 280, 283, 300) //男子气 否

        val mf_ft = mutableListOf(4, 25, 70, 74, 77, 78, 87, 92, 126, 132, 133, 134, 140, 149, 187, 203, 204, 217, 226, 239, 261, 278, 282, 295, 299) //女子气 是
        val mf_ff =
            mutableListOf(1, 19, 26, 28, 69, 79, 80, 81, 89, 99, 112, 115, 116, 117, 120, 144, 176, 179, 198, 213, 214, 219, 221, 223, 229, 231, 249, 254, 260, 262, 264, 280, 283, 297, 300) //女子气 否

        val pat = mutableListOf(16, 24, 27, 35, 110, 121, 123, 127, 151, 157, 158, 202, 275, 284, 291, 293, 299, 305, 314, 317, 326, 338, 341, 364, 365) //妄想症 是
        val paf = mutableListOf(93, 107, 109, 111, 117, 124, 268, 281, 294, 313, 316, 319, 327, 347, 348) //妄想症 否

        val ptt = mutableListOf(10, 15, 22, 32, 41, 67, 76, 86, 94, 102, 106, 142, 159, 182, 189, 217, 238, 266, 301, 304, 321, 336, 337, 340, 342, 343, 344, 346, 349, 351, 352, 356, 357, 358, 359, 360, 361, 362, 366) //精神衰弱 是
        val ptf = mutableListOf(3, 8, 36, 122, 152, 164, 178, 329, 353) //精神衰弱 否,
        val sct = mutableListOf(15, 22, 40, 41, 47, 52, 76, 97, 104, 102, 156, 157, 159, 168, 179, 182, 194, 202, 210, 212, 238, 241, 251, 259, 266, 273, 282, 291, 297, 301, 303, 307, 308, 311, 312, 315, 320, 323, 324, 325, 328, 331, 332, 333, 334, 335, 339, 341, 345, 349, 350, 352, 354, 355, 356, 360, 363, 364, 366) //精神分裂症 是
        val scf = mutableListOf(17, 65, 103, 119, 177, 178, 187, 192, 196, 220, 276, 281, 302, 306, 309, 310, 318, 322, 330) //精神分裂症 否
        val mat =
            mutableListOf(11, 13, 21, 22, 59, 64, 73, 97, 100, 109, 127, 134, 143, 156, 157, 167, 181, 194, 212, 222, 226, 228, 232, 233, 238, 240, 250, 251, 263, 266, 268, 271, 277, 279, 298) //轻躁狂 是
        val maf = mutableListOf(101, 105, 111, 119, 120, 148, 166, 171, 180, 267, 289) //轻躁狂 否

        val sit = mutableListOf(32, 67, 82, 111, 117, 124, 138, 147, 171, 172, 180, 201, 236, 267, 278, 292, 304, 316, 321, 332, 336, 342, 257, 369, 370, 373, 376, 378, 379, 385, 389, 393, 398, 399) //社会内向 是
        val sif = mutableListOf(25, 33, 57, 91, 99, 119, 126, 143, 191, 208, 229, 231, 254, 262, 281, 296, 309, 353, 359, 367, 371, 374, 377, 380, 381, 382, 383, 384, 387, 388, 390, 391, 392, 395, 396, 397) //社会内向 否


        // 分数统计
        var lfCount = 0
        var ftCount = 0
        var ffCount = 0
        var ktCount = 0
        var kfCount = 0
        var hytCount = 0
        var hyfCount = 0
        var dtCount = 0
        var dfCount = 0
        var hstCount = 0
        var hsfCount = 0
        var pdtCount = 0
        var pdfCount = 0
        var mf_mtCount = 0
        var mf_mfCount = 0
        var mf_ftCount = 0
        var mf_ffCount = 0
        var patCount = 0
        var pafCount = 0
        var pttCount = 0
        var ptfCount = 0
        var sctCount = 0
        var scfCount = 0
        var matCount = 0
        var mafCount = 0
        var sitCount = 0
        var sifCount = 0 //各分量表总分
        var noAnswer = 0 //统计无法作答的题目数

        // 循环
        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            val value = answers[index].toInt() + 1
            lfCount += if (lf.contains(index)) QuizUtils.score(value, 2) else 0
            ftCount += if (ft.contains(index) && value == 1) 1 else 0
            ffCount += if (ff.contains(index)) QuizUtils.score(value, 2) else 0
            ktCount += if (kt.contains(index) && value == 1) 1 else 0
            kfCount += if (kf.contains(index)) QuizUtils.score(value, 2) else 0
            hytCount += if (hyt.contains(index) && value == 1) 1 else 0
            hyfCount += if (hyf.contains(index)) QuizUtils.score(value, 2) else 0
            dtCount += if (dt.contains(index) && value == 1) 1 else 0
            dfCount += if (df.contains(index)) QuizUtils.score(value, 2) else 0
            hstCount += if (hst.contains(index) && value == 1) 1 else 0
            hsfCount += if (hsf.contains(index)) QuizUtils.score(value, 2) else 0
            pdtCount += if (pdt.contains(index) && value == 1) 1 else 0
            pdfCount += if (pdf.contains(index)) QuizUtils.score(value, 2) else 0
            mf_mtCount += if (mf_mt.contains(index) && value == 1) 1 else 0
            mf_mfCount += if (mf_mf.contains(index)) QuizUtils.score(value, 2) else 0
            mf_ftCount += if (mf_ft.contains(index) && value == 1) 1 else 0
            mf_ffCount += if (mf_ff.contains(index)) QuizUtils.score(value, 2) else 0
            patCount += if (pat.contains(index) && value == 1) 1 else 0
            pafCount += if (paf.contains(index)) QuizUtils.score(value, 2) else 0
            pttCount += if (ptt.contains(index) && value == 1) 1 else 0
            ptfCount += if (ptf.contains(index)) QuizUtils.score(value, 2) else 0
            sctCount += if (sct.contains(index) && value == 1) 1 else 0
            scfCount += if (scf.contains(index)) QuizUtils.score(value, 2) else 0
            matCount += if (mat.contains(index) && value == 1) 1 else 0
            mafCount += if (maf.contains(index)) QuizUtils.score(value, 2) else 0
            sitCount += if (sit.contains(index) && value == 1) 1 else 0
            sifCount += if (sif.contains(index)) QuizUtils.score(value, 2) else 0
            noAnswer += if (value == 3) 1 else 0  //统计无法作答的题目数
        }


        // 原始分数  注：lfCount 说谎分数            sitCount 社会内向   noAnswer 无法作答
        val fCount = ftCount + ffCount //诈病分数
        val kCount = ktCount + kfCount //校正分数
        val hyCount = hytCount + hyfCount //癔病
        val dCount = dtCount + dfCount //抑郁
        var hsCount = hstCount + hsfCount //疑病
        var pdCount = pdtCount + pdfCount //精神病态
        val mf_mCount = mf_mtCount + mf_mfCount //男子气
        val mf_fCount = mf_ftCount + mf_ffCount //女子气
        val paCount = patCount + pafCount //妄想症
        var ptCount = pttCount + ptfCount //精神衰弱
        var scCount = sctCount + scfCount //精神分裂症
        var maCount = matCount + mafCount //轻躁狂
        val siCount = sitCount + sifCount //社会内向

        val b = sex == "男"   // true:男 false:女
        // 未加K 原始分
        val countList = mutableListOf(lfCount, fCount, kCount, hsCount, dCount, hyCount, pdCount)
        //男子气   女子气
        if (b) countList.add(mf_mCount) else countList.add(mf_fCount)
        countList.add(paCount)
        countList.add(ptCount)
        countList.add(scCount)
        countList.add(maCount)
        countList.add(siCount)
        countList.add(noAnswer)
        // 将未加K的原始分转换成 T分 分男女
        var tHs: Int
        var tPd: Int
        var tPt: Int
        var tSc: Int
        val tMaMF = T("MaMF", maCount)
        if (b) {
            tHs = T("HsM", hsCount)
            tPd = T("PdM", pdCount)
            tPt = T("PtM", ptCount)
            tSc = T("ScM", scCount)
        } else {
            tHs = T("HsF", hsCount)
            tPd = T("PdF", pdCount)
            tPt = T("PtF", ptCount)
            tSc = T("ScF", scCount)
        }

        // 加  K 分数   四舍五入取值
        hsCount += (kCount * 0.5f).roundToInt()
        pdCount += (kCount * 0.4f).roundToInt()
        ptCount += kCount
        scCount += kCount
        maCount += (kCount * 0.2f).roundToInt()


        // 将加K后的原始分 转换成 T 分   分男女
        var noAnswerT: Int
        var lfT: Int
        var fT: Int
        var kT: Int
        var hsT: Int
        var dT: Int
        var hyT: Int
        var pdT: Int   //转换后的T分
        var mfT: Int
        var paT: Int
        var ptT: Int
        var scT: Int
        var maT: Int
        var siT: Int   //转换后的T分

        noAnswerT = KT_NEW("NoneMF", noAnswer)
        lfT = KT_NEW("LMF", lfCount)
        fT = KT_NEW("FMF", fCount)
        kT = KT_NEW("KMF", kCount)
        pdT = KT_NEW("PdMF", pdCount)
        paT = KT_NEW("PaMF", paCount)
        maT = KT_NEW("MaMF", maCount)
        siT = KT_NEW("SiMF", sitCount)
        if (b) {
            hsT = KT_NEW("HsM", hsCount)
            dT = KT_NEW("DM", dCount)
            hyT = KT_NEW("HyM", hyCount)
            mfT = KT_NEW("MfM", mf_mCount)  // 男子气
            ptT = KT_NEW("PtM", ptCount)
            scT = KT_NEW("ScM", scCount)
        } else {
            hsT = KT_NEW("HsF", hsCount)
            dT = KT_NEW("DF", dCount)
            hyT = KT_NEW("HyF", hyCount)
            mfT = KT_NEW("MfF", mf_fCount)  // 女子气
            ptT = KT_NEW("PtF", ptCount)
            scT = KT_NEW("ScF", scCount)
        }


        // 结果解释  数组按 高 低 分排列 使用时注意此顺序          2016-06-06  杨
        // 结果展示
        // L量表 >=70为高L分  <70 低L分
        val yjL = mutableListOf(
            "|【L量表】受试者对症状汇报不真实因而表示答卷相对无效  可能因为受试者对自己要求过高",
            "|【L量表】 受试者易与他人意见一致，但无创造性、压抑、不注意其他人的活动，易发生慌乱糊涂，无内疚感（如为精神科的病人则为自己的夸大妄想有强烈自信）"
        )
        //原始分在1以下,如伴有低K分与高F分表示
        val yjLg = mutableListOf("此人为正常人受良好的教育并争取取得别人领先任的人一般比较诚实、自信，能独立并会自觉承认小缺点等。")
        //如伴有低K分与高F分表示
        val yjLd = mutableListOf("此人为正常人受良好的教育并争取取得别人领先任的人一般比较诚实、自信，能独立并会自觉承认小缺点等。")

        //F量表  >=70为高F分  <70 低F分
        val yjF = mutableListOf("|【F量表】 量表有效，但应考虑精神病或边缘状态的可能。", "|【F量表】 为精神病患者至多为一边缘状态，精神病刚缓解时亦可见到。")

        //高分 F分超过80T分者常见于全部回答均为“否”的人病患者，特别是伴有Sc或Pa在70分以上的人
        val yjFg =
            mutableListOf("诈病、粗心者或精神病患者。急性精神分裂症或其他慢性精神病急性发作。", "诈病、粗心者或精神病患者。急性精神分裂症或其他慢性精神病急性发作。")

        //低分  60—70分之间时，50—60分之间， 60T分以下的低F分，（特别在同时有K分接近60--70T分者）正常的低F分
        val yjFd = mutableListOf(
            "精神病人则说明有新近发生过境遇性精神障碍，如为正常人则提示其家中发生过重大事件如亲人死亡或遭遇横祸（如强奸、杀人）等不幸。",
            "见于偏执性精神病人人格完好或偏执型精神分裂症保持人格完整者。这种人表示有严重的思维障碍或有自己一套妄想，这个妄想已完全逻辑化。",
            "基本正常，受测者一般合群，易与社会观点一致，并试图作一个大家都满意的人。",
            "可能表示谦逊、诚恳、诚实、平静。受测者很少有社会激动，平时愉快或对近来生活满足精神比较松懈的人，一般表现无所需求，对生活问题采取无所谓的态度。"
        )

        val yjK = mutableListOf(
            "|【K量表】 可纠正“是”回答过多，还可能表示那些拒绝承认症状而想“装好”的人，以及“否”回答过多的人。有时为一害羞对自己问题缺乏自觉者。",
            "|【K量表】 低K分可纠正“否”回答过多，常见于“装坏”自杀观念，同性恋者或对自己生活境遇持消极态度的人。处境不顺请求人帮助但又怀疑别人且无自知力的人。常表现对社会反应不满或愤世嫉俗者。与年龄和教育均有关系。年轻的人对生活乐观者常把问题认识不足，受高等教育者则对问题轻视，特别在一个人既年轻又受高等教育时更为明显。"
        )

        //K高分 精神科临床患者高K分，常态的K分
        val yjKg = mutableListOf(
            "特别在一个人既年轻又受高等教育时更为明显。",
            "人格平衡完整实事求是者，受测者常表现独立自信，才艺双全，头脑清醒专心于社会事业者。但有时急性精神病人中亦可有正常的K分。"
        )


        //1、Hs 疑病量表 >=70高 Hs分者 <=70低Hs分者
        val yjHs = mutableListOf(
            "|【Hs 疑病量表】 表明此人异常关心个人身体功能或有模糊的疑病体诉，这些人的体诉包括食欲不振、失眠、疲倦与情绪不高。还可表现被动，退缩并很少与人接触，真正有内科疾病者亦会有高Hs分。",
            "|【Hs 疑病量表】 低分多见于年轻人与乐观情绪的人。这些人常表现敏感、灵活、愉快、合群。提示无神经官能症并能以愉快有效的反应接触别人。"
        )
        //Hs 高分
        val yjHsg = mutableListOf(
            "高于70分的情况需要配合心理辅导。",
            "提示强烈的心因成分，有些身体症状如终日感到疼痛要考虑为椎间盘脱出，或糖尿病等亦可因精神因素而加重。病人的体诉一定要重视以免误为单纯身心疾病。"
        )

        //2、D 抑郁量表
        val yjD = mutableListOf(
            "|【D 抑郁量表】 无积极情绪，一般悲观（对人对己均如此），对既往行为思想表现内疚，可可能有强迫症状。",
            "|【D 抑郁量表】 快活、轻松、活动量高。"
        )
        //D 高分  特别在伴有量表7高分时更是如此，这些特别在T分达80分以上时更为明显,量表7高分其他临床量表必须在70分以下时
        val yjDg = mutableListOf(
            "受测者常自感无用企图从社会活动中退缩，不愿负业务责任，常在睡眠问题上消耗很多时间，有时亦表现犹豫不决。",
            "甚至很小的事情亦难以作出决定。他们常想寻求帮助但不想主动合作。对日常生活事物亦感到受干扰，作一点事就感到严重躯体不适，如果发生拒食时抗抑郁剂非常有效。",
            "有自发缓解。"
        )
        //D 低分  参照量表9（40--70），照量表0分数（<70），如伴有量表4高分，量表2低分如果是测图中最低分（量表5、0除外），并伴有高量表8、9分时
        val yjDd = mutableListOf(
            "受测者愉快欢乐，社会处境悠闲。",
            "这种人负责、伶俐，宜负领导职责。",
            "可能有冲动、不受抑制，与权威冲突亦无罪恶感。",
            "躁狂—抑郁症躁狂相。"
        )

        //3、Hy 癔病
        val yjHy = mutableListOf(
            "|【Hy 癔症量表】 常有婚姻方面的不愉快，易抱怨权威者。但他们与之晤谈时常会取悦对方。在医疗单位中他们会抱怨医生或其他工作人员。常满足自己的生活与现实处境，对权威或领导表示满意。常告诉医生既往的医务人员如何不好而现在的医生显然比较高明，有礼貌，但时过境迁后则又在更新的医生面前抱怨过去。受测者对自己的行为很少有自知力并抱怨别人说他有心理问题。",
            "|【Hy 癔症量表】 常有婚姻方面的不愉快，易抱怨权威者。但他们与之晤谈时常会取悦对方。在医疗单位中他们会抱怨医生或其他工作人员。"
        )

        //Hy 高分   量表3高分者并伴有K量表与L量表高分
        val yjHyg = mutableListOf("可能是癔症")


        //4、Pd 精神状态
        val yjPd = mutableListOf(
            "|【Pd 精神状态量表】  常有复仇攻击观念，往往在15岁前即开始出现行为越规。不能接受同伴，结婚与工作均发生困难，判断不良，行为呈冲动性，亦有为自爱自怜、自我中心。受测者遇有压力时有自杀姿态但并不抑郁，对人有报复心，当别人不支持他们的目的时常会受到报复。可能表现合群，对他人感兴趣，但这种兴趣只限于企图在他人身上取得利益时，这种目的往往为暂时的冲动性的，有些可能智力可很高，但玩世不恭，常侵犯别人，易发生烦恼，特别是在境遇中某些事件影响其生活时，有些可能亦可把专横、权力、合群等人格特点转变为社会的积极因素方面，这种人亦可能成为很有成就的心理学家、医生。成功的售货员，以及其他与人打交道的职业，此时临床上不宜诊断为Pd。",
            "|【Pd 精神病态】常表现易与社会要求协调一致，对权威顺从，但被动无能，安全意识强烈，无创造性，对性问题不感兴趣，柔顺怕负责任，对稍微违反常规即深感内疚者。"
        )


        //5、Mf_M 男子气
        val yjMf_M = mutableListOf(
            "|【Mf_M 男子气量表】 男性特点不明显，对负起男性责任有不安全感。甚至为一同性恋者（检验者应注意此项结论须慎重）",
            "|【Mf_M 男子气量表】 自认为有丈夫气，易保持传统的男子气框框，常过份强调体力、专横、喜寻求刺激进行冒险活动。这种人可能表现粗野，但内心却又怀疑自己的男子气，即想象中自己很有丈夫气而实际却很懦弱，有些人亦可能缺乏自知，精神松驰，合群。"
        )

        //5、Mf_F 女子气
        val yjMf_F = mutableListOf(
            "|【Mf_F 女子气量表】 可能拒绝承认典型的女性气质，这种女性对所谓的男性体育运动或活动（包括工种）爱好、专横、好竞争、粗鲁。",
            "|【Mf_F 女子气量表】  女子气很重，常自认为“女人家”而被动、屈从、柔顺，不愿与男子相比，对自己能力不信任，在职业妇女中尤其如此。"
        )

        //Mf_F 女子气 高分  F量表高分者
        val yjMf_Fg = mutableListOf("常主动要求男子竞赛，以显示她们与男子相象或更好一些。")

        //Mf_F 女子气 低分 F量表低分者，如合并量表4、6分升高
        val yjMf_Fd =
            mutableListOf("亦可能表现敏感庄重、拘谨、自爱自怜。还可能表现过分自我克制，富有理想。", "表现多疑特别对个人能力显明。这种人有时还伴有Hy量表表高分。")


        //6、Pa 妄想症
        val yjPa = mutableListOf(
            "|【Pa 妄想症】 可能有偏执妄想。常有与性有关的牵涉事件与成见。",
            "|【Pa 妄想症】 表现敏感，可信赖，尽职守信。肯干有广泛兴趣、智慧、人格平衡、合作、通情达理，但缺乏自信，或者顺从但易忧虑。"
        )

        //Pa 高 特别是合并F量表分数与Sc量表分数升高者，量表6高分者，Pa量表在75T分以上时称为高Pa分如效度量表在有效分内（70T分以下），如有一量表6分升高者，Pa分升高低于75分者必须有Sc量表高分时才可诊断，有些Pa量表高分者
        val yjPag = mutableListOf(
            "常有形式思维障碍及抑郁感这种特别常易投射到他人身上而自己无罪恶感及责任感。",
            "受测者几乎都有精神病行为。病人往往有关系妄想，夸大或被害妄想，思维障碍大多存在（虽然有时仅在萌芽状态）。这种人易发怒或害怕、报复别人，故对周围的人往往有害。偏执型精神分裂症患者常有此量表分升高。",
            "常怀疑有人追踪他而从楼上跳下逃跑。",
            "受测者有时表现正常，但敏感多疑，仇恨心很强，多抱怨责怪他人。发生失败时埋怨别人。",
            "由于提防别人的警惕性很高与之晤谈时常撒谎，对不同的调查者可编造不同的故事，人格僵硬者对精神治疗效果不佳。"
        )

        //Pa 低 精神科的病人（或社会适应有缺陷的人）Pa量表分在35—50T分者，如合并Pd量表分升高则，Pa量表分低于35T分者特别为精神科病人，他们对症状不肯承认以至于Pa量表分很低 量表6极低分
        val yjPad = mutableListOf(
            "自我中心、高度敏感及多疑。缺乏独立性，成就不大。",
            "可能无自责感。",
            "受测者虽有明显的偏执妄想但态度为自我保护性与退缩的或躲闪性设法回避的。",
            "亦可见于正常人。提示此人不能控制敌对心理或面对现实。这种人易发作阵怒以表给对方压力。",
        )


        //7、Pt 精神衰弱
        val yjPt = mutableListOf(
            "|【Pt 精神衰弱】 有强迫观念，非常焦虑，高度紧张等反应。常见于神经过敏，易激动的人并有明显的身体或生理表现如焦急出汗心悸恐慌等。常表现集中注意困难，思考问题作计划、理解与识别周围事物动向均有困难。这样就更增加其焦虑。常有罪恶感、抑郁及十全十美的要求自己及客观事物并缺乏自信。常表现过份的讲求条理、干净、整齐注意细节、谨小慎微。但是他们缺乏创造或机智，遇事常常很久不能决断或踌躇不前。对每样事情都感到发愁行动困难。还可有各种身体问题经常把注意集中在心脏、肠道、胃等内脏方面或诉失眠、头痛、衰弱等。这种人如为支气管喘哮患亦可因焦虑引起发作。还可应用自己的智慧与理由化能力作为保护机制使他们难于进行心理治疗。",
            "|【Pt 精神衰弱】 常见于安静、气量宽大、能适应别人而无神经质倾向。这种人一般正视现实有效地掌握境遇方面的压力，能中肯地估计功过，得到赏识与一定的社会地位。"
        )
        //Pt 高 合并D量表高分
        val yjPtg = mutableListOf("还有抑郁。")

        //8、Sc 精神分裂症
        val yjSc = mutableListOf(
            "|【Sc 精神分裂症】 ",
            "|【Sc 精神分裂症】 成熟，适应良好，具有这种量表的人表现谨慎、实际，人格比较平衡，勇于负责。但保守，避免竞争性境遇而又关心权力地位。"
        )

        //Sc 高 70—80分，81--100，101以上， Sc量表T分在70分以上 常与F量表高分（70分以上）合并发生 如只有量表8高分而无F量表T分升高
        val yjScg = mutableListOf(
            "常为慢性精神分裂症患者。",
            "有急性发作或急性症状恶化。",
            "常出现胡乱作答、伪装坏人的测试。",
            "类分裂性人格。这种人朋友很少、孤独，常受人误解而遭厌弃。他们常避免接触新环境或与人有情感接触的境遇。"
        )

        //9、Ma 轻躁狂
        val yjMa = mutableListOf(
            "|【Ma 轻躁狂】可有思维混乱、妄想、偏执（固执）。人格解体，记忆不良，或定向障碍与集中注意困难。常表现友好、合群，爱交际，喜欢围绕人群活动。幽默，喜开玩笑，这个特点与其他精神病明显不同。躁狂发作当病人可发怒、专横并带有偏执妄想，常用不现实的计划去追求将来的权力，躁狂阶段对抑郁发作时的反应常采取否定态度。这种人有时伴有D量表高分。",
            "|【Ma 轻躁狂】提示能力低下 ，这种人嗜睡、无力、慢性疲劳，如无严重抑郁他自己的描述尚可靠时，他们常表现忠诚、庄重、自制力较强。但亦可能表现为退缩，与人相处不大受欢迎（常伴有Si量表与D量表的高分）。",
            "|【Ma 轻躁狂】正常状态。"
        )

        //Ma 高 特别在T分超过80同时又合并Sc量表高分者，Ma量表T分超过90者
        val yjMag = mutableListOf("亦可有幻觉出现。", "常见于躁抑郁症或精神分裂症情感型患者。这类患者对锂盐治疗一般反应良好。")

        //0、Si 社会内向
        val yjSi = mutableListOf(
            "|【Si 社会内向】一般表现退缩，社会活动可能只是与一两个朋友或一小撮人群来来往。这种人常有乏社交倾向。",
            "|【Si 社会内向】无病理状态反应。合群、外向、活跃、智慧，可以信赖的人。对领导职务感兴趣。"
        )

        //Si 高 Si表高度高分者常与Sc量表合并存在，特别在合并量表9低分时更为明显，如合并量表2高分
        val yjSig = mutableListOf(
            "有明显分裂性人格。",
            "他们倾向谨慎、保守，很少去带头作一些事，但参加的工作亦会有个人的成就。这种人常有各种抑郁经验以致D量表分升高。",
            "易怒，情绪低沉。"
        )

        //Si 低 低于40，特别是合并量表4高分
        val yjSid = mutableListOf(
            "对异性相处良好，但这种人亦可能表现稚气易冲动或自我放纵。与人交往时有强烈的表面关系。",
            "这些人亦可能为老练的机会主义者遇有失败时易抱怨他人。"
        )


        // 两点划分法
        //  12/21   公用，躯体问题可能为功能的或器质的 如出现1、2、3三联高点，量表2>1，如为1、2、7高分，如为1、2、4；1、2、6或1、2、6、4高分，如为1、2、8高分
        val ld1221 = mutableListOf(
            "常有躯体疾病伴抑郁情绪,亦可见于近期发生急性意外的人。其抑郁是急性的。为对严重损伤的一种反应，客观上可见到其身体损伤以及近期发生疼痛的疾患。可诊断为疑病性焦虑，或抑郁性神经症。",
            "肯定为疑病患者。",
            "常与防御功能衰竭有关。在起病时抑郁有焦急与急性严重的体诉。亦常见于器质性慢性疼痛患者，但亦可能为目前突然加重的疼痛。",
            "可诊断为焦虑性神经症。",
            "提示有人格障碍，但不诊断为病态人格，而应提示为被动专横人格，急性抑郁状态伴依赖性人格。",
            "并伴有F量表高分者可诊断为精神分裂症未分化型。"
        )

        // 13/31`公用，有量表2低分， D  2 量表分数较高者，如量表2T分特别低(<=35)，在量表2比量表1，在量表6低分或相对高分(比1、3都高)时多见，如量表6T分很低（35），在量表4出现高分时
        val ld1331 = mutableListOf(
            "受测者与相处关系肤浅，缺乏与异性打交道的技术。",
            "被测者常有许多体诉，并有典型的转换性癔症表现。亦可见于许多慢性疼痛患者。常见的器质性原因为髓核脱出或椎间盘破裂，被测者常无焦虑反应。",
            "在境遇压力下可能有焦虑经验。并且可能有器质性损伤存在。",
            "可考虑转换性癔症。",
            "可作为典型转换型癔症的诊断。",
            "常压抑个人仇恨。",
            "有强烈的主动压抑倾向。",
            "表现为病人遇到极严重的困难时过份控制自己，并能在一阵“阵怒”中把仇恨释放出来。有时有杀人或自杀企图。受测者可诊断为疑病性。"
        )

        // 14/41  公用，量表0分数低时，依量表5的T分低分而定，量表4高分
        val ld1441 = mutableListOf(
            "严重疑病并有些做作。行为中乏社交征象很少。",
            "受测者可能表现外向",
            "但不善与异性相处",
            "可能有酗酒、药瘾、工作成绩不良、或婚姻不愉快等历史。一般诊断为疑病性神经症伴人格障碍。但常须参照其他临床量表的情况。"
        )

        //18/81   公用，伴有F量表高分，（与量表7 T分有关）Pt量表分数接近1、8测图（1和8的平均分 上下浮动3）
        val ld1881 = mutableListOf(
            "受测者可能易出现思想混乱，注意不能集中。",
            "可诊断为精神分裂症。受测者在遇到麻烦掌握境遇方面的压力与焦虑情绪时可有清楚的妄想，特别是关于身体功能或身体疾病方面，他们常常不相信别人而出现偏执。",
            "能的诊断为疑病或焦虑性神经症。"
        )

        //19/91   公用，量表9的分数(9>1)，特别在合并量表4高分与量表6低分者
        val ld1991 = mutableListOf(
            "可有许多体诉，但人格是外向的。话多、善交际、性格开朗。其基本人格为被动的、对自己要求较高，但其目的不明确且常不现实。亦见于身体损伤的反应时。",
            "反映基本人格。有时这种人甚至放弃基本需要用来证明他们是坚强独立的。",
            "其诊断有疑病性躁狂，或被动—专横性人格。"
        )

        //23/32   公用，有时可合并量表5、6 T分相对增高 在量表0中亦可找到与之相关的情况，如有F量表高分或量表8高分，
        val ld2332 = mutableListOf(
            "常常易倦、疲劳、衰弱、抑郁、焦虑、不能自己照顾自己，全靠别人服待他。有些人的躯体症状很多，但多变而且不持久。一般表现不成熟、稚气、表达自己的感觉困难，他们亦可有不安全感，适应社会困难，不能与异性打交道。",
            "可诊断为神经症性抑郁。",
            "诊断为精神病性抑郁，对心理治病反应欠佳。"
        )

        //24/42   公用，量表2高分,量表4高分，如伴有量表6高分，量表6低分，量表9高分与量表6同时高分时，9高分与量表6同时高分时分数如接近90分时(F量表与量表8 T分的低分)，Sc量表T分较高，2、4、9、8高分，量表2T分常高于量表1、3
        val ld2442 = mutableListOf(
            "常有人格方面的问题。",
            "反社会人格可能发生过受法律制裁的事件（如逮捕）因而抑郁。",
            "慢性抑郁，可表现为极度对立情绪与敌意。",
            "对立情绪可公然表现，这些一般不易被社会常规或心理卫生工作者识别出来，行为根基很深可有犯罪、药瘾或酒瘾，性生活乱七八糟。",
            "对立情绪被压抑下来，这些一般不易被社会常规或心理卫生工作者识别出来，行为根基很深可有犯罪、药瘾或酒瘾，性生活乱七八糟。",
            "十分危险。",
            "其行为往往是精神病或精神病先兆。",
            "可能有自杀行动。",
            "受测者如有罪恶感其真诚程度有限，由于眼前的困难，可一时表现很好，一旦困境解除而又重返老路。因而其个人的工作史，或在校表现以及婚姻史等均不佳。",
            "常有人格障碍，被动专横。或混合有反社会特点。",
            "有时伴有急性抑郁。"
        )

        //26/62   公用，有F量表T分升高 量表8、9T分升高，量表6或F量表仅有60分左右，在6>4>2
        val ld2662 = mutableListOf(
            "有偏执倾向。",
            "精神病性测图。",
            "妄想伪装偏执。",
            "受测者常有专横、敏感以及抑郁。抑郁常与躯体疾患有关。病人易抱怨别人，与人相处困难，易发生人际关系紧张。诊断有抑郁性神经症，被动专横人格，偏执状态，或早期的偏执型精神分裂症，少数病例为更年期偏执。"
        )

        val ld2772 = mutableListOf(
            "受测者抑郁，焦急不安，并有些神经质。有些人还有一般无力与疲劳症状。这种人特别需要识别，通常郁抑情绪与其失败不相称（抑郁高于失败）。受测者为僵硬的、强迫行为以及要求十全十美者，他们在控制对立情绪时很困难。",
            "亦可能有神经衰弱症状。",
            "受测者可诊断为抑郁性神经症、焦虑性神经症。",
            "亦可诊断为精神病性抑郁。"
        )

        //28/82   公用，F量表T分高于70，2>8>7
        val ld2882 = mutableListOf(
            "受测者常诉抑郁、焦虑，以及睡眠障碍。有思想混乱及记忆障碍等特点，亦可能有躯体疾病而发生严重抑郁。受测者多为依赖性无能的人。情感不大暴露，并且多疑。",
            "可诊断为精神病性抑郁（躁抑性精神病）更年期抑郁，或分裂性情感性精神病，如这种测图不能提示精神病，可诊断为分裂性人格带抑郁，或抑郁性神经症。",
            "要注意自杀企图。"
        )

        //29/92   公用，年青人(18~35),老人(60),量表9 T分高,量表2、9T分均高
        val ld2992 = mutableListOf(
            "常为躁狂患者但有抑郁发作。躁狂常掩盖抑郁，这种人表面看来正常，如无明显躁狂，这种人也可表现紧张不安。",
            "自己对社会起的作用无信心。",
            "可能为对躯体衰残的反应或为更年期抑郁患者。",
            "受测者对自己的能力限制采取否认态度。",
            "常见的诊断为躁抑性精神病与循环性人格。"
        )

        //34/43   公用，量表4的T分高于量表3的T分 或两个量表基本相等,女性患者还可能为4>3 伴有量表5低分
        val ld3443 = mutableListOf(
            " 受测者以慢性严重的易怒为特征。他们常惹麻烦。他们对自己的敌对情绪来源无清楚的认识。",
            "表现为易暴发脾气呈周期性阵怒。这种人易发生内科疾病包括头痛、月经问题、与其他转换性或心身疾病。病人的躯体疾患期与情感暴发期交替发作。躯体疾患期间病人表现合群、合作，对人友好。在情感暴发期间则敌对情绪常向家中人发作。许多人有慢性婚姻困难、反社会行为包括犯罪、行为粗野残酷。",
            "表面诱人男女关系乱七八糟，但有时有暴发式自杀行为或有药瘾与酒精中毒。诊断有癔症性人格、混合性人格障碍，被动专横人格，暴发性人格。常有双亲嗜酒的家族史。"
        )

        //36/63   公用，与34/43测图相似((3>6&&3>4)||(3<6&&3<4))
        val ld3663 = mutableListOf(
            "可能有躯体症状如胃肠功能紊乱、焦虑加神经质。",
            "有慢性逐渐增重的敌对情绪，特别对家中人明显。但这种感觉并不公开承认，受测者表现僵硬，拒绝别人建议，否认个人有心因问题。受测者自我中心、易抱怨人、有敌对心理、否认对别人有报复、喜借题发挥。其婚姻方面常不协调。"
        )

        //38/83   公用，在F量表、Sc量表T分都不超过70分时
        val ld3883 = mutableListOf(
            "有焦虑与抑郁感，大多数人可能有体诉。思维方式往往表现很不寻常，有强烈的压抑与防御。常有妄想与注意集中困难。有明显的社会退缩。由于严重退缩缺乏合作有时妄想与其他怪异行为，可不太明显。受测者严重的丧失自知力、表现不成熟、依赖，不能对自己作出合理计划。",
            "常见的诊断有精神分裂症，或癔症神经症。"
        )

        //39/93   公用，量表9高分
        val ld3993 = mutableListOf(
            "常有相当大的敌对情绪或仇恨心理。急性焦虑发作，内科体诉有明显的癔症表现。",
            "可能为掩盖实质上依赖人格的表现。专横、傲慢、自我中心、爱慕虚荣。他们相信别人喜欢他而实际正是别人在批评他，因而事实上正是他缺乏自知力的表现。受测者可能呈周期性抑郁，诊断有癔证性神经症、焦虑性神经症。被动—专横或专横型人格。"
        )

        //45/54   公用，量表4、5超过70分以上，量表4、5超过70分以上女性，量表4、5超过70分以上男性
        val ld4554 = mutableListOf(
            "可能有明显性心理活动障碍。",
            "同性恋或其他性心理异常。",
            "常有不寻常的专横行为。受测者与众不同但很少犯罪。如失职、违法。有时受测者表现的行为有些越轨，但多为处于不得已情境下或酒精中毒下发生。受测者可有犯罪感但为一时性，很少能影响将来的行为。",
            "被动，依赖及自我中心。受测者与众不同但很少犯罪。受测者与众不同但很少犯罪。如失职、违法。有时受测者表现的行为有些越轨，但多为处于不得已情境下或酒精中毒下发生。受测者可有犯罪感但为一时性，很少能影响将来的行为。"
        )

        //46/64   公用，同时伴有量表9高分,4>6型高分,量表9的T分高于量表4 T分或两者均高于70分
        val ld4664 = mutableListOf(
            "受测者多为被动—依赖性人格。对别人要求多，但责怪别人对他提出要求。他们常抑郁、易激惹，退缩与大惊小怪。常有压抑的敌对情绪。",
            "受测者常易发生各种报复行为。",
            "时常为自我中心，不成熟，不易形成较深的复杂情感，他们需要的支持与过多的要求很难满足，结果能使他们愉快的事情很少，因而婚姻问题（或家庭问题）与工作问题都很多。他们常常把这些问题推诿（或投射）到别人身上。",
            "可能的诊断有偏执型精神分裂症、更年期偏执或被动—专横人格。"
        )

        //47/74   公用，4>7,7>4
        val ld4774 = mutableListOf(
            "受测者对别人不大敏感，不注意别人的情感反应。",
            "但有高度对自己行为的罪恶感倾向，常易发生自己抱怨自己。",
            "常常反复地表现循环行为：行为进行期与自罪懊恼期，交替、循环。行为进行期时可能表现滥交、醉酒。以后懊悔烦恼。但不能预防以后的重复发生。当处于懊悔时期时可能有不明确的体诉。"
        )

        //48/84   公用，8>4
        val ld4884 = mutableListOf(
            "行为好像很怪，很特殊，常有不寻常的宗数仪节动作，他们的行为飘忽不定不可能捉摸亦可能干出一些反社会行为（用一些不正当的手段作出某种违社会要求的行为）。这种行动一般无计划性或呈冲动性，但作法很恶毒。因而这些人在社会上可能是不安全的。有性倒错或强迫性、社会退缩或孤独。",
            "还可有思维障碍、记忆障碍、思想混乱与其他精神分裂的症状等都很突出。这些人一般诊断为精神分裂症，还可能有强烈的偏执妄想。如无精神病症状亦很可能为其他精神病的先兆。"
        )

        //49/94    公用，T分亦可能有80—90分者
        val ld4994 = mutableListOf(
            "常有违反社会要求的行为，经常表现躁狂、易怒、粗暴、外向、世故、能量很大。常为冲动的、无预见性的。不能推迟等待个人渴求，自我中心。此外日常生活方面还表现不成熟、不负责任、忧郁、怨恨，但野心勃勃。他们有很深的复仇感（有时这种复仇感还可能得逞），他们还可能采取短路方式即使已被捉住只要有机会还会重犯。常见的诊断为反社会人格。",
            "或与反对权威建立的倾向有关而不是特殊的反社会行为。"
        )

        //68/86   公用，<30岁,68量表分均升高超过70 F分亦超过70,F分未升高 68量表分稍升一点,
        val ld6886 = mutableListOf(
            "受测者表现多疑、不信任、缺乏自信与自我评价。他们对每日的生活表现退缩、情感平淡、思想混乱，并有偏执妄想，不能与别人保持密切联系常与现实脱节。亦可见于甲状腺疾患。受测者的治疗须用抗精神病药物。",
            "经常退入幻想世界之中，可出现认识衰退。",
            "偏执型精神分裂症。",
            "可诊断偏执状态或分裂性人格。妄想内容可包括一些病人具体的生活实况。"
        )

        //69/96   公用，F量表高分,F量表与Sc量表的T分很高,量表8低分
        val ld6996 = mutableListOf(
            "可表现极度焦虑、神经过敏，并有全身发抖等体征，当受到威胁时易退缩到幻想中去。他们有时表现过分自我克制，但另一些时候则表现有躁狂情绪而不能控制自己。",
            "可有强烈的思维意识缺陷。强迫观念亦很常见。",
            "强迫观念亦很常见。",
            "可无明显的思想混乱。此时可诊为躁郁性精神病。受测者锂盐与抗精神病药物联合治疗有效。"
        )

        //78/87   公用，如量表8与7之间分数相差多,量表8 T分很高,87测图较其余各量表分数高,全部量表分数均高,8>100
        val ld7887 = mutableListOf(
            "常有高度激动与烦躁不安等症状。缺乏掌握环境压力的能力，可能有防御系统衰竭表现。这些人常有不安全感，犹豫不决，左右摇摆，混乱焦急和社交笨拙等特点。他们行为被动、行动困难，与异性交往时尤其明显。有时可出现强迫观念和其他精神病等症状如混乱、幻觉或妄想。亦可有内疚抑郁与过分害怕等。有时有过度饮酒或滥用药物。",
            "诊断为焦虑性神经症。但亦可能为其他急性精神病后遗状态（或康复不全）。因为急性精神病患者发病时不能接受测验，只有在激动症状过去后测试才有这样的结果。",
            "可能为精神分裂症。",
            "可能为一急性精神病患者。",
            "提示为一慢性精神病患者。",
            "可能为一胡乱的回答或“装坏（faking  bad）”"
        )

        //89/98   公用，量表9高分
        val ld8998 = mutableListOf(
            "倾向于活动过度、能力充沛、情感不稳、不现实及夸大妄想。",
            "尤其是关于生活、对人接物及个人的重要性方面总是夸大自己的作用。对自己的缺点缺乏自知力，对住院不满。他们需要别人注意、表现不成熟的稚气。他们不能与别人形成密切的社会关系。并有明显的思维障碍出现。诊断有精神分裂症与躁郁症。确切的诊断取决于思维障碍与情感障碍的比重，分裂情感性精神病亦有可能。这种病人需要抗精神药物治疗。当出现明显的幻觉或妄想时尤其需要。"
        )


        var yj = StringBuffer() //意见列表
        val k = kT >= 70 //K高低分判断
        val f = fT >= 70 //F高低分判断
        val sc = scT >= 70 //Sc高低分判断
        val pa = paT >= 70 //Pa高低分判断
        val isjsb = false //user.contains("精神疾病") || user.contains("心理疾病")
        //1、Hs，2、D，3、Hy，4、Pd，5、Mf，6、Pa，7、Pt，8、Sc，9、Ma，0、Si
        if (noAnswer >= 22) {
            yj.append("答卷无效。下面评估情况，仅供参考。")
        }
        //  各个量表的结果
        val pjz18 = (scT + hsT) / 2
        yj.append("临床量表：")
            .append(yjL[MMPIAssit(lfT)])
            .append(if (lfT < 70 && k && f) yjLd[0] else if (lfT >= 70 && !k && f) yjLg[0] else "")
            .append(yjF[MMPIAssit(fT)])
            .append(
                if (fT < 70) {
                    if (fT >= 60) yjFd[0] else if (fT in 50..59) yjFd[1] else "" + if (fT < 60) yjFd[2] else "" + if (fT < 60 && kCount < 70 && kCount > 60) yjFd[3] else ""
                } else {
                    if (fT >= 70) {
                        if (sc || pa) yjFg[1] else "" + if (fT >= 80) yjFg[0] else ""
                    } else ""
                }
            )
            .append(yjK[MMPIAssit(kT)])
            .append(if (isjsb) yjKg[0] else yjKg[1])
            .append(yjHs[MMPIAssit(hsT)])
            .append(if (hsT >= 70) yjHsg[0] else "")
            .append(if ((age > 18 && age < 35 && hsT >= 65) || (age >= 60 && hsT >= 70)) yjHsg[1] else "")//量表1
            .append(yjD[MMPIAssit(dT)])
            .append(
                if (dT >= 70) {
                    if (ptT >= 70) yjDg[0] else "" + if (ptT >= 80) yjDg[1] else ""
                } else if (dT < 70) {
                    if (maT in 41..69) yjDd[0] else "" + if (siT < 70) yjDd[1] else "" + if (pdT >= 70) yjDd[2] else "" + if (dT < hsT && dT < hyT && dT < pdT && dT < paT && dT < ptT && dT < scT && dT < maT && scT >= 70 && maT >= 70) yjDd[3] else ""
                } else ""
            )//量表2
            .append(yjHy[MMPIAssit(hyT)])
            .append(if (hyT >= 70 && kT >= 70 && lfT >= 70) yjHyg[0] else "")//量表3
            .append(yjPd[MMPIAssit(pdT)])//量表4
            .append(
                if (b) yjMf_M[MMPIAssit(mfT)] else {
                    yjMf_F[MMPIAssit(mfT)] + if (mfT >= 70) {
                        if (f) yjMf_Fg[0] else ""
                    } else if (mfT < 70) {
                        if (!f) yjMf_Fd[0] else "" + if (pdT >= 70 && paT >= 70) yjMf_Fd[1] else ""
                    } else ""
                }
            )//量表5
            .append(yjPa[MMPIAssit(paT)])
            .append(
                if (paT >= 70) {
                    if (f && sc) yjPag[0] else "" + if (pa) yjPag[1] else "" + if (paT >= 75 && lfT < 70 && kT < 70 && fT < 70) yjPag[2] else "" + if (paT < 75 && sc) yjPag[3] else ""
                } else if (paT < 70) {
                    if (isjsb && paT >= 35 && paT < 50) yjPad[0] else "" + if (pdT >= 70) yjPad[1] else "" + if (isjsb && paT < 35) yjPad[2] else "" + if (paT < 35) yjPad[3] else ""
                } else ""
            )//量表6
            .append(yjPt[MMPIAssit(ptT)])
            .append(if (ptT >= 70 && dT >= 70) yjPtg[0] else "")//量表7
            .append(yjSc[MMPIAssit(scT)])
            .append(
                if (scT >= 70) {
                    if (scT >= 70 && scT <= 80) yjScg[0] else if (scT > 80 && scT < 100) yjScg[1] else yjScg[2] + if (!f) yjScg[3] else ""
                } else ""
            )//量表8
            .append(
                if (maT >= 70) yjMa[0] else if (maT < 40) yjMa[1] else yjMa[2] + if (maT >= 70) {
                    if (maT > 80 && sc) yjMag[0] else "" + if (maT > 90) yjMag[1] else ""
                } else ""
            )//量表9
            .append(yjSi[MMPIAssit(siT)])
            .append(
                if (siT >= 70) {
                    if (sc) yjSig[0] else "" + if (maT < 40) yjSig[1] else "" + if (dT >= 70) yjSig[2] else ""
                } else {
                    if (siT < 40) yjSid[0] else "" + if (pdT >= 70) yjSid[1] else ""
                }
            )//量表0
            .append("||...........................||")// 两点划分法
            .append("两点呈现：")
            .append("|【12/21】")
            .append(ld1221[0])
            .append(if (hsT >= 70 && hyT >= 70 && dT >= 70) ld1221[1] else "")
            .append(if (dT > hsT) ld1221[2] else "")
            .append(if (hsT >= 70 && dT >= 70 && ptT >= 70) ld1221[3] else "")
            .append(if ((hsT >= 70 && dT >= 70 && pdT >= 70) || (hsT >= 70 && dT >= 70 && paT >= 70) || (hsT >= 70 && dT >= 70 && pdT >= 70 && paT >= 70)) ld1221[4] else "")
            .append(if (hsT >= 70 && dT >= 70 && scT >= 70) ld1221[5] else "")
            .append("|【13/31】")
            .append(ld1331[0])
            .append(if (dT < 70) ld1331[1] else "")
            .append(if (dT > hsT && hyT < dT) ld1331[2] else "")
            .append(if (dT <= 35) ld1331[3] else "")
            .append(if (dT < hsT) ld1331[4] else "")
            .append(if (paT < 70 || (paT > hsT && paT > hyT)) ld1331[5] else "")
            .append(if (pdT >= 70) ld1331[5] else "")
            .append(if (dT >= 70) ld1331[6] else "")
            .append("|【14/41】")
            .append(ld1441[0])
            .append(if (siT < 70) ld1441[1] else "")
            .append(if (mfT < 70) ld1441[2] else "")
            .append(if (pdT >= 70) ld1441[3] else "")
            .append("|【18/81】")
            .append(ld1881[0])
            .append(if (fT >= 70) ld1881[1] else "")
            .append(if (ptT <= pjz18 + 3 && ptT >= pjz18 - 3) ld1881[2] else "")
            .append("|【19/91】")
            .append(ld1991[0])
            .append(if (maT > hsT) ld1991[1] else "")
            .append(if (paT >= 70 && paT < 70) ld1991[2] else "")
            .append("|【23/32】")
            .append(ld2332[0])
            .append(if (mfT >= 70 && paT >= 70) ld2332[1] else "")
            .append(if (fT >= 70 || scT >= 70) ld2332[2] else "")
            .append("|【24/42】")
            .append(ld2442[0])
            .append(if (dT >= 70) ld2442[1] else "")
            .append(if (pdT >= 70) ld2442[2] else "")
            .append(if (paT >= 70) ld2442[3] else "")
            .append(if (paT < 70) ld2442[4] else "")
            .append(if (paT >= 70 && maT >= 70) ld2442[5] else "")
            .append(if (paT >= 85 && maT >= 85 && fT >= 70 && scT >= 70) ld2442[6] else "")
            .append(if (scT >= 70) ld2442[7] else "")
            .append(if (dT >= 70 && pdT >= 70 && scT >= 70 && maT >= 70) ld2442[8] else "")
            .append(if (dT >= 70 && pdT >= 70 && scT >= 70) ld2442[9] else "")
            .append(if (dT > hsT && dT > hyT) ld2442[10] else "")
            .append("|【26/62】")
            .append(ld2662[0])
            .append(if (fT >= 70 && scT >= 70 && maT >= 70) ld2662[1] else "")
            .append(if ((paT < 65 && paT > 55) || (fT < 65 && fT > 55)) ld2662[2] else "")
            .append(if (paT > pdT && pdT > dT) ld2662[3] else "")
            .append("|【27/72】")
            .append(ld2772[0])
            .append(if (hsT >= 70 && dT >= 70 && hyT >= 70) ld2772[1] else "")
            .append(if (paT >= 70 || (paT >= 70 && hyT >= 70 && pdT >= 70)) ld2772[2] else "")
            .append(if (fT >= 70) ld2772[3] else "")
            .append("|【28/82】")
            .append(ld2882[0])
            .append(if (fT >= 70) ld2882[1] else "")
            .append(if (dT > scT && scT > ptT) ld2882[2] else "")
            .append("|【29/92】")
            .append(ld2992[0])
            .append(if (age >= 18 && age < 35) ld2992[1] else "")
            .append(if (age >= 60) ld2992[2] else "")
            .append(if (maT >= 70) ld2992[3] else "")
            .append(if (dT >= 70 && maT >= 70) ld2992[4] else "")
            .append("|【34/43】")
            .append(ld3443[0])
            .append(if (pdT > hyT || abs(pdT - hyT) == 1) ld3443[1] else "")
            .append(if (!b && pdT > hyT && mfT < 70) ld3443[2] else "")
            .append("|【36/63】")
            .append(ld3663[0])
            .append(if ((hyT > paT && hyT > pdT) || (hyT < paT && hyT < pdT)) ld3443[1] else "")
            .append("|【38/83】")
            .append(ld3883[0])
            .append(if (fT < 70 && scT < 70) ld3883[1] else "")
            .append("|【39/93】")
            .append(ld3993[0])
            .append(if (maT >= 70) ld3993[1] else "")
            .append("|【45/54】")
            .append(ld4554[0])
            .append(if (pdT >= 70 && mfT >= 70) ld4554[1] else "")
            .append(if (!b && pdT >= 70 && mfT >= 70) ld4554[2] else "")
            .append(if (b && pdT >= 70 && mfT >= 70) ld4554[3] else "")
            .append("|【46/64】")
            .append(ld4664[0])
            .append(if (maT >= 70) ld4664[1] else "")
            .append(if (pdT > paT) ld4664[2] else "")
            .append(if (maT > pdT || (maT > 70 && pdT > 70)) ld4664[3] else "")
            .append("|【47/74】")
            .append(ld4774[0])
            .append(if (pdT > ptT) ld4774[1] else "")
            .append(if (pdT < ptT) ld4774[2] else "")
            .append("|【48/84】")
            .append(ld4884[0])
            .append(if (scT > pdT) ld4884[1] else "")
            .append("|【49/94】")
            .append(ld4994[0])
            .append(if ((maT in 81..89) || (pdT in 81..89)) ld4994[1] else "")
            .append("|【68/86】")
            .append(ld6886[0])
            .append(if (age > 30) ld6886[1] else "")
            .append(if (paT > 70 && scT > 70 && fT > 70) ld6886[2] else "")
            .append(if (paT > 70 && scT > 70 && fT < 70) ld6886[3] else "")
            .append("|【69/96】")
            .append(ld6996[0])
            .append(if (fT >= 70) ld6996[1] else "")
            .append(if (fT > 70 && scT > 70) ld6996[2] else "")
            .append(if (scT < 70) ld6996[3] else "")
            .append("|【78/87】")
            .append(ld7887[0])
            .append(if (abs(ptT - scT) >= 5) ld7887[1] else "")
            .append(if (scT >= 80) ld7887[2] else "")
            .append(if (scT > hsT && scT > dT && scT > hyT && scT > pdT && scT > mfT && scT > paT && scT > maT && scT > siT && ptT > hsT && ptT > dT && ptT > hyT && ptT > pdT && ptT > mfT && ptT > paT && ptT > maT && ptT > siT) ld7887[3] else "")
            .append(if (hsT >= 70 && dT >= 70 && hyT >= 70 && pdT >= 70 && mfT >= 70 && paT >= 70 && maT >= 70 && siT >= 70) ld7887[4] else "")
            .append(if (scT >= 100) ld7887[5] else "")
            .append("|【89/98】")
            .append(ld8998[0])
            .append(if (maT >= 70) ld8998[1] else "")


        //加K原始分
        val countKList = mutableListOf(lfCount, fCount, kCount, hsCount, dCount, hyCount, pdCount)
        //男子气 女子气
        if (b) countKList.add(mf_mCount) else countKList.add(mf_fCount)
        countKList.add(paCount)
        countKList.add(ptCount)
        countKList.add(scCount)
        countKList.add(maCount)
        countKList.add(sitCount)
        countKList.add(noAnswer)
        //未加K T分
        val tList = mutableListOf(lfCount, fCount, kCount, tHs, dCount, hyCount, tPd)
        //男子气 女子气
        if (b) tList.add(mf_mCount) else tList.add(mf_fCount)
        tList.add(paCount)
        tList.add(tPt)
        tList.add(tSc)
        tList.add(tMaMF)
        tList.add(sitCount)
        tList.add(noAnswer)
        //加K 的  T分
        val tKlist =
            mutableListOf(lfT, fT, kT, hsT, dT, hyT, pdT, mfT, paT, ptT, scT, maT, siT, noAnswerT)
        val ysm = mutableListOf("说谎", "诈病", "校正", "疑病", "抑郁", "癔病", "精神病态", "男子气-女子气", "妄想症", "精神衰弱", "精神分裂症", "轻躁狂", "社会内向", "无法作答").joinToString("|")
        val bzf = countKList.joinToString("|")
        val jf = tList.joinToString("|")
        val bzc = tKlist.joinToString("|")
        if (lfCount >= 22) {
            yj = StringBuffer("由于您对症状汇报的不真实因而系统默认为答卷无效。")
        }
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = ysm,
            BZF = bzf,
            JF = jf,
            YJ = yj.toString(),
            BZC = bzc,
            XX = answers.joinToString("|")
        )

    }

    private fun KT_NEW(columnName: String, protypeVal: Int): Int {
        //Key 原始分 Value T分
        val dict = HashMap<Int, Int>()
        // 原始分和T分的转换表
        when (columnName) {
            "NoneMF" -> {
                // 无法作答
                dict.put(0, 41)
                dict.put(1, 42)
                dict.put(2, 42)
                dict.put(3, 42)
                dict.put(4, 42)
                dict.put(5, 43)
                dict.put(6, 43)
                dict.put(7, 43)
                dict.put(8, 43)
                dict.put(9, 43)
                dict.put(10, 44)
                dict.put(11, 45)
                dict.put(12, 45)
                dict.put(13, 45)
                dict.put(14, 45)
                dict.put(15, 46)
                dict.put(16, 46)
                dict.put(17, 46)
                dict.put(18, 46)
                dict.put(19, 46)
                dict.put(20, 47)
                dict.put(21, 48)
                dict.put(22, 48)
                dict.put(23, 48)
                dict.put(24, 48)
                dict.put(25, 49)
                dict.put(26, 49)
                dict.put(27, 49)
                dict.put(28, 49)
                dict.put(29, 49)
                dict.put(30, 50)
                dict.put(31, 51)
                dict.put(32, 51)
                dict.put(33, 51)
                dict.put(34, 51)
                dict.put(35, 52)
                dict.put(36, 52)
                dict.put(37, 52)
                dict[38] = 52
                dict.put(39, 52)
                dict.put(40, 53)
                dict.put(41, 54)
                dict.put(42, 54)
                dict.put(43, 54)
                dict.put(44, 54)
                dict.put(45, 55)
                dict.put(46, 55)
                dict.put(47, 55)
                dict.put(48, 55)
                dict.put(49, 55)
                dict.put(50, 56)
                dict.put(51, 57)
                dict.put(52, 57)
                dict.put(53, 57)
                dict.put(54, 57)
                dict.put(55, 58)
                dict.put(56, 58)
                dict.put(57, 58)
                dict.put(58, 58)
                dict.put(59, 58)
                dict.put(60, 58)
                dict.put(61, 59)
                dict.put(62, 59)
                dict.put(63, 59)
                dict.put(64, 59)
                dict.put(65, 60)
                dict.put(66, 60)
                dict.put(67, 60)
                dict.put(68, 60)
                dict.put(69, 60)
                dict.put(70, 61)
                dict.put(71, 62)
                dict.put(72, 62)
                dict.put(73, 62)
                dict.put(74, 62)
                dict.put(75, 63)
                dict.put(76, 63)
                dict.put(77, 63)
                dict.put(78, 63)
                dict.put(79, 63)
                dict.put(80, 64)
                dict.put(81, 65)
                dict.put(82, 65)
                dict.put(83, 65)
                dict.put(84, 65)
                dict.put(85, 66)
                dict.put(86, 66)
                dict.put(87, 66)
                dict.put(88, 66)
                dict.put(89, 66)
                dict.put(90, 66)
                dict.put(91, 67)
                dict.put(92, 67)
                dict.put(93, 67)
                dict.put(94, 67)
                dict.put(95, 68)
                dict.put(96, 68)
                dict.put(97, 68)
                dict.put(98, 68)
                dict.put(99, 68)
                dict.put(100, 69)
                dict.put(101, 70)
                dict.put(102, 70)
                dict.put(103, 70)
                dict.put(104, 70)
                dict.put(105, 71)
                dict.put(106, 71)
                dict.put(107, 71)
                dict.put(108, 71)
                dict.put(109, 71)
                dict.put(110, 72)
                dict.put(111, 73)
                dict.put(112, 73)
                dict.put(113, 73)
                dict.put(114, 74)
                dict.put(115, 74)
                dict.put(116, 74)
                dict.put(117, 75)
                dict.put(118, 75)
                dict.put(119, 75)
                dict.put(120, 76)
                dict.put(121, 77)
                dict.put(122, 77)
                dict.put(123, 77)
                dict.put(124, 78)
                dict.put(125, 78)
                dict.put(126, 78)
                dict.put(127, 79)
                dict.put(128, 79)
                dict.put(129, 79)
                dict.put(130, 80)
            }


            "LMF" -> {
                // 说谎
                dict.put(0, 36)
                dict.put(1, 40)
                dict.put(2, 44)
                dict.put(3, 46)
                dict.put(4, 50)
                dict.put(5, 53)
                dict.put(6, 56)
                dict.put(7, 60)
                dict.put(8, 63)
                dict.put(9, 66)
                dict.put(10, 70)
                dict.put(11, 73)
                dict.put(12, 76)
                dict.put(13, 80)
                dict.put(14, 83)
                dict.put(15, 86)
            }


            "FMF" -> {
                // 诈病
                dict.put(0, 44)
                dict.put(1, 46)
                dict.put(2, 48)
                dict.put(3, 50)
                dict.put(4, 53)
                dict.put(5, 55)
                dict.put(6, 58)
                dict.put(7, 60)
                dict.put(8, 62)
                dict.put(9, 64)
                dict.put(10, 66)
                dict.put(11, 68)
                dict.put(12, 70)
                dict.put(13, 73)
                dict.put(14, 76)
                dict.put(15, 78)
                dict.put(16, 80)
                dict.put(17, 82)
                dict.put(18, 84)
                dict.put(19, 86)
                dict.put(20, 88)
                dict.put(21, 90)
                dict.put(22, 92)
                dict.put(23, 94)
                dict.put(24, 96)
                dict.put(25, 98)
                dict.put(26, 100)
                dict.put(27, 102)
                dict.put(28, 104)
                dict.put(29, 106)
                dict.put(30, 108)
                dict.put(31, 110)

            }

            "KMF" -> {
                // 校正分数
                dict.put(0, 27)
                dict.put(1, 29)
                dict.put(2, 31)
                dict.put(3, 33)
                dict.put(4, 35)
                dict.put(5, 36)
                dict.put(6, 38)
                dict.put(7, 40)
                dict.put(8, 42)
                dict.put(9, 44)
                dict.put(10, 46)
                dict.put(11, 48)
                dict.put(12, 49)
                dict.put(13, 51)
                dict.put(14, 53)
                dict.put(15, 55)
                dict.put(16, 57)
                dict.put(17, 59)
                dict.put(18, 61)
                dict.put(19, 62)
                dict.put(20, 64)
                dict.put(21, 66)
                dict.put(22, 68)
                dict.put(23, 70)
                dict.put(24, 72)
                dict.put(25, 74)
                dict.put(26, 75)
                dict.put(27, 77)
                dict.put(28, 79)
                dict.put(29, 81)
                dict.put(30, 83)
            }


            "HsM" -> {
                // 疑病-男
                dict.put(0, 21)
                dict.put(1, 23)
                dict.put(2, 26)
                dict.put(3, 29)
                dict.put(4, 31)
                dict.put(5, 34)
                dict.put(6, 36)
                dict.put(7, 39)
                dict.put(8, 41)
                dict.put(9, 44)
                dict.put(10, 46)
                dict.put(11, 49)
                dict.put(12, 52)
                dict.put(13, 54)
                dict.put(14, 57)
                dict.put(15, 59)
                dict.put(16, 62)
                dict.put(17, 65)
                dict.put(18, 67)
                dict.put(19, 70)
                dict.put(20, 72)
                dict.put(21, 75)
                dict.put(22, 77)
                dict.put(23, 80)
                dict.put(24, 82)
                dict.put(25, 85)
                dict.put(26, 88)
                dict.put(27, 90)
                dict.put(28, 93)
                dict.put(29, 95)
                dict.put(30, 98)
                dict.put(31, 100)
                dict.put(32, 103)
                dict.put(33, 106)
                dict.put(34, 108)
                dict.put(35, 111)
                dict.put(36, 113)
                dict.put(37, 116)
                dict.put(38, 118)
            }


            "HsF" -> {
                // 疑病-女
                dict.put(0, 23)
                dict.put(1, 25)
                dict.put(2, 27)
                dict.put(3, 29)
                dict.put(4, 31)
                dict.put(5, 33)
                dict.put(6, 35)
                dict.put(7, 37)
                dict.put(8, 39)
                dict.put(9, 42)
                dict.put(10, 44)
                dict.put(11, 46)
                dict.put(12, 48)
                dict.put(13, 50)
                dict.put(14, 52)
                dict.put(15, 54)
                dict.put(16, 56)
                dict.put(17, 58)
                dict.put(18, 60)
                dict.put(19, 62)
                dict.put(20, 64)
                dict.put(21, 66)
                dict.put(22, 68)
                dict.put(23, 70)
                dict.put(24, 72)
                dict.put(25, 74)
                dict.put(26, 76)
                dict.put(27, 78)
                dict.put(28, 80)
                dict.put(29, 82)
                dict.put(30, 85)
                dict.put(31, 87)
                dict.put(32, 89)
                dict.put(33, 91)
                dict.put(34, 93)
                dict.put(35, 95)
                dict.put(36, 97)
                dict.put(37, 99)
                dict.put(38, 101)
                dict.put(39, 103)
                dict.put(40, 105)
                dict.put(41, 107)
                dict.put(42, 109)
                dict.put(43, 111)

            }

            "DM" -> {
                // 抑郁-男
                dict.put(8, 29)
                dict.put(9, 32)
                dict.put(10, 34)
                dict.put(11, 36)
                dict.put(12, 39)
                dict.put(13, 41)
                dict.put(14, 44)
                dict.put(15, 46)
                dict.put(16, 48)
                dict.put(17, 51)
                dict.put(18, 53)
                dict.put(19, 56)
                dict.put(20, 58)
                dict.put(21, 60)
                dict.put(22, 63)
                dict.put(23, 65)
                dict.put(24, 68)
                dict.put(25, 70)
                dict.put(26, 72)
                dict.put(27, 75)
                dict.put(28, 77)
                dict.put(29, 80)
                dict.put(30, 82)
                dict.put(31, 84)
                dict.put(32, 87)
                dict.put(33, 89)
                dict.put(34, 92)
                dict.put(35, 94)
                dict.put(36, 96)
                dict.put(37, 99)
                dict.put(38, 101)
                dict.put(39, 104)
                dict.put(40, 106)
                dict.put(41, 108)
                dict.put(42, 111)
                dict.put(43, 113)
                dict.put(44, 116)
                dict.put(45, 118)
                dict.put(46, 120)

            }

            "DF" -> {
                // 抑郁-女
                dict.put(8, 28)
                dict.put(9, 30)
                dict.put(10, 32)
                dict.put(11, 34)
                dict.put(12, 36)
                dict.put(13, 38)
                dict.put(14, 40)
                dict.put(15, 42)
                dict.put(16, 44)
                dict.put(17, 46)
                dict.put(18, 47)
                dict.put(19, 49)
                dict.put(20, 51)
                dict.put(21, 53)
                dict.put(22, 55)
                dict.put(23, 57)
                dict.put(24, 59)
                dict.put(25, 62)
                dict.put(26, 63)
                dict.put(27, 65)
                dict.put(28, 67)
                dict.put(29, 69)
                dict.put(30, 71)
                dict.put(31, 73)
                dict.put(32, 75)
                dict.put(33, 76)
                dict.put(34, 78)
                dict.put(35, 80)
                dict.put(36, 82)
                dict.put(37, 84)
                dict.put(38, 86)
                dict.put(39, 88)
                dict.put(40, 90)
                dict.put(41, 92)
                dict.put(42, 94)
                dict.put(43, 96)
                dict.put(44, 98)
                dict.put(45, 100)
                dict.put(46, 102)
                dict.put(47, 103)
                dict.put(48, 105)
                dict.put(49, 107)
                dict.put(50, 109)
                dict.put(51, 111)
                dict.put(52, 113)
                dict.put(53, 115)
                dict.put(54, 117)
            }


            "HyM" -> {
                // 癔病-男
                dict.put(8, 35)
                dict.put(9, 36)
                dict.put(10, 38)
                dict.put(11, 40)
                dict.put(12, 42)
                dict.put(13, 44)
                dict.put(14, 45)
                dict.put(15, 47)
                dict.put(16, 49)
                dict.put(17, 51)
                dict.put(18, 53)
                dict.put(19, 55)
                dict.put(20, 56)
                dict.put(21, 58)
                dict.put(22, 60)
                dict.put(23, 62)
                dict.put(24, 64)
                dict.put(25, 65)
                dict.put(26, 67)
                dict.put(27, 69)
                dict.put(28, 71)
                dict.put(29, 73)
                dict.put(30, 75)
                dict.put(31, 76)
                dict.put(32, 78)
                dict.put(33, 80)
                dict.put(34, 82)
                dict.put(35, 84)
                dict.put(36, 86)
                dict.put(37, 87)
                dict.put(38, 89)
                dict.put(39, 91)
                dict.put(40, 93)
                dict.put(41, 95)
                dict.put(42, 96)
                dict.put(43, 98)
                dict.put(44, 100)
                dict.put(45, 102)
                dict.put(46, 104)
                dict.put(47, 106)
                dict.put(48, 107)
                dict.put(49, 109)
                dict.put(50, 111)
                dict.put(51, 113)
                dict.put(52, 115)
                dict.put(53, 116)
                dict.put(54, 118)
            }


            "HyF" -> {
                // 癔病-女
                dict.put(4, 24)
                dict.put(5, 26)
                dict.put(6, 27)
                dict.put(7, 29)
                dict.put(8, 31)
                dict.put(9, 33)
                dict.put(10, 34)
                dict.put(11, 36)
                dict.put(12, 38)
                dict.put(13, 40)
                dict.put(14, 42)
                dict.put(15, 43)
                dict.put(16, 45)
                dict.put(17, 47)
                dict.put(18, 49)
                dict.put(19, 50)
                dict.put(20, 52)
                dict.put(21, 54)
                dict.put(22, 56)
                dict.put(23, 57)
                dict.put(24, 59)
                dict.put(25, 61)
                dict.put(26, 63)
                dict.put(27, 64)
                dict.put(28, 66)
                dict.put(29, 68)
                dict.put(30, 70)
                dict.put(31, 72)
                dict.put(32, 73)
                dict.put(33, 75)
                dict.put(34, 77)
                dict.put(35, 79)
                dict.put(36, 80)
                dict.put(37, 82)
                dict.put(38, 84)
                dict.put(39, 86)
                dict.put(40, 87)
                dict.put(41, 89)
                dict.put(42, 91)
                dict.put(43, 93)
                dict.put(44, 94)
                dict.put(45, 96)
                dict.put(46, 98)
                dict.put(47, 100)
                dict.put(48, 101)
                dict.put(49, 103)
                dict.put(50, 105)
                dict.put(51, 107)
                dict.put(52, 109)
                dict.put(53, 110)
                dict.put(54, 112)
            }


            "PdMF" -> {
                // 精神病态
                dict.put(6, 20)
                dict.put(7, 22)
                dict.put(8, 24)
                dict.put(9, 27)
                dict.put(10, 29)
                dict.put(11, 32)
                dict.put(12, 34)
                dict.put(13, 36)
                dict.put(14, 39)
                dict.put(15, 41)
                dict.put(16, 43)
                dict.put(17, 46)
                dict.put(18, 48)
                dict.put(19, 50)
                dict.put(20, 53)
                dict.put(21, 55)
                dict.put(22, 57)
                dict.put(23, 60)
                dict.put(24, 62)
                dict.put(25, 64)
                dict.put(26, 67)
                dict.put(27, 69)
                dict.put(28, 71)
                dict.put(29, 74)
                dict.put(30, 76)
                dict.put(31, 79)
                dict.put(32, 81)
                dict.put(33, 83)
                dict.put(34, 86)
                dict.put(35, 88)
                dict.put(36, 90)
                dict.put(37, 93)
                dict.put(38, 95)
                dict.put(39, 97)
                dict.put(40, 100)
                dict.put(41, 102)
                dict.put(42, 104)
                dict.put(43, 107)
                dict.put(44, 109)
                dict.put(45, 111)
                dict.put(46, 114)
                dict.put(47, 116)
                dict.put(48, 119)

            }

            "MfM" -> {
                // 男子气
                dict.put(8, 26)
                dict.put(9, 28)
                dict.put(10, 30)
                dict.put(11, 32)
                dict.put(12, 34)
                dict.put(13, 35)
                dict.put(14, 37)
                dict.put(15, 39)
                dict.put(16, 41)
                dict.put(17, 43)
                dict.put(18, 45)
                dict.put(19, 47)
                dict.put(20, 49)
                dict.put(21, 51)
                dict.put(22, 53)
                dict.put(23, 55)
                dict.put(24, 57)
                dict.put(25, 59)
                dict.put(26, 61)
                dict.put(27, 63)
                dict.put(28, 65)
                dict.put(29, 67)
                dict.put(30, 69)
                dict.put(31, 71)
                dict.put(32, 73)
                dict.put(33, 74)
                dict.put(34, 76)
                dict.put(35, 78)
                dict.put(36, 80)
                dict.put(37, 82)
                dict.put(38, 84)
                dict.put(39, 86)
                dict.put(40, 88)
                dict.put(41, 90)
                dict.put(42, 92)
                dict.put(43, 94)
                dict.put(44, 96)
                dict.put(45, 98)
                dict.put(46, 100)
                dict.put(47, 102)
                dict.put(48, 104)
                dict.put(49, 106)
                dict.put(50, 108)
                dict.put(51, 110)
            }


            "MfF" -> {
                // 女子气
                dict.put(15, 95)
                dict.put(16, 92)
                dict.put(17, 90)
                dict.put(18, 88)
                dict.put(19, 86)
                dict.put(20, 84)
                dict.put(21, 82)
                dict.put(22, 80)
                dict.put(23, 78)
                dict.put(24, 76)
                dict.put(25, 74)
                dict.put(26, 72)
                dict.put(27, 70)
                dict.put(28, 68)
                dict.put(29, 66)
                dict.put(30, 63)
                dict.put(31, 61)
                dict.put(32, 59)
                dict.put(33, 57)
                dict.put(34, 55)
                dict.put(35, 53)
                dict.put(36, 51)
                dict.put(37, 49)
                dict.put(38, 47)
                dict.put(39, 45)
                dict.put(40, 43)
                dict.put(41, 41)
                dict.put(42, 39)
                dict.put(43, 37)
                dict.put(44, 34)
                dict.put(45, 32)
                dict.put(46, 30)
                dict.put(47, 28)
                dict.put(48, 26)
                dict.put(49, 24)
                dict.put(50, 22)
                dict.put(51, 20)
            }


            "PaMF" -> {
                // 妄想症
                dict.put(0, 27)
                dict.put(1, 30)
                dict.put(2, 33)
                dict.put(3, 35)
                dict.put(4, 38)
                dict.put(5, 41)
                dict.put(6, 44)
                dict.put(7, 47)
                dict.put(8, 50)
                dict.put(9, 53)
                dict.put(10, 56)
                dict.put(11, 59)
                dict.put(12, 62)
                dict.put(13, 65)
                dict.put(14, 67)
                dict.put(15, 70)
                dict.put(16, 73)
                dict.put(17, 76)
                dict.put(18, 79)
                dict.put(19, 82)
                dict.put(20, 85)
                dict.put(21, 88)
                dict.put(22, 91)
                dict.put(23, 94)
                dict.put(24, 97)
                dict.put(25, 100)
                dict.put(26, 102)
                dict.put(27, 105)
                dict.put(28, 108)
                dict.put(29, 111)
                dict.put(30, 114)
                dict.put(31, 117)
                dict.put(32, 120)
            }


            "PtM" -> {
                // 精神衰弱-男
                dict.put(9, 21)
                dict.put(10, 23)
                dict.put(11, 26)
                dict.put(12, 28)
                dict.put(13, 30)
                dict.put(14, 32)
                dict.put(15, 34)
                dict.put(16, 36)
                dict.put(17, 38)
                dict.put(18, 40)
                dict.put(19, 42)
                dict.put(20, 44)
                dict.put(21, 46)
                dict.put(22, 48)
                dict.put(23, 50)
                dict.put(24, 52)
                dict.put(25, 54)
                dict.put(26, 56)
                dict.put(27, 58)
                dict.put(28, 60)
                dict.put(29, 62)
                dict.put(30, 64)
                dict.put(31, 66)
                dict.put(32, 69)
                dict.put(33, 71)
                dict.put(34, 73)
                dict.put(35, 75)
                dict.put(36, 77)
                dict.put(37, 79)
                dict.put(38, 81)
                dict.put(39, 83)
                dict.put(40, 85)
                dict.put(41, 87)
                dict.put(42, 89)
                dict.put(43, 91)
                dict.put(44, 93)
                dict.put(45, 95)
                dict.put(46, 97)
                dict.put(47, 99)
                dict.put(48, 101)
                dict.put(49, 103)
                dict.put(50, 105)
                dict.put(51, 107)
                dict.put(52, 110)
                dict.put(53, 112)
                dict.put(54, 114)
                dict.put(55, 116)
                dict.put(56, 118)
                dict.put(57, 120)
            }


            "PtF" -> {
                // 精神衰弱-女
                dict.put(7, 20)
                dict.put(8, 22)
                dict.put(9, 23)
                dict.put(10, 25)
                dict.put(11, 27)
                dict.put(12, 28)
                dict.put(13, 30)
                dict.put(14, 32)
                dict.put(15, 33)
                dict.put(16, 35)
                dict.put(17, 36)
                dict.put(18, 38)
                dict.put(19, 40)
                dict.put(20, 41)
                dict.put(21, 43)
                dict.put(22, 45)
                dict.put(23, 46)
                dict.put(24, 48)
                dict.put(25, 50)
                dict.put(26, 51)
                dict.put(27, 53)
                dict.put(28, 55)
                dict.put(29, 56)
                dict.put(30, 58)
                dict.put(31, 60)
                dict.put(32, 61)
                dict.put(33, 63)
                dict.put(34, 65)
                dict.put(35, 66)
                dict.put(36, 68)
                dict.put(37, 69)
                dict.put(38, 71)
                dict.put(39, 73)
                dict.put(40, 74)
                dict.put(41, 76)
                dict.put(42, 78)
                dict.put(43, 79)
                dict.put(44, 81)
                dict.put(45, 83)
                dict.put(46, 84)
                dict.put(47, 86)
                dict.put(48, 88)
                dict.put(49, 89)
                dict.put(50, 91)
                dict.put(51, 93)
                dict.put(52, 94)
                dict.put(53, 96)
                dict.put(54, 98)
                dict.put(55, 99)
                dict.put(56, 101)
                dict.put(57, 102)
                dict.put(58, 104)
                dict.put(59, 106)
                dict.put(60, 107)
            }


            "ScM" -> {
                // 精神病症-男
                dict.put(7, 21)
                dict.put(8, 23)
                dict.put(9, 25)
                dict.put(10, 26)
                dict.put(11, 28)
                dict.put(12, 30)
                dict.put(13, 32)
                dict.put(14, 34)
                dict.put(15, 36)
                dict.put(16, 38)
                dict.put(17, 40)
                dict.put(18, 42)
                dict.put(19, 44)
                dict.put(20, 46)
                dict.put(21, 48)
                dict.put(22, 50)
                dict.put(23, 51)
                dict.put(24, 53)
                dict.put(25, 55)
                dict.put(26, 57)
                dict.put(27, 59)
                dict.put(28, 61)
                dict.put(29, 63)
                dict.put(30, 65)
                dict.put(31, 67)
                dict.put(32, 69)
                dict.put(33, 71)
                dict.put(34, 73)
                dict.put(35, 74)
                dict.put(36, 76)
                dict.put(37, 78)
                dict.put(38, 80)
                dict.put(39, 82)
                dict.put(40, 84)
                dict.put(41, 86)
                dict.put(42, 88)
                dict.put(43, 90)
                dict.put(44, 92)
                dict.put(45, 94)
                dict.put(46, 96)
                dict.put(47, 97)
                dict.put(48, 99)
                dict.put(49, 101)
                dict.put(50, 103)
                dict.put(51, 105)
                dict.put(52, 107)
                dict.put(53, 109)
                dict.put(54, 111)
                dict.put(55, 113)
                dict.put(56, 115)
                dict.put(57, 117)
                dict.put(58, 119)
            }


            "ScF" -> {
                // 精神病症-女
                dict.put(5, 23)
                dict.put(6, 24)
                dict.put(7, 26)
                dict.put(8, 27)
                dict.put(9, 29)
                dict.put(10, 31)
                dict.put(11, 32)
                dict.put(12, 34)
                dict.put(13, 35)
                dict.put(14, 37)
                dict.put(15, 38)
                dict.put(16, 40)
                dict.put(17, 41)
                dict.put(18, 44)
                dict.put(19, 45)
                dict.put(20, 46)
                dict.put(21, 47)
                dict.put(22, 49)
                dict.put(23, 51)
                dict.put(24, 52)
                dict.put(25, 54)
                dict.put(26, 55)
                dict.put(27, 57)
                dict.put(28, 58)
                dict.put(29, 60)
                dict.put(30, 61)
                dict.put(31, 63)
                dict.put(32, 64)
                dict.put(33, 66)
                dict.put(34, 67)
                dict.put(35, 69)
                dict.put(36, 71)
                dict.put(37, 72)
                dict.put(38, 74)
                dict.put(39, 75)
                dict.put(40, 77)
                dict.put(41, 78)
                dict.put(42, 80)
                dict.put(43, 81)
                dict.put(44, 83)
                dict.put(45, 84)
                dict.put(46, 86)
                dict.put(47, 87)
                dict.put(48, 89)
                dict.put(49, 91)
                dict.put(50, 92)
                dict.put(51, 94)
                dict.put(52, 95)
                dict.put(53, 97)
                dict.put(54, 98)
                dict.put(55, 100)
                dict.put(56, 101)
                dict.put(57, 103)
                dict.put(58, 104)
                dict.put(59, 106)
                dict.put(60, 107)
                dict.put(61, 109)
                dict.put(62, 111)
                dict.put(63, 112)
                dict.put(64, 114)
                dict.put(65, 115)
                dict.put(66, 117)
                dict.put(67, 118)
            }


            "MaMF" -> {
                // 轻躁狂
                dict.put(5, 21)
                dict.put(6, 23)
                dict.put(7, 26)
                dict.put(8, 28)
                dict.put(9, 30)
                dict.put(10, 33)
                dict.put(11, 35)
                dict.put(12, 38)
                dict.put(13, 40)
                dict.put(14, 43)
                dict.put(15, 45)
                dict.put(16, 48)
                dict.put(17, 50)
                dict.put(18, 53)
                dict.put(19, 55)
                dict.put(20, 58)
                dict.put(21, 60)
                dict.put(22, 63)
                dict.put(23, 65)
                dict.put(24, 68)
                dict.put(25, 70)
                dict.put(26, 73)
                dict.put(27, 75)
                dict.put(28, 78)
                dict.put(29, 81)
                dict.put(30, 83)
                dict.put(31, 86)
                dict.put(32, 88)
                dict.put(33, 91)
                dict.put(34, 93)
                dict.put(35, 96)
                dict.put(36, 98)
                dict.put(37, 101)
                dict.put(38, 103)
                dict.put(39, 106)
                dict.put(40, 108)
            }


            "SiMF" -> {
                // 社会内向
                dict.put(1, 25)
                dict.put(2, 26)
                dict.put(3, 27)
                dict.put(4, 28)
                dict.put(5, 29)
                dict.put(6, 30)
                dict.put(7, 32)
                dict.put(8, 33)
                dict.put(9, 34)
                dict.put(10, 35)
                dict.put(11, 36)
                dict.put(12, 37)
                dict.put(13, 38)
                dict.put(14, 39)
                dict.put(15, 40)
                dict.put(16, 41)
                dict.put(17, 42)
                dict.put(18, 43)
                dict.put(19, 44)
                dict.put(20, 45)
                dict.put(21, 46)
                dict.put(22, 47)
                dict.put(23, 48)
                dict.put(24, 49)
                dict.put(25, 50)
                dict.put(26, 51)
                dict.put(27, 52)
                dict.put(28, 53)
                dict.put(29, 54)
                dict.put(30, 55)
                dict.put(31, 56)
                dict.put(32, 58)
                dict.put(33, 60)
                dict.put(34, 61)
                dict.put(35, 62)
                dict.put(36, 63)
                dict.put(37, 64)
                dict.put(38, 65)
                dict.put(39, 66)
                dict.put(40, 67)
                dict.put(41, 68)
                dict.put(42, 69)
                dict.put(43, 70)
                dict.put(44, 71)
                dict.put(45, 72)
                dict.put(46, 73)
                dict.put(47, 74)
                dict.put(48, 75)
                dict.put(49, 76)
                dict.put(50, 77)
                dict.put(51, 78)
                dict.put(52, 79)
                dict.put(53, 80)
                dict.put(54, 81)
                dict.put(55, 82)
                dict.put(56, 83)
                dict.put(57, 84)
                dict.put(58, 85)
                dict.put(59, 86)
                dict.put(60, 87)
                dict.put(61, 88)
                dict.put(62, 89)
                dict.put(63, 90)
                dict.put(64, 91)
                dict.put(65, 92)
                dict.put(66, 93)
                dict.put(67, 94)
                dict.put(68, 95)
                dict.put(69, 96)
                dict.put(70, 97)
            }
        }

        var t = -1
        if (dict.size > 0) {
            val maxKey = dict.maxOf { it.key }
            val minKey = dict.minOf { it.key }
            if (protypeVal > maxKey) {
                t = dict[maxKey]!!  //大于最大的原始值 则赋值最大的T分值
            } else if (protypeVal < minKey) {
                t = dict[minKey]!!  //小于最小的原始值 则赋值最小的T分值
            } else {
                for ((key, value) in dict) {
                    if (key == protypeVal) {
                        t = value
                        break
                    }
                }
            }
        }
        return t
    }

    private fun T(s: String, i: Int): Int {
        return 0
    }

    /// <summary>
    /// 针对 MMPI的辅助方法
    /// </summary>
    /// <param name="val">T分</param>
    /// <returns>返回值当作索引来用</returns>
    private fun MMPIAssit(value: Int): Int {
        return if (value >= 70) 0 else if (value < 70) 1 else 2
    }
}