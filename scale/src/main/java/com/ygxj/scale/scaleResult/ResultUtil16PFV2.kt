package com.ygxj.scale.scaleResult

class ResultUtil16PFV2 {

    fun getResult(scaleId: Int, answers: List<String>, costTime: String) {
        // A因素题目索引 A:3.26.27.51.52.76.101.126.151.176.
        val aIndex = arrayOf(3, 26, 27, 51, 52, 76, 101, 126, 151, 176)
        // B因素题目索引 B:28.53.54.77.78.102.103.127.128.152.153.177.178.180.
        val bIndex = arrayOf(28, 53, 54, 77, 78, 102, 103, 127, 128, 152, 153, 177, 178, 180)
        // C:4.5.29.30.55.79.80.104.105.129.130.154.179.
        val cIndex = arrayOf(4, 5, 29, 30, 55, 79, 80, 104, 105, 129, 130, 154, 179)
        // E:6.7.31.32.56.57.81.106.131.155.156.181.
        val eIndex = arrayOf(6, 7, 31, 32, 56, 57, 81, 106, 131, 155, 156, 181)
        // F:8.33.58.82.83.107.108.132.133.157.158.182.183
        val fIndex = arrayOf(8, 33, 58, 82, 83, 107, 108, 132, 133, 157, 158, 182, 183)
        // G:9.34.59.84.109.134.159.160.184.185
        val gIndex = arrayOf(9, 34, 59, 84, 109, 134, 159, 160, 184, 185)
        // H:10.35.36.60.61.85.86.110.111.135.136.161.186.
        val hIndex = arrayOf(10, 35, 36, 60, 61, 85, 86, 110, 111, 135, 136, 161, 186)
        // I:11.12.37.62.87.112.137.138.162.163.
        val iIndex = arrayOf(11, 12, 37, 62, 87, 112, 137, 138, 162, 163)
        // L:13.38.63.64.88.89.113.114.139.164
        val lIndex = arrayOf(13, 38, 63, 64, 88, 89, 113, 114, 139, 164)
        // M:14.15.39.40.65.90.91.115.116.140.141.165.166
        val mIndex = arrayOf(14, 15, 39, 40, 65, 90, 91, 115, 116, 140, 141, 165, 166)
        // N:16.17.41.42.66.67.92.117.142.167
        val nIndex = arrayOf(16, 17, 41, 42, 66, 67, 92, 117, 142, 167)
        // O:18.19.43.44.68.69.93.94.118.119.143.144.168.
        val oIndex = arrayOf(18, 19, 43, 44, 68, 69, 93, 94, 118, 119, 143, 144, 168)
        // Q1:20.21.45.46.70.95.120.145.169.170
        val q1Index = arrayOf(20, 21, 45, 46, 70, 95, 120, 145, 169, 170)
        // Q2:22.47.71.72.96.97.121.122.146.171
        val q2Index = arrayOf(22, 47, 71, 72, 96, 97, 121, 122, 146, 171)
        // Q3:23.24.48.73.98.123.147.148.172.173
        val q3Index = arrayOf(23, 24, 48, 73, 98, 123, 147, 148, 172, 173)
        // Q4:25.49.50.74.75.99.100.124.125.149.150.174.175
        val q4Index = arrayOf(25, 49, 50, 74, 75, 99, 100, 124, 125, 149, 150, 174, 175)

        // (2)下列每题凡是选B均加1分，选一下对应的选项加2分，否则得0分
        // 3.A   4.A   5.C   6.C   7.A   8.C   9.C   10.A   11.C   12.C   13.A   14.C   15.C   16.C   17.A   18.A
        // 19.C   20.A   21.A   22.C   23.C   24.C   25.A   26.C   27.C 29.C   30.A   31.C   32.C   33.A   34.C
        // 35.C   36.A   37.A   38.A   39.A   40.A   41.C   42.A   43.A   44.C   45.C   46.A   47.A   48.A   49.A
        // 50.A   51.C   52.A 55.A   56.A   57.C   58.A   59.A   60.C   61.C   62.C   63.C   64.C   65.A   66.C
        // 67.C   68.C   69.A   70.A   71.A   72.A   73.A   74.A   75.C   76.C   77.C 78.C   79.C   80.C   81.C
        // 82.C   83.C   84.C   85.C   86.C   87.C   88.A   89.C   90.C   91.A   92.C   93.C   94.C   95.C   96.C
        // 97.C   98.A   99.A   100.A 101.A   102.A   103.A    104.A   105.A   106.C   107.A  108.A   109.A   110.A
        // 111.A   112.A   113.A   114.A   115.A   116.A   117.A   118.A   119.A   120.C   121.C    122.C   123.C
        // 124.A   125.C   126.A   129.A   130.A   131.A   132.A   133.A   134.A   135.C   136.A   137.C   138.A
        // 139.C   140.A   141.C   142.A   143.A   144.C   145.A   146.A   147.A  148.A   149.A   150.A   151.C
        // 154.C   155.A   156.A   157.C   158.C   159.C   160.A   161.C   162.C   163.A 164.A   165.C   166.C
        // 167.A   168.A   169.A   170.C   171.A   172.C   173.A   174.A   175.C   176.A   179.A   180.A   181.A
        // 182.A   183.A   184.A   185.A   186.A

        // 选择A+1分的题目索引
        val add1WhenAnswerA = arrayOf(177, 178)
        // 选A+2分的题目索引
        val add2WhenAnswerA = arrayOf(0)
        // 选择B+1分的题目索引
        val add1WhenAnswerB = arrayOf(
            28, 78, 103, 128, 152, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35,
            36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56
        )
        // 选B+2分的题目索引
        val add2WhenAnswerB = arrayOf(0)
        // 选择C+1分的题目索引
        val add1WhenAnswerC = arrayOf(77, 102, 127, 153)
        // 选C+2分的题目索引
        val add2WhenAnswerC = arrayOf(0)
    }

}