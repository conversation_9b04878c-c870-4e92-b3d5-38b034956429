package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils
import kotlin.math.roundToInt

//焦虑自评量表(SAS)
class ResultUtilSAS {

    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        val fxlist = mutableListOf(5, 9, 13, 17, 19)//反向计分
        var count = 0
        for (index in answers.indices) {
            val value = answers[index].toInt() + 1
            count += if (index in fxlist) QuizUtils.score(value, 4) else value
        }
        val bzf = (count * 1.25f).roundToInt()
        val yj = StringBuffer()
            .append("您本次的得分为${bzf}分")
            .append(
                when (bzf) {
                    in 50..59 -> "轻度焦虑。|分值越高，焦虑症状越重。|结果释义:|该测试者在面对新的环境和人际关系时，可能会表现出焦虑加深的情况，需要外界的适当引导，学会自我调适。困扰多来自于某些特定的事件、人物和场景，以及自己内心的不安思绪。面对重大生活事件，复原力欠佳，难以积极有效地应对，可能这些需要外界的引导和帮助，从事件中走出来。"
                    in 60..69 -> "中度焦虑。|分值越高，焦虑症状越重。|结果释义:|该测试者在面对新的环境和人际关系时，可能会表现出焦虑加深的情况，需要外界的及时引导，学会自我调适。困扰来自于某些特定的事件、人物和场景，以及自己内心的不安思绪，以及比较严重的焦虑症状。面对重大生活事件，复原力较差，难以积极有效地应对，可能这些需要外界及时的帮助，从事件中走出来。"
                    in 70..Int.MAX_VALUE -> "重度焦虑。|分值越高，焦虑症状越重。|结果释义:|该测试者在面对新的环境和人际关系时，可能会表现出焦虑加深的情况，需要外界的及时帮助，学会自我调适。困扰来自于某些特定的事件、人物和场景，以及自己内心的不安思绪，以及严重的躯体上的焦虑症状。面对重大生活事件，复原力差，难以有效应对，可能这些需要外界及时的帮助，从事件中走出来。"
                    else -> "无焦虑症状。|结果释义:|该测试者具有良好的心态，能够以积极的态度接受新的环境或者新的人际关系，适应性良好。困扰较少。面对重大生活事件，也能够积极接受，并寻求合理的解决方法，复原力良好。"
                }
            ).toString()
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "粗分|标准分",
            BZF = "${count}|${bzf}",
            JF = "",
            BZC = "",
            YJ = yj,
            XX = answers.joinToString("|")
        )
    }
}