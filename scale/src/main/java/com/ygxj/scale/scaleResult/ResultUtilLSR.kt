package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils

//生活满意度评定量表(LSR)
class ResultUtilLSR {

    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        var count = 0 //总分
        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            count += answers[index].toInt() + 1
        }
        val yj = StringBuffer("您本次的得分为${count}分，")
            .append(
                when (count) {
                    in 0..14 -> "生活满意度水平低。|对自己以前和当前的生活状态有较多不满意的地方，感到生活有些沉闷、乏味，可能容易烦恼。"
                    in 15..19 -> "生活满意度水平中等。|对自己以前和当前的生活状态有一些不满意的地方，有时会感到生活有些沉闷、乏味，有时会感到烦恼。"
                    else -> "生活满意度水平高。|对自己以前和当前的生活状态比较满意，感到生活令人愉快和期待，觉得比较幸福。"
                }
            )
            .append("|测试分数＜15分为不满意结果，分数越低说明生活满意度水平越低。测试分数≥15分为满意结果，分数越高，说明生活满意度水平越高。").toString()
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "总分",
            BZF = count.toString(),
            JF = "",
            BZC = "",
            YJ = yj,
            XX = answers.joinToString("|"),
        )
    }

}