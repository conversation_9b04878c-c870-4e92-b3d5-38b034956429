package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity
import com.ygxj.scale.util.QuizUtils

//UCLA孤独量表
class ResultUtilUCLA55 {
    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        //结果统计
        val list = mutableListOf(1, 5, 6, 9, 10, 15, 16, 19, 20) //反方向计分
        var count = 0  //总分
        for (index in answers.indices) {
            if (!QuizUtils.isInteger(answers[index])) continue
            val value = answers[index].toInt() + 1
            count += if (index in list) {
                if (value == 4) 1 else if (value == 3) 2 else if (value == 2) 3 else 4
            } else value
        }
        //赋值
        val yj = StringBuffer()
            .append("${count}分，")
            .append(
                when (count) {
                    in 0..27 -> "低度孤独。表明受测者一般没有孤独感，对于自己目前的社会交往状况很满意。同时社会适应能力较好，善于与人建立和谐、愉快的关系，对他人的信任程度比较高。"
                    in 28..32 -> "一般偏下孤独。 测试结果显示受测者偶尔感到孤独，对自己目前的社交状况还算满意。能与一些人比较友好地相处，并能和其中一部分人建立比较信任的关系。但偶尔会感到有些寂寞，不过受测者通常能找到排解的方法。"
                    in 33..38 -> "中间水平。 测试结果显示受测者孤独感的感觉中等，对自己目前的社交状况感觉一般。有一定的社交适应能力，能与周围的人相处，并能和其中少数人建立比较信任的关系，但有时会感到有些寂寞。"
                    in 39..43 -> "一般偏上孤独。测试结果显示受测者对自己目前的社会交往状况不满，比较多的时候感到孤独、寂寞，心情有时不好，也会有伤感、情绪失落等情绪出现，与朋友的交流不太多，独处时间居多。"
                    else -> "高度孤独。测试结果显示受测者对自己目前的社会交往状况非常不满，常常感到孤独，心情不好，经常有悲伤、郁闷等情绪表现。经常独处，亲密朋友较少来往或者是与朋友的交流非常少。"
                }
            ).toString()
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "总分",
            BZF = count.toString(),
            JF = "",
            YJ = yj,
            BZC = "",
            XX = answers.joinToString("|")
        )

    }

}