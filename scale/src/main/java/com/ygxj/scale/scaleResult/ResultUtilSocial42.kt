package com.ygxj.scale.scaleResult

import com.ygxj.scale.entity.ScaleResultEntity

class ResultUtilSocial42 {
    fun getResult(scaleId: Int, answers: List<String>, costTime: String): ScaleResultEntity {
        var count = 0; // 总分
        for (index in answers.indices) {
            val value = answers[index].toInt() + 1
            count += if (index % 2 == 0) {
                if (value == 1) 2 else if (value == 3) -2 else 0
            } else {
                if (value == 1) -2 else if (value == 3) 2 else 0
            }
        }
        val results = mutableListOf(
            "社会适应能力很强,能很快地适应新的学习,生活环境,与人交往轻松,大方,给人的印象极好,无论进入什么样的环境,都能应付自如,左右逢源。",
            "社会适应能力良好。能较好地适应周围的环境,与人关系融洽 ,处事能力较强。",
            "社会适应能力一般,当进入一个新环境,经过一段时间的努力,基本上能适应。",
            "社会适应能力较差,依赖于较好的学习,生活环境,一旦遇到困难则易怨天尤人,甚至消沉。",
            "社会适应能力很差,在各种新环境中,即使经过一段相当长时间的努力,也不一定能够适应,常常困惑到与周围事物格格不入而十分苦恼。在与他人的交往中,总是显得拘谨,羞怯,手足无措。"
        )
        val yj = when (count) {
            in 35..40 -> results[0]
            in 29..34 -> results[1]
            in 17..28 -> results[2]
            in 6..16 -> results[3]
            in 0..5 -> results[4]
            else -> results[4]
        }
        return ScaleResultEntity(
            scaleId = scaleId,
            costTime = costTime,
            YSM = "总分",
            BZF = count.toString(),
            JF = (count / answers.size).toString(),
            YJ = yj,
            BZC = "",
            XX = answers.joinToString("|")
        )
    }
}