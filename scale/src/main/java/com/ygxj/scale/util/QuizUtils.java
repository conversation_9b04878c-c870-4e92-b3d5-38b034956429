package com.ygxj.scale.util;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

public class QuizUtils {

    /*方法二：推荐，速度最快
     * 判断是否为整数
     * @param str 传入的字符串
     * @return 是整数返回true,否则返回false
     */

    public static boolean isInteger(String str) {
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        return pattern.matcher(str).matches();
    }

    /**
     * 返回List列表
     *
     * @param starter   初始值
     * @param increment 增量
     * @param i         个数
     * @return List列表
     */
    public static List<Integer> getList(int starter, int increment, int i) {
        List<Integer> list = new ArrayList<>();
        list.add(starter);
        for (int j = 1; j < i; j++) {
            list.add(list.get(j - 1) + increment);
        }
        return list;
    }

    /**
     * 反向计分
     *
     * @param val 选项值
     * @param num 选项个数
     * @return 分数
     */
    public static int score(int val, int num) {
        switch (num) {
            case 2:
                return val == 2 ? 1 : 0;   //注意 此项比较特殊
            case 3:
                return val == 3 ? 1 : val == 2 ? 2 : val == 1 ? 3 : 0;
            case 4:
                return val == 4 ? 1 : val == 3 ? 2 : val == 2 ? 3 : val == 1 ? 4 : 0;
            case 5:
                return val == 5 ? 1 : val == 4 ? 2 : val == 3 ? 3 : val == 2 ? 4 : val == 1 ? 5 : 0;
            case 6:
                return val == 6 ? 1 : val == 5 ? 2 : val == 4 ? 3 : val == 3 ? 4 : val == 2 ? 5 : val == 1 ? 6 : 0;
            case 7:
                return val == 7 ? 1 : val == 6 ? 2 : val == 5 ? 3 : val == 4 ? 4 : val == 3 ? 5 : val == 2 ? 6 : val == 1 ? 7 : 0;
            case 8:
                return val == 8 ? 1 : val == 7 ? 2 : val == 6 ? 3 : val == 5 ? 4 : val == 4 ? 5 : val == 3 ? 6 : val == 2 ? 7 : val == 1 ? 8 : 0;
            case 9:
                return val == 9 ? 1 : val == 8 ? 2 : val == 7 ? 3 : val == 6 ? 4 : val == 5 ? 5 : val == 4 ? 6 : val == 3 ? 7 : val == 2 ? 8 : val == 1 ? 9 : 0;
            default:
                return 0;
        }
        //反向计分可以这么做
        //return num - val + 1;
    }


}
