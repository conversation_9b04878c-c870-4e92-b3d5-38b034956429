package com.ygxj.scale.util

import android.annotation.SuppressLint
import android.util.Log
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale


object DateUtils {

    /**
     * 获取年月日,用指定的分隔符连接
     * @param date 日期毫秒值
     * @param separator 分隔符号
     * @return 如"2020-03-21"
     */
    fun getYearMonthDayWithSeparator(date: Long, separator: String): String {
        return "${getYear(date)}$separator${getMonth(date)}$separator${getDay(date)}"
    }

    /**
     * 获取月日,用指定的分隔符连接
     * @param date 日期毫秒值
     * @param separator 分隔符号
     * @return 如"03-21"
     */
    fun getYearMonthWidthSeparator(date: Long, separator: String): String {
        return "${getYear(date)}$separator${getMonth(date)}"
    }

    /**
     * 获取年
     * @param date 日期毫秒值
     * @return 如:"2020"
     */
    fun getYear(date: Long): String? {
        return format(date, "yyyy");
    }

    /**
     * 获取月
     * @param date 日期毫秒值
     * @return 如:"01"
     */
    fun getMonth(date: Long): String? {
        return format(date, "MM");
    }

    /**
     * 获取日
     * @param date 日期毫秒值
     * @return 如:"08"
     */
    fun getDay(date: Long): String? {
        return format(date, "dd");
    }

    /**
     * 获取小时
     * @param date 日期毫秒值
     * @return 如"02"
     */
    fun getHour(date: Long): String? {
        return format(date, "HH")
    }

    /**
     * 获取分
     * @param date 日期毫秒值
     * @return 如"07"
     */
    fun getMinute(date: Long): String? {
        return format(date, "mm")
    }

    /**
     * 获取秒
     * @param date 日期毫秒值
     * @return 如"03"
     */
    fun getSecond(date: Long): String? {
        return format(date, "ss")
    }

    /**
     * 获取自定义的时间格式
     * @param pattern 格式
     * @param date 日期毫秒值
     * @see <a href="https://docs.oracle.com/javase/tutorial/i18n/format/simpleDateFormat.html">定义格式方式</a>
     */
    fun format(date: Long?, pattern: String): String? {
        date ?: return null
        return SimpleDateFormat(pattern, Locale.CHINESE).format(Date(date))
    }


    /**
     * 从字符串获取Date值
     */
    fun getDateForString(stringDate: String): Date? {
        return try {
            SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINESE).parse(
                stringDate.replace(
                    "T",
                    " "
                )
            )
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    /**根据生日算年龄*/
    @SuppressLint("SimpleDateFormat")
    fun getAgeByBirth(birthday: String): Int {
        //Calendar：日历
        val sft = SimpleDateFormat("yyyy-MM-dd")
        val date: Date?
        try {
            date = sft.parse(birthday)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            return 0
        }
        /*从Calendar对象中或得一个Date对象*/
        val cal = Calendar.getInstance()
        /*把出生日期放入Calendar类型的bir对象中，进行Calendar和Date类型进行转换*/
        val bir = Calendar.getInstance()
        bir.time = date!!
        /*如果生日大于当前日期，则抛出异常：出生日期不能大于当前日期*/
        if (cal.before(date)) return 0
        /*取出当前年月日*/
        val yearNow = cal[Calendar.YEAR]
        val monthNow = cal[Calendar.MONTH]
        val dayNow = cal[Calendar.DAY_OF_MONTH]
        /*取出出生年月日*/
        val yearBirth = bir[Calendar.YEAR]
        val monthBirth = bir[Calendar.MONTH]
        val dayBirth = bir[Calendar.DAY_OF_MONTH]
        /*大概年龄是当前年减去出生年*/
        var age = yearNow - yearBirth
        /*如果出当前月小与出生月，或者当前月等于出生月但是当前日小于出生日，那么年龄age就减一岁*/
        if (monthNow < monthBirth || monthNow == monthBirth && dayNow < dayBirth) age--
        return age
    }

    fun getAgeByBirthFloat(birthday: String): Float {
        //Calendar：日历
        val sft = SimpleDateFormat("yyyy-MM-dd")
        val date: Date?
        try {
            date = sft.parse(birthday)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            return 0f
        }
        /*从Calendar对象中或得一个Date对象*/
        val cal = Calendar.getInstance()
        /*把出生日期放入Calendar类型的bir对象中，进行Calendar和Date类型进行转换*/
        val bir = Calendar.getInstance()
        bir.time = date!!
        /*如果生日大于当前日期，则抛出异常：出生日期不能大于当前日期*/
        if (cal.before(date)) return 0f
        /*取出当前年月日*/
        val yearNow = cal[Calendar.YEAR]
        val monthNow = cal[Calendar.MONTH]
        val dayNow = cal[Calendar.DAY_OF_MONTH]
        /*取出出生年月日*/
        val yearBirth = bir[Calendar.YEAR]
        val monthBirth = bir[Calendar.MONTH]
        val dayBirth = bir[Calendar.DAY_OF_MONTH]
        /*大概年龄是当前年减去出生年*/
        val age = 0f + yearNow - yearBirth
        /*如果出当前月小与出生月，或者当前月等于出生月但是当前日小于出生日，那么年龄age就减一岁*/
        return when {
            monthNow < monthBirth -> age + monthNow / monthBirth - 1f
            monthNow == monthBirth && dayNow > dayBirth -> age + 0.1f
            monthNow == monthBirth && dayNow < dayBirth -> age - 0.1f
            else -> age
        }
    }

    /**
     * 根据秒数,返回转换后的时:分:秒
     */
    fun getHourMinSecFromSec(second: Long): String {
        val hour = second / 3600
        var min = second % 3600
        val sec = min % 60
        min /= 60
        return String.format(Locale.CHINESE, "%02d:%02d:%02d", hour, min, sec)
    }

}