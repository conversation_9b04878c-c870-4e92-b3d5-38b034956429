package com.ygxj.scale.entity

import java.io.Serializable

/**
 * 量表实体
 * 最终各依赖项目用的都是这个实体
 */
data class ScaleEntity(
    /**量表id*/
    var scaleId: Int,
    /**量表名*/
    var scaleName: String,
    /**量表介绍*/
    var scaleIntro: String,
    /**量表帮助*/
    var scaleHelper: String,
    /**是否开放,0不开放 1开放*/
    var enable: Int,
    /**是否显示结果 0不显示  1显示*/
    var showResult: Int,
    /**量表题目*/
    var scaleQuestions: List<ScaleQuestion>,
    var selected: Boolean = false
) : Serializable {

    data class ScaleQuestion(
        /**题目文本,可能为字符串,也可能为图片*/
        var questionTitle: String,

        /**题目选项*/
        var questionOptions: List<QuestionOptions>
    ) : Serializable {

        data class QuestionOptions(
            /**选项内容*/
            var optionValue: String,
            /**选项状态*/
            var selected: Boolean = false,
            /**是否为输入选项*/
            var isInput: Boolean = false
        ) : Serializable
    }
}
