package com.ygxj.scale.entity

import com.ygxj.scale.util.DateUtils
import java.io.Serializable

data class ScaleResultEntity(
    // 量表id
    var scaleId: Int,
    // 测试用时
    var costTime: String,
    // 因素名
    var YSM: String,
    // 总分标准分
    var BZF: String,
    // 均分
    var JF: String,
    // 标准差
    var BZC: String,
    // 意见
    var YJ: String,
    // 粗分
    val cf: String? = null,
    // 选项及答案
    var XX: String,
) : Serializable {

    override fun toString(): String {
        return "量表id:$scaleId 测试日期:$testDate 测试用时:$costTime 因素名:$YSM 标准分:$BZF 均分:$JF 标准差:$BZC 意见:$YJ 选项:$XX"
    }

    /**测试日期*/
    var testDate: String = DateUtils.format(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss")!!
}