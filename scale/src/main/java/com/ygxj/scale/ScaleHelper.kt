package com.ygxj.scale

import android.app.Application
import com.ygxj.scale.database.ScaleDataBase
import com.ygxj.scale.database.table.QuestionData
import com.ygxj.scale.database.table.ScaleData
import com.ygxj.scale.entity.ScaleEntity

/**
 * 量表助手
 * 使用说明:
 * 1. 在app.gradle中依赖此module
 * 2. 在app的Application.onCreate()中调用ScaleHelper.initialize(context)进行初始化
 * 3. 具体方法查看对应注释
 */
object ScaleHelper {

    private const val TAG = "ScaleHelper"

    /**数据库名称*/
    const val DB_NAME = "scale.db"
    private lateinit var scaleDB: ScaleDataBase

    /**
     * 初始化,使用前在Application中调用
     * 创建数据库并将assets目录下的scale.db中的数据填充到数据库中
     */
    fun initialize(context: Application) {
        // 初始化数据库
        scaleDB = ScaleDataBase.getInstance(context)
    }

    /**
     * 获取所有的转换后的量表实体
     * data后缀的是数据库的实体,宿主项目中不直接使用
     * entity后缀的是最终使用的实体
     */
    fun getAllScaleEntity(): List<ScaleEntity> {

        checkDataBaseInitialized()

        val scaleDataList = getAllScaleData()
        val scaleEntityList = arrayListOf<ScaleEntity>()

        for (scaleData in scaleDataList) {
            val scaleEntity = getScaleEntityById(scaleData.id)
            scaleEntityList.add(scaleEntity)
        }
        return scaleEntityList
    }

    /**
     * 获取指定多个量表id的转换后的量表实体
     * data后缀的是数据库的实体,宿主项目中不直接使用
     * entity后缀的是最终使用的实体
     * - [scaleIdList] 需要查找的量表id集合
     */
    fun getScaleEntityListByIds(scaleIdList: List<Int>): ArrayList<ScaleEntity> {
        checkDataBaseInitialized()

        val scaleEntityList = arrayListOf<ScaleEntity>()
        for (scaleId in scaleIdList) {
            val scaleEntity = getScaleEntityById(scaleId)
            scaleEntityList.add(scaleEntity)
        }
        return scaleEntityList
    }

    /**
     * 获取指定量表id的转换后的量表实体
     * data后缀的是数据库的实体,宿主项目中不直接使用
     * entity后缀的是最终使用的实体
     * - [scaleId] 量表id
     */
    fun getScaleEntityById(scaleId: Int): ScaleEntity {
        checkDataBaseInitialized()

        val scaleData = getScaleData(scaleId)
        val questionDataList = getScaleQuestionData(scaleId)
        val questionEntityList = arrayListOf<ScaleEntity.ScaleQuestion>()

        for (questionData in questionDataList) {
            val options = arrayListOf<ScaleEntity.ScaleQuestion.QuestionOptions>()
            if (!questionData.option1.isNullOrBlank()) options.add(ScaleEntity.ScaleQuestion.QuestionOptions(questionData.option1))
            if (!questionData.option2.isNullOrBlank()) options.add(ScaleEntity.ScaleQuestion.QuestionOptions(questionData.option2))
            if (!questionData.option3.isNullOrBlank()) options.add(ScaleEntity.ScaleQuestion.QuestionOptions(questionData.option3))
            if (!questionData.option4.isNullOrBlank()) options.add(ScaleEntity.ScaleQuestion.QuestionOptions(questionData.option4))
            if (!questionData.option5.isNullOrBlank()) options.add(ScaleEntity.ScaleQuestion.QuestionOptions(questionData.option5))
            if (!questionData.option6.isNullOrBlank()) options.add(ScaleEntity.ScaleQuestion.QuestionOptions(questionData.option6))
            if (!questionData.option7.isNullOrBlank()) options.add(ScaleEntity.ScaleQuestion.QuestionOptions(questionData.option7))
            if (!questionData.option8.isNullOrBlank()) options.add(ScaleEntity.ScaleQuestion.QuestionOptions(questionData.option8))
            if (!questionData.option9.isNullOrBlank()) options.add(ScaleEntity.ScaleQuestion.QuestionOptions(questionData.option9))
            if (!questionData.option10.isNullOrBlank()) options.add(ScaleEntity.ScaleQuestion.QuestionOptions(questionData.option10))
            if (!questionData.option11.isNullOrBlank()) options.add(ScaleEntity.ScaleQuestion.QuestionOptions(questionData.option11))

            // 再过滤下选项为0的情况
            val temp = arrayListOf<ScaleEntity.ScaleQuestion.QuestionOptions>()
            temp.addAll(options.filter { it.optionValue != "0" })
            questionEntityList.add(ScaleEntity.ScaleQuestion(questionData.question, temp))
        }
        return ScaleEntity(
            scaleId = scaleId,
            scaleName = scaleData.name,
            scaleIntro = scaleData.introduce,
            scaleHelper = scaleData.help,
            enable = scaleData.IsDisplay ?: 0,
            showResult = scaleData.IsShowResult ?: 0,
            scaleQuestions = questionEntityList
        )
    }

    /**
     * 更新量表
     */
    fun updateScale(scaleEntity: ScaleEntity) {
        val scaleData = getScaleData(scaleEntity.scaleId)
        scaleData.name = scaleEntity.scaleName
        scaleData.introduce = scaleEntity.scaleIntro
        scaleData.help = scaleEntity.scaleHelper
        scaleData.IsDisplay = scaleEntity.enable
        scaleData.IsShowResult = scaleEntity.showResult
        scaleDB.scaleDao().updateScaleData(scaleData)
    }

    /**
     * 获取所有的未经转换的量表
     * 不对外暴露,请使用转换后的
     */
    private fun getAllScaleData(): List<ScaleData> {
        return scaleDB.scaleDao().getAllScale()
    }

    /**
     * 获取指定id的未经转换的量表
     * 不对外暴露,请使用转换后的
     */
    private fun getScaleData(scaleId: Int): ScaleData {
        return scaleDB.scaleDao().getScaleDataById(scaleId)
    }

    /**
     * 获取指定id的未经转换的量表问题
     * 不对外暴露,请使用转换后的
     */
    private fun getScaleQuestionData(scaleId: Int): List<QuestionData> {
        return scaleDB.scaleDao().getScaleQuestionDataes(scaleId)
    }

    /**
     * 检查是否初始化,如果没有初始化,抛出异常
     */
    private fun checkDataBaseInitialized() {
        if (!this::scaleDB.isInitialized) {
            throw Exception("数据库未初始化,使用前请先调用 ScaleHelper.initialize(context) 方法")
        }
    }
}