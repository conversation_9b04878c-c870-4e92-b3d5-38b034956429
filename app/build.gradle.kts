import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.kapt)
}

android {
    namespace = "com.ygxj.prshs"
    compileSdk = 36

    defaultConfig {
        applicationId = "com.ygxj.prshs"
        minSdk = 26
        targetSdk = 36
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_11)
        }
    }

    buildFeatures {
        buildConfig = true
        dataBinding = true
        viewBinding = true
    }

    lint {
        disable += arrayOf(
            "HardcodedText",
            "CustomSplashScreen",
            "SetTextI18n",
            "ContentDescription",
            "ViewConstructor",
            "DiscouragedApi",
            "SmallSp"
        )
    }
}

//noinspection UseTomlInstead
dependencies {

    implementation("androidx.core:core-ktx:1.16.0")
    implementation("androidx.appcompat:appcompat:1.7.1")
    implementation("com.google.android.material:material:1.12.0")
    implementation("androidx.activity:activity:1.10.1")
    implementation("androidx.constraintlayout:constraintlayout:2.2.1")

    implementation(project(":scale"))

    // XPopup  https://github.com/junixapp/XPopup
    implementation("com.github.li-xiaojun:XPopup:2.10.0")
    // XPopup扩展的选择器弹窗 https://github.com/junixapp/XPopupExt
    implementation("com.github.li-xiaojun:XPopupExt:1.0.1")

    //MMKV：https://github.com/Tencent/MMKV
    implementation("com.tencent:mmkv-static:2.2.2")

    //AndroidUtilCode  https://github.com/Blankj/AndroidUtilCode
    implementation("com.blankj:utilcodex:1.31.1")

    // Net https://github.com/liangjingkanji/Net
    implementation("com.github.liangjingkanji:Net:3.7.0")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.10.2")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2") // 协程(版本自定)
    implementation("com.squareup.okhttp3:okhttp:4.12.0") // 要求OkHttp4以上
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0") //日志工具

    // 列表框架 https://github.com/liangjingkanji/BRV
    implementation("com.github.liangjingkanji:BRV:1.6.1")

    // ShapeView https://github.com/getActivity/ShapeView
    implementation("com.github.getActivity:ShapeView:9.8")

    //权限申请 https://github.com/getActivity/XXPermissions
    implementation("com.github.getActivity:XXPermissions:25.0")

    // Toaster https://github.com/getActivity/Toaster
    implementation("com.github.getActivity:Toaster:13.2")

    //Gson 解析容错：https://github.com/getActivity/GsonFactory
    implementation("com.google.code.gson:gson:2.13.1")
    implementation("com.github.getActivity:GsonFactory:10.0")

    // 云加密
    implementation("com.ygxj.activate-sdk:active:2.0.4")

    // LitePal https://github.com/guolindev/LitePal
    implementation("org.litepal.guolindev:core:3.2.3")

    // Glide
    implementation("com.github.bumptech.glide:glide:4.16.0")

    // exoplayer
    implementation("com.google.android.exoplayer:exoplayer:2.19.1")
    implementation("com.google.android.exoplayer:exoplayer-ui:2.19.1")

    //蓝牙库 https://github.com/buhuiming/BleCore
    implementation("com.github.buhuiming:BleCore:2.5.0")

    // AutoSize尺寸自适应框架 https://github.com/JessYanCoding/AndroidAutoSize
    implementation("com.github.JessYanCoding:AndroidAutoSize:v1.2.1")

    implementation("com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.1")
}