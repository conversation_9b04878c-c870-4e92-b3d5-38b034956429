package com.ygxj.prshs.data.entity

import androidx.databinding.BaseObservable
import com.drake.brv.item.ItemExpand

/**
 * 危机事件分类
 */
data class DangerCategoryEntity(
    val id: Int,
    val categoryName: String,
    val events: List<DangerEventEntity>
) : ItemExpand {

    override var itemExpand: Boolean = true

    override var itemGroupPosition: Int = id

    override fun getItemSublist(): List<Any?>? {
        return events
    }

}

/**
 * 危机事件
 */
data class DangerEventEntity(
    val eventName: String,
    var selected: Boolean = false
) : BaseObservable()
