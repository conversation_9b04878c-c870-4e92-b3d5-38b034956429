package com.ygxj.prshs.data.manager

import com.tencent.mmkv.MMKV
import com.ygxj.prshs.data.entity.UserEntity
import com.ygxj.prshs.ext.toJson
import com.ygxj.prshs.ext.toObject

object UserManager {

    private val mmkv: MMKV by lazy {
        MMKV.mmkvWithID("UserManager")
    }

    var loginUser: UserEntity?
        get() = mmkv.decodeString("loginUser")?.toObject(UserEntity::class.java)
        set(value) {
            mmkv.encode("loginUser", value?.toJson())
        }
}