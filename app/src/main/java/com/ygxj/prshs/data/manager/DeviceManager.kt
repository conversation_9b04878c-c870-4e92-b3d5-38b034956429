package com.ygxj.prshs.data.manager

import com.tencent.mmkv.MMKV

/**
 * 蓝牙设备管理
 */
object DeviceManager {

    private val mmkv: MMKV by lazy {
        MMKV.mmkvWithID("DeviceManager")
    }

    /**
     * 指脉设备名称
     */
    var fingerName: String
        get() = mmkv.decodeString("fingerName", "BerryMed").toString()
        set(value) {
            mmkv.encode("fingerName", value)
        }

    /**
     * 指脉设备ServiceUUID
     */
    var fingerServiceUUID = "49535343-fe7d-4ae5-8fa9-9fafd205e455"

    /**
     * 指脉设备CharacterUUID
     */
    var fingerCharacterUUID = "49535343-1e4d-4bd9-ba61-23c647249616"

    /**
     * 指脉设备mac地址
     */
    var fingerMac: String?
        get() = mmkv.decodeString("fingerMac")
        set(value) {
            mmkv.encode("fingerMac", value)
        }

    /**
     * 清除用户信息
     */
    fun clearDeviceData() {
        mmkv.clearAll()
    }
}