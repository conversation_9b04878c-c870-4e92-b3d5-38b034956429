package com.ygxj.prshs.data.repository

import com.ygxj.prshs.constant.UserRole
import com.ygxj.prshs.data.entity.UserEntity
import org.litepal.LitePal

object UserRepository {

    fun checkDefaultAdminUser() {
        val admin = findUserByAccount("admin")
        if (admin == null) {
            // 创建默认管理员
            val user = UserEntity(
                account = "admin",
                password = "123456",
                role = UserRole.ADMIN,
                name = "admin",
                birthDate = "2000年01月01日",
                sex = "男"
            )
            user.save()
        }
    }

    fun checkDefaultGuestUser() {
        val guest = findUserByAccount("游客")
        if (guest == null) {
            // 创建默认游客
            val user = UserEntity(
                account = "游客",
                password = "123456",
                role = UserRole.GUEST,
                name = "游客",
                birthDate = "2000年01月01日",
                sex = "男"
            )
            user.save()
        }
    }

    /**
     * 根据account查找用户
     */
    fun findUserByAccount(account: String): UserEntity? {
        return LitePal.where("account = ?", account).findFirst(UserEntity::class.java)
    }

    /**
     * 根据id查找用户
     */
    fun findUserById(id: Int): UserEntity? {
        return LitePal.where("id = ?", id.toString()).findFirst(UserEntity::class.java)
    }

    /**
     * 修改用户
     */
    fun updateUser(user: UserEntity) {
        user.saveOrUpdate("id = ?", user.id.toString())
    }
}