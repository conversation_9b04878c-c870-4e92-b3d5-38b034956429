package com.ygxj.prshs.data.repository

import com.ygxj.prshs.data.entity.EmergencyProfileEntity
import org.litepal.LitePal

/**
 * 急救档案相关数据
 */
object EmergencyProfileRepository {

    /**
     * 根据用户id查找急救档案
     */
    fun findEmergencyProfileByUserId(userId: Int): EmergencyProfileEntity? {
        return LitePal.where("userId = ?", userId.toString()).findFirst(EmergencyProfileEntity::class.java)
    }

    /**
     * 保存或更新急救档案
     */
    fun saveOrUpdate(emergencyProfile: EmergencyProfileEntity) {
        emergencyProfile.saveOrUpdate("userId = ?", emergencyProfile.userId.toString())
    }
}