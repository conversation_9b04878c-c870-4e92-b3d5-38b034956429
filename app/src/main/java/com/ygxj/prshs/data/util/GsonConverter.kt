package com.ygxj.prshs.data.util

import com.drake.net.convert.JSONConvert
import com.hjq.gson.factory.GsonFactory
import com.ygxj.prshs.data.entity.BaseEntity
import org.json.JSONObject
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type

class GsonConverter : JSONConvert(
    success = "0",
    code = "status",
    message = "message"
) {

    companion object {

        private val gson = GsonFactory.getSingletonGson()
    }

    override fun <R> String.parseBody(succeed: Type): R? {
        return if (isBaseDataType(succeed)) {
            gson.fromJson<R>(this, succeed)
        } else {
            val string = try {
                JSONObject(this).getString("data")
            } catch (e: Exception) {
                this
            }
            gson.fromJson<R>(string, succeed)
        }
    }

    /**
     * 判断succeed是不是BaseData类型，如果不是就解析data中的数据
     */
    private fun isBaseDataType(type: Type): Boolean {
        if (type is ParameterizedType) {
            val rawType = type.rawType

            if (rawType is Class<*>) {
                if (BaseEntity::class.java.isAssignableFrom(rawType)) {
                    return true
                }
            }
        }

        return false
    }
}