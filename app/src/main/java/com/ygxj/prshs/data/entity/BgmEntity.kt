package com.ygxj.prshs.data.entity

import android.graphics.Color
import androidx.core.graphics.toColorInt
import androidx.databinding.BaseObservable

data class BgmEntity(
    val id: Int,
    val name: String,
    val resId: Int,
    var checked: Boolean = false
) : BaseObservable() {

    fun getStatusTextColor(): Int {
        return if (checked) {
            "#5ec0fe".toColorInt()
        } else {
            Color.WHITE
        }
    }
}
