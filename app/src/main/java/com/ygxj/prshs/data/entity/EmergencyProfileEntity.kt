package com.ygxj.prshs.data.entity

import com.blankj.utilcode.constant.TimeConstants
import com.blankj.utilcode.util.TimeUtils
import org.litepal.annotation.Column
import org.litepal.crud.LitePalSupport
import java.util.Date

/**
 * 用户急救档案
 */
data class EmergencyProfileEntity(
    val userId: Int,
    // 联系方式
    val phone: String,
    // 紧急联系人
    val emergencyName: String,
    // 紧急联系人电话
    val emergencyPhone: String,
    // 危机事件发生日期 yyyy年MM月dd日
    val dangerDate: String,
    // 危机事件,多个,#号分割
    val dangerEvents: String,
    // 危机事件描述
    val dangerDesc: String
) : LitePalSupport() {

    @Column(unique = true)
    val id: Int = 0

    /**
     * 判断危机事件是否发生在一个月内
     */
    fun isInOneMonth(): Boolean {
        return calculateDaySpan() <= 30
    }

    /**
     * 判断危机事件是否发生在1-3个月内
     */
    fun isInThreeMonth(): Boolean {
        val span = calculateDaySpan()
        return span > 30 && span <= 90
    }

    /**
     * 判断危机事件是否发生在3个月前
     */
    fun isBeforeThreeMonth(): Boolean {
        return calculateDaySpan() > 90
    }

    /**
     * 计算危机事件发生日期与当前日期的天数差
     */
    private fun calculateDaySpan(): Long {
        val nowDateTime = Date()
        val dangerDateTime = TimeUtils.string2Date(dangerDate, "yyyy年MM月dd日")
        val daySpan = TimeUtils.getTimeSpan(nowDateTime, dangerDateTime, TimeConstants.DAY)
        return daySpan
    }
}
