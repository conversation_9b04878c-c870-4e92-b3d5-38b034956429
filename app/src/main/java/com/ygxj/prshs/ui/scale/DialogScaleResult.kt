package com.ygxj.prshs.ui.scale

import android.content.Context
import androidx.databinding.DataBindingUtil
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.CenterPopupView
import com.ygxj.prshs.R
import com.ygxj.prshs.databinding.DialogScaleResultBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.ext.toPx
import com.ygxj.scale.entity.ScaleResultEntity

/**
 * 快速评估结果
 */
class DialogScaleResult(
    context: Context,
    private val scaleId: Int,
    private val result: ScaleResultEntity,
    private val onConfirmClick: () -> Unit,
    private val onCancelClick: () -> Unit,
) : CenterPopupView(context) {

    private lateinit var binding: DialogScaleResultBinding

    override fun getImplLayoutId() = R.layout.dialog_scale_result

    override fun onCreate() {
        super.onCreate()
        binding = DataBindingUtil.bind(popupImplView)!!

        binding.btnGoTrain.click { dismissWith { onConfirmClick() } }
        binding.btnLater.click { dismissWith { onCancelClick() } }

        var finalResult = result.YJ
        // 把结果中所有的|替换成\n\t\t,注意,可能有多个
        finalResult = finalResult.replace("\\|".toRegex(), "\n\t\t")
        binding.tvResult.text = "\t\t${finalResult}"

    }

    override fun getPopupWidth(): Int {
        return 753.toPx()
    }

    override fun getPopupHeight(): Int {
        return 464.toPx()
    }

    fun build(): BasePopupView {
        return XPopup.Builder(context)
            .isViewMode(true)
            .asCustom(this)
    }
}