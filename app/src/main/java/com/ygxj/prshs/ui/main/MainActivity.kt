package com.ygxj.prshs.ui.main

import android.content.Context
import android.content.Intent
import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.constant.UserRole
import com.ygxj.prshs.data.manager.UserManager
import com.ygxj.prshs.data.repository.EmergencyProfileRepository
import com.ygxj.prshs.databinding.ActivityMainBinding
import com.ygxj.prshs.ui.emergencyProfile.EmergencyProfileActivity
import com.ygxj.prshs.ui.eyeMove.EyeMoveTrainActivity

class MainActivity : NoVMBaseActivity<ActivityMainBinding>(R.layout.activity_main) {

    companion object {

        fun start(context: Context) {
            context.startActivity(Intent(context, MainActivity::class.java))
        }
    }

    override fun initData() {
        binding.flContainer.postDelayed({
            EyeMoveTrainActivity.start(this, "")
        }, 500)
    }

    override fun onResume() {
        super.onResume()

        val loginUser = UserManager.loginUser ?: return

        // ===== 游客角色,只显示部分菜单
        if (loginUser.role == UserRole.GUEST) {
            val ft = supportFragmentManager.beginTransaction()
            ft.replace(R.id.flContainer, HomeFragmentGuest.newInstance())
            ft.commit()
            return
        }

        // ===== 非游客角色
        val profile = EmergencyProfileRepository.findEmergencyProfileByUserId(loginUser.id)
        // 非游客角色没有急救档案时,则跳转到急救档案页面
        if (profile == null) {
            EmergencyProfileActivity.start(this, loginUser.id)
            return
        }
        // 非游客角色,根据急救档案中的危机事件发生时间不同,显示不同的菜单
        val ft = supportFragmentManager.beginTransaction()
        when {
            // 危机事件发生1个月内
            profile.isInOneMonth() -> ft.replace(R.id.flContainer, HomeFragmentInOneMonth.newInstance())
            // 危机事件发生1个月到3个月之间
            profile.isInThreeMonth() -> ft.replace(R.id.flContainer, HomeFragmentInThreeMonth.newInstance())
            // 危机事件发生3个月以上
            else -> ft.replace(R.id.flContainer, HomeFragmentBeforeThreeMonth.newInstance())
        }
        ft.commit()

    }

}