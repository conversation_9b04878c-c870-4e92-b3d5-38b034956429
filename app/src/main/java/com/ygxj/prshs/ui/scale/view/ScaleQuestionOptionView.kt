package com.ygxj.prshs.ui.scale.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.core.view.doOnAttach
import androidx.core.view.doOnDetach
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.drake.net.time.Interval
import com.hjq.toast.Toaster
import com.ygxj.prshs.BuildConfig
import com.ygxj.prshs.databinding.ViewScaleQuestionOptionBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.ext.visible
import com.ygxj.prshs.ui.scale.ScaleTestModel
import java.util.concurrent.TimeUnit

class ScaleQuestionOptionView(
    context: Context,
    attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

    private var binding = ViewScaleQuestionOptionBinding.inflate(LayoutInflater.from(context), this, true)
    private var model: ScaleTestModel? = null
    private var adapter = ScaleQuestionOptionAdapter()
    private var autoTestTimer: Interval? = null

    init {
        if (!isInEditMode) {
            doOnAttach {
                binding.recyclerView.itemAnimator = null
                binding.recyclerView.adapter = adapter
            }
            doOnDetach {
                stopTimer()
            }

            // 自动测试功能,用于调试
            if (BuildConfig.DEBUG) {
                binding.btnAutoTest.visible()
                binding.btnAutoTest.click {
                    model!!.startTimestamp = System.currentTimeMillis()
                    autoTest()
                }
            }
        }
    }

    fun setModel(model: ScaleTestModel?) {
        model ?: return
        this.model = model

        adapter.setOnItemClickListener { _, _, position ->
            // 做了第1次选后,开始计时
            if (model.questionIndex.value == 0) {
                model.startTimestamp = System.currentTimeMillis()
            }
            adapter.data.forEach { questionOptions ->
                questionOptions.selected = false
            }
            adapter.data[position].selected = true
            adapter.notifyItemRangeChanged(0, adapter.data.size)
            if (model.questionIndex.value!! >= model.scale.value!!.scaleQuestions.lastIndex) {
                Toaster.show("测试已完成，如需提交，请点击提交按钮")
                return@setOnItemClickListener
            }
            binding.recyclerView.postDelayed({
                // 延迟时间越大时,快速点击就越有可能出错,
                // 在这个时间结束之前,如果快速点击了两次,就有可能会跳过一题
                model.questionIndex.value = model.questionIndex.value!! + 1
            }, 10)
        }
    }

    fun setQuestionIndex(index: Int) {
        if (model == null) return
        // 瑞文量表使用GridLayoutManager
        if (binding.recyclerView.layoutManager == null) {
            if (model!!.scale.value!!.scaleId == 50) {
                binding.recyclerView.layoutManager = GridLayoutManager(context, 4)
            } else {
                binding.recyclerView.layoutManager = LinearLayoutManager(context)
            }
        }
        val questionsList = model?.scale?.value?.scaleQuestions
        if (questionsList.isNullOrEmpty()) return

        val question = questionsList[index]
        adapter.setNewData(question.questionOptions.toMutableList())
    }

    /**
     *  自动测试功能,用于调试
     */
    private fun autoTest() {
        if (autoTestTimer != null) {
            stopTimer()
            return
        }
        binding.btnAutoTest.text = "自动测试进行中"
        autoTestTimer = Interval(50, TimeUnit.MILLISECONDS).subscribe {
            val randomIndex = (0 until model!!.scale.value!!.scaleQuestions[model!!.questionIndex.value!!].questionOptions.size).random()
            adapter.data.forEach { questionOptions ->
                questionOptions.selected = false
            }
            adapter.data[randomIndex].selected = true
            adapter.notifyItemRangeChanged(0, adapter.data.size)

            if (model!!.questionIndex.value!! >= model!!.scale.value!!.scaleQuestions.lastIndex) {
                Toaster.show("自动测试已完成")
                stopTimer()
            } else {
                binding.recyclerView.postDelayed({
                    model!!.questionIndex.value = model!!.questionIndex.value!! + 1
                }, 10)
            }
        }.start()
    }

    private fun stopTimer() {
        autoTestTimer?.stop()
        autoTestTimer = null
        binding.btnAutoTest.text = "自动测试关闭中"
    }
}

