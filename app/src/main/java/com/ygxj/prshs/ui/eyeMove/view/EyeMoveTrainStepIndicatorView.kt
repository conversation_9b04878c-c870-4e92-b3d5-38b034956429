package com.ygxj.prshs.ui.eyeMove.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.core.graphics.toColorInt
import com.drake.brv.annotaion.DividerOrientation
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.divider
import com.drake.brv.utils.grid
import com.drake.brv.utils.setup
import com.ygxj.prshs.R
import com.ygxj.prshs.data.entity.EyeMoveTrainStepEntity
import com.ygxj.prshs.databinding.ItemEyeMoveTrainStepIndicatorBinding
import com.ygxj.prshs.databinding.ViewEyeMoveTrainStepIndicatorBinding

/**
 * 眼动训练步骤指示器
 */
class EyeMoveTrainStepIndicatorView(context: Context, attrs: AttributeSet?) : LinearLayout(context, attrs) {

    private val binding = ViewEyeMoveTrainStepIndicatorBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        orientation = VERTICAL
        if (!isInEditMode) {
            configRV()
        }
    }

    private fun configRV() {
        val list = arrayListOf<EyeMoveTrainStepEntity>()
        for (i in 0 until 8) {
            list.add(EyeMoveTrainStepEntity("阶段${i + 1}"))
        }
        binding.rv.grid(2).divider {
            setDivider(10, true)
            orientation = DividerOrientation.GRID
        }.setup {
            addType<EyeMoveTrainStepEntity>(R.layout.item_eye_move_train_step_indicator)
            onBind {
                val item = getModel<EyeMoveTrainStepEntity>()
                getBinding<ItemEyeMoveTrainStepIndicatorBinding>().let {
                    it.tvIndicator.text = item.stepName
                    it.tvIndicator.shapeDrawableBuilder
                        .setSolidColor((if (item.checked) "#5ec0fe" else "#333F7EB4").toColorInt())
                        .intoBackground()
                }
            }
            singleMode = true
            onChecked { position, isChecked, isAllChecked ->
                val item = getModel<EyeMoveTrainStepEntity>(position)
                item.checked = isChecked
                notifyItemChanged(position)
            }
        }.models = list
    }

    fun setCurrentStep(step: Int) {
        binding.rv.bindingAdapter.setChecked(step, true)
    }
}