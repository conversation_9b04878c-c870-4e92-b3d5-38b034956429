package com.ygxj.prshs.ui.eyeMove.fragment

import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.base.NoVMBaseFragment
import com.ygxj.prshs.databinding.FragmentEyeMoveTrainStep3Binding

class EyeMoveTrainStep3Fragment : NoVMBaseFragment<FragmentEyeMoveTrainStep3Binding>(
    R.layout.fragment_eye_move_train_step3
) {

    companion object {

        fun getInstance() = EyeMoveTrainStep3Fragment()
    }

    override fun initData() {

    }

}
