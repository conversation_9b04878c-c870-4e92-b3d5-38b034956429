package com.ygxj.prshs.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.ygxj.prshs.R
import com.ygxj.prshs.databinding.ViewHomeMenuItemBinding

class HomeMenuItemView(
    context: Context,
    attrs: AttributeSet? = null,
) : FrameLayout(context, attrs) {

    private val binding = ViewHomeMenuItemBinding.inflate(LayoutInflater.from(context), this, true)

    init {

        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.HomeMenuItemView)
        val iconResId = typedArray.getResourceId(R.styleable.HomeMenuItemView_menu_icon, 0)
        val title = typedArray.getString(R.styleable.HomeMenuItemView_menu_title)
        typedArray.recycle()

        setBackgroundResource(R.mipmap.bg_home_menu_out)
        if (iconResId != 0) {
            binding.icon.setImageResource(iconResId)
        }
        binding.tvTitle.text = title
    }

    fun getTitle(): String {
        return binding.tvTitle.text.toString()
    }
}
