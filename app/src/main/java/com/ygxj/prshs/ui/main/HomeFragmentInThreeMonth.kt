package com.ygxj.prshs.ui.main

import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseFragment
import com.ygxj.prshs.databinding.FragmentHomeInThreeMonthBinding

/**
 * 危机事件发生1-3个月内可见的首页
 */
class HomeFragmentInThreeMonth : NoVMBaseFragment<FragmentHomeInThreeMonthBinding>(
    R.layout.fragment_home_in_three_month
) {

    companion object {

        fun newInstance() = HomeFragmentInThreeMonth()
    }

    override fun initData() {

    }

}