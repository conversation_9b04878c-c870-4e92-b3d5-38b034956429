package com.ygxj.prshs.ui.main

import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseFragment
import com.ygxj.prshs.databinding.FragmentHomeGuestBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.ui.video.VideoPlayActivity

/**
 * 游客可见的首页
 */
class HomeFragmentGuest : NoVMBaseFragment<FragmentHomeGuestBinding>(
    R.layout.fragment_home_guest
) {

    companion object {

        fun newInstance() = HomeFragmentGuest()
    }

    override fun initData() {

        binding.btnVideo1.click {
            val url = "android.resource://${requireContext().packageName}/${R.raw.hu_xi_fang_song_fa}"
            VideoPlayActivity.start(requireContext(), url, it.getTitle())
        }
        binding.btnVideo2.click {
            val url = "android.resource://${requireContext().packageName}/${R.raw.xiang_xiang_fang_song_fa}"
            VideoPlayActivity.start(requireContext(), url, it.getTitle())
        }
        binding.btnVideo3.click {
            val url = "android.resource://${requireContext().packageName}/${R.raw.jian_jin_ji_rou_fang_song_fa}"
            VideoPlayActivity.start(requireContext(), url, it.getTitle())
        }

    }
}