package com.ygxj.prshs.ui.login

import android.content.Context
import android.content.Intent
import com.hjq.toast.Toaster
import com.ygxj.prshs.BuildConfig
import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.data.manager.UserManager
import com.ygxj.prshs.data.repository.UserRepository
import com.ygxj.prshs.databinding.ActivityLoginBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.ui.main.MainActivity
import com.ygxj.prshs.ui.register.RegisterActivity

class LoginActivity : NoVMBaseActivity<ActivityLoginBinding>(R.layout.activity_login) {

    companion object {

        fun start(context: Context) {
            context.startActivity(Intent(context, LoginActivity::class.java))
        }
    }

    override fun initData() {
        // 登录
        binding.btnLogin.click {
            login()
        }
        // 注册
        binding.btnRegister.click { register() }
        // 游客登录
        binding.btnGuestLogin.click { guestLogin() }


        if (BuildConfig.DEBUG) {
            binding.etAccount.setText("ttt")
            binding.etPassword.setText("123456")

            binding.etAccount.postDelayed({
                binding.btnLogin.performClick()
            }, 200)
        }
    }

    /**
     * 登录
     */
    private fun login() {
        val account = binding.etAccount.text.toString()
        val password = binding.etPassword.text.toString()
        if (account.isEmpty()) {
            Toaster.show("请输入账号")
            return
        }
        if (password.isEmpty()) {
            Toaster.show("请输入密码")
            return
        }
        // 根据账号查找用户
        binding.btnLogin.isEnabled = false
        val user = UserRepository.findUserByAccount(account)
        if (user == null) {
            Toaster.show("用户不存在")
            binding.btnLogin.isEnabled = true
            return
        }
        if (user.password != password) {
            binding.btnLogin.isEnabled = true
            Toaster.show("密码错误")
            return
        }
        UserManager.loginUser = user
        MainActivity.start(this)
        finish()
    }

    /**
     * 注册
     */
    private fun register() {
        RegisterActivity.start(this)
    }

    /**
     * 游客登录
     */
    private fun guestLogin() {
        val guest = UserRepository.findUserByAccount("游客")
        if (guest == null) {
            Toaster.show("游客不存在")
            return
        }
        UserManager.loginUser = guest
        MainActivity.start(this)
        finish()
    }

}