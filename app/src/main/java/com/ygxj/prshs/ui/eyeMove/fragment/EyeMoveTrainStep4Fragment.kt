package com.ygxj.prshs.ui.eyeMove.fragment

import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.base.NoVMBaseFragment
import com.ygxj.prshs.databinding.FragmentEyeMoveTrainStep4Binding

class EyeMoveTrainStep4Fragment : NoVMBaseFragment<FragmentEyeMoveTrainStep4Binding>(
    R.layout.fragment_eye_move_train_step4
) {

    companion object {

        fun getInstance() = EyeMoveTrainStep4Fragment()
    }

    override fun initData() {

    }

}
