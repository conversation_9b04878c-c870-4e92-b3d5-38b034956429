@file:Suppress("DEPRECATION")

package com.ygxj.prshs.ui.trainPlan

import android.content.Context
import android.content.Intent
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageButton
import androidx.core.net.toUri
import com.bhm.ble.BleManager
import com.bhm.ble.data.BleDescriptorGetType
import com.bhm.ble.data.BleScanFailType
import com.bhm.ble.device.BleDevice
import com.bhm.ble.log.BleLogger
import com.blankj.utilcode.util.LogUtils
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.SimpleExoPlayer
import com.hjq.toast.Toaster
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.util.XPopupUtils
import com.ygxj.prshs.BuildConfig
import com.ygxj.prshs.R
import com.ygxj.prshs.base.App
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.data.entity.BgmEntity
import com.ygxj.prshs.data.entity.IssueEntity
import com.ygxj.prshs.data.entity.TrainPlanEntity
import com.ygxj.prshs.data.manager.DeviceManager
import com.ygxj.prshs.databinding.ActivityTrainPlanDetailBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.util.FingerDataUtil

class TrainPlanDetailActivity : NoVMBaseActivity<ActivityTrainPlanDetailBinding>(
    R.layout.activity_train_plan_detail
) {

    private val trainPlan by lazy { intent.getSerializableExtra("trainPlan") as? TrainPlanEntity }
    private val selectedIssues by lazy { intent.getStringExtra("selectedIssues") }

    private var isFullscreen = false
    private var videoPlayer: ExoPlayer? = null
    private var audioPlayer: SimpleExoPlayer? = null
    private var currentPlayBgm: BgmEntity? = null

    companion object {

        fun start(context: Context, trainPlan: TrainPlanEntity, selectedIssues: String) {
            val intent = Intent(context, TrainPlanDetailActivity::class.java)
            intent.putExtra("trainPlan", trainPlan)
            intent.putExtra("selectedIssues", selectedIssues)
            context.startActivity(intent)
        }
    }

    override fun initData() {
        // 初始化视频播放器
        initVideoPlayer()
        // 初始化音频播放器
        initAudioPlayer()

        if (BuildConfig.DEBUG) {
            Toaster.show("调试模式，跳过指脉连接")
            playVideo()
        } else {
            XPopup.Builder(this).isViewMode(true).dismissOnTouchOutside(false).dismissOnBackPressed(false)
                .asConfirm(
                    "提示", "请佩戴好指脉设备，点击按钮开始连接设备。", "退出", "开始连接", {
                        startScanDevice()
                    }, {
                        finish()
                    },
                    false, R.layout.dialog_custom_comfirm
                ).show()
        }

        // 点击BGM按钮
        binding.btnBGM.click {
            DialogTrainPlanBGM(it.context, currentPlayBgm) { bgm ->
                playAudio(bgm)
            }.build().show()
        }
        // 点击音量按钮
        binding.btnVolume.click {
            DialogTrainVolume(it.context, audioPlayer!!, videoPlayer!!).build().show()
        }
    }

    /**
     * 初始化 ExoPlayer 播放器
     */
    private fun initVideoPlayer() {

        videoPlayer = ExoPlayer.Builder(this).build()
        binding.playerView.player = videoPlayer
        videoPlayer?.playWhenReady = true // 自动播放

        // 全屏按钮点击事件
        binding.playerView.findViewById<ImageButton>(R.id.btnFullScreen).click {
            if (isFullscreen) {
                // ===== 退出全屏
                // 获取playerView的父布局,此时应该是window.decorView
                val parent = binding.playerView.parent as? ViewGroup
                // 将playerView从父布局移除
                parent?.removeView(binding.playerView)
                // 将playerView添加到原本所在的父布局中,此时layoutParam的宽高可以使用matchParent,因为是相对父布局的
                binding.playerContainer.addView(binding.playerView, FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT))
            } else {
                // ==== 进入全屏
                // 获取playerView的父布局,此时应该是binding.clPlayerContainer
                val parent = binding.playerView.parent as? ViewGroup
                // 将playerView从父布局移除
                parent?.removeView(binding.playerView)
                // 将playerView添加到window.decorView中,此时layoutParams的高度不能使用matchParent,因为可能会有底部导航栏的存在,所以要使用screenHeight
                val windowDecorView = window?.decorView as? ViewGroup
                windowDecorView?.addView(binding.playerView, FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, XPopupUtils.getScreenHeight(this)))
            }
            isFullscreen = !isFullscreen
        }
    }

    private fun initAudioPlayer() {
        audioPlayer = SimpleExoPlayer.Builder(this).build()
        audioPlayer?.playWhenReady = true // 自动播放

        // 设置音频播放器声音
        audioPlayer?.volume = 1.0f // 默认音量为1.0
    }

    private fun playVideo() {
        val resId = trainPlan?.resId
        val url = "android.resource://${App.context.packageName}/${resId}"
        val mediaItem = MediaItem.fromUri(url.toUri())
        videoPlayer?.setMediaItem(mediaItem)
        videoPlayer?.prepare()
    }

    private fun playAudio(bgm: BgmEntity) {
        val resId = bgm.resId
        val url = "android.resource://${App.context.packageName}/${resId}"
        val mediaItem = MediaItem.fromUri(url.toUri())
        audioPlayer?.setMediaItem(mediaItem)
        audioPlayer?.prepare()
        this.currentPlayBgm = bgm
    }

    /**
     * 开始扫描设备
     */
    private fun startScanDevice() {
        BleManager.get().startScan {
            onScanStart { showLoadingDialog("设备搜索中") }
            onLeScan { bleDevice, currentScanCount ->
                //可以根据currentScanCount是否已有清空列表数据
            }
            onLeScanDuplicateRemoval { bleDevice, currentScanCount ->
                //与onLeScan区别之处在于：同一个设备只会出现一次

            }
            onScanComplete { bleDeviceList, bleDeviceDuplicateRemovalList ->
                //扫描到的数据是所有扫描次数的总和
                hideLoadingDialog()
                handleScanResult(bleDeviceDuplicateRemovalList)
            }
            onScanFail {
                val msg: String = when (it) {
                    is BleScanFailType.UnSupportBle -> "BleScanFailType.UnSupportBle: 设备不支持蓝牙"
                    is BleScanFailType.NoBlePermission -> "BleScanFailType.NoBlePermission: 权限不足，请检查"
                    is BleScanFailType.GPSDisable -> "BleScanFailType.BleDisable: 设备未打开GPS定位"
                    is BleScanFailType.BleDisable -> "BleScanFailType.BleDisable: 蓝牙未打开"
                    is BleScanFailType.AlReadyScanning -> "BleScanFailType.AlReadyScanning: 正在扫描"
                    is BleScanFailType.ScanError -> {
                        "BleScanFailType.ScanError: ${it.throwable?.message}"
                    }
                }
                BleLogger.e(msg)
                Toaster.show(msg)
                hideLoadingDialog()
            }
        }
    }

    /**
     * 处理扫描结果
     */
    private fun handleScanResult(deviceList: MutableList<BleDevice>) {
        val fingerList = deviceList.filter { it.deviceName?.contains(DeviceManager.fingerName) == true }
        if (deviceList.isEmpty() || fingerList.isEmpty()) {
            XPopup.Builder(this).isViewMode(true).dismissOnTouchOutside(false).dismissOnBackPressed(false)
                .asConfirm(
                    "提示", "未能找到指脉设备，请确保设备处于开启状态后，点击按钮尝试再次搜索", "退出", "再次搜索", {
                        startScanDevice()
                    }, {
                        finish()
                    }, false, R.layout.dialog_custom_comfirm
                ).show()
            return
        }
        val fingerDevice = fingerList.first()
        BleManager.get().removeBleEventCallback(fingerDevice)

        BleManager.get().connect(fingerDevice, false) {
            onConnectStart {
                showLoadingDialog("正在连接设备")
            }
            onConnectFail { bleDevice, connectFailType ->
                hideLoadingDialog()
                XPopup.Builder(this@TrainPlanDetailActivity)
                    .dismissOnTouchOutside(false)
                    .dismissOnBackPressed(false)
                    .isViewMode(true)
                    .asConfirm("提示", "指脉设备连接连接失败，请检查设备是否正常，点击重新连接或退出。", "退出", "重新连接", {
                        startScanDevice()
                    }, {
                        finish()
                    }, false, R.layout.dialog_custom_comfirm).show()

            }
            onDisConnected { isActiveDisConnected, bleDevice, _, _ ->
                hideLoadingDialog()
                videoPlayer?.pause()
                XPopup.Builder(this@TrainPlanDetailActivity)
                    .dismissOnTouchOutside(false)
                    .dismissOnBackPressed(false)
                    .isViewMode(true)
                    .asConfirm("提示", "指脉设备连接已断开，请检查设备是否正常。\n您可以点击重新连接按钮或退出训练。", "退出", "重新连接", {
                        startScanDevice()
                    }, {
                        finish()
                    }, false, R.layout.dialog_custom_comfirm).show()
            }
            onConnectSuccess { bleDevice, _ ->
                Toaster.show("设备连接成功")
                hideLoadingDialog()
                startNotify(bleDevice)

                if (videoPlayer?.mediaItemCount == 0) {
                    playVideo()
                } else {
                    videoPlayer?.play()
                }
            }
        }
    }

    /**
     * 开始Notify
     */
    private fun startNotify(bleDevice: BleDevice) {
        BleManager.get().notify(bleDevice, DeviceManager.fingerServiceUUID, DeviceManager.fingerCharacterUUID, BleDescriptorGetType.Default) {
            onNotifyFail { _, _, _ ->
                BleLogger.e("获取数据失败")
            }
            onNotifySuccess { _, _ ->
                BleLogger.e("指脉获取数据成功")
            }
            onCharacteristicChanged { bleDevice, _, value ->
                val data = FingerDataUtil.parseFingerData(value)
                if (data != null) {
                    LogUtils.e("$data")
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        release()
    }

    /**
     * 释放 ExoPlayer 播放器资源
     */
    private fun release() {
        BleManager.get().disConnectAll()
        videoPlayer?.release()
        videoPlayer = null
        audioPlayer?.release()
        audioPlayer = null
    }

}