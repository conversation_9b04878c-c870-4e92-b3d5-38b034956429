package com.ygxj.prshs.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.ygxj.prshs.databinding.ViewLogoBinding

class LogoView(context: Context, attrs: AttributeSet?) : LinearLayout(context, attrs) {

    private val binding = ViewLogoBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        orientation = HORIZONTAL
        gravity = Gravity.CENTER
    }
}