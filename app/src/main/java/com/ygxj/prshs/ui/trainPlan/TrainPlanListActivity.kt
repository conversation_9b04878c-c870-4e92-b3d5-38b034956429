package com.ygxj.prshs.ui.trainPlan

import android.content.Context
import android.content.Intent
import com.drake.brv.annotaion.DividerOrientation
import com.drake.brv.utils.divider
import com.drake.brv.utils.grid
import com.drake.brv.utils.setup
import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.constant.TrainPlanUtil
import com.ygxj.prshs.data.entity.TrainPlanEntity
import com.ygxj.prshs.databinding.ActivityTrainPlanListBinding
import com.ygxj.prshs.ui.issues.DialogIssuesSelect
import com.ygxj.prshs.util.PermissionsUtils

/**
 * 训练方案列表
 */
class TrainPlanListActivity : NoVMBaseActivity<ActivityTrainPlanListBinding>(
    R.layout.activity_train_plan_list
) {

    companion object {

        fun start(context: Context) {
            context.startActivity(Intent(context, TrainPlanListActivity::class.java))
        }
    }

    override fun initData() {

        binding.rv.grid(3).divider {
            orientation = DividerOrientation.GRID
            setDivider(30, true)
        }.setup {
            addType<TrainPlanEntity>(R.layout.item_train_plan)
            onClick(R.id.llRoot) {
                val trainPlan = getModel<TrainPlanEntity>()
                onTrainPlanClick(trainPlan)
            }
        }.models = TrainPlanUtil.getTrainPlanList()
    }

    /**
     * 训练方案点击事件
     */
    private fun onTrainPlanClick(trainPlan: TrainPlanEntity) {
        PermissionsUtils.requestBlePermission(this) {
            DialogIssuesSelect(this) { selectedIssus ->
                TrainPlanDetailActivity.start(
                    this,
                    trainPlan,
                    selectedIssus.joinToString("|") { it.title }
                )
            }.build().show()

        }
    }
}