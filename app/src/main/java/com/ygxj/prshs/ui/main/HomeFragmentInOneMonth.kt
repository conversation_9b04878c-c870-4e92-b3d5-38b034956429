package com.ygxj.prshs.ui.main

import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseFragment
import com.ygxj.prshs.databinding.FragmentHomeInOneMonthBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.ui.emergencyAdjust.EmergencyAdjustActivity
import com.ygxj.prshs.ui.testAdjust.TestAdjustActivity

/**
 * 危机事件发生1个月内可见的首页
 */
class HomeFragmentInOneMonth : NoVMBaseFragment<FragmentHomeInOneMonthBinding>(
    R.layout.fragment_home_in_one_month
) {

    companion object {

        fun newInstance() = HomeFragmentInOneMonth()
    }

    override fun initData() {

        // 紧急调节
        binding.btnEmergencyAdjust.click { EmergencyAdjustActivity.start(requireContext()) }

        // 测试后调节
        binding.btnTestAdjust.click { TestAdjustActivity.start(requireContext()) }

        // 训练方案
        binding.btnTrainingPlan.click {
            //DialogIssuesSelect(it.context) { selectedIssues ->
            //    Toaster.show(selectedIssues.joinToString { issue -> issue.title })
            //}.build().show()
        }

    }
}