package com.ygxj.prshs.ui.main

import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseFragment
import com.ygxj.prshs.databinding.FragmentHomeBeforeThreeMonthBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.ui.eyeMove.EyeMoveTrainActivity
import com.ygxj.prshs.ui.issues.DialogIssuesSelect

/**
 * 危机事件发生在三个月前可见的首页
 */
class HomeFragmentBeforeThreeMonth : NoVMBaseFragment<FragmentHomeBeforeThreeMonthBinding>(
    R.layout.fragment_home_before_three_month
) {

    companion object {

        fun newInstance() = HomeFragmentBeforeThreeMonth()
    }

    override fun initData() {

        // 眼动训练
        binding.btnEyeMove.click {
            DialogIssuesSelect(requireContext()) { selectedIssues ->
                EyeMoveTrainActivity.start(requireContext(), selectedIssues)
            }.build().show()
        }
    }

}