package com.ygxj.prshs.ui.emergencyProfile

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import androidx.recyclerview.widget.GridLayoutManager
import com.blankj.utilcode.util.TimeUtils
import com.drake.brv.layoutmanager.HoverGridLayoutManager
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.setup
import com.hjq.toast.Toaster
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.util.KeyboardUtils
import com.lxj.xpopupext.popup.TimePickerPopup
import com.ygxj.prshs.BuildConfig
import com.ygxj.prshs.R
import com.ygxj.prshs.constant.DangerEvent
import com.ygxj.prshs.data.entity.DangerCategoryEntity
import com.ygxj.prshs.data.entity.DangerEventEntity
import com.ygxj.prshs.data.repository.EmergencyProfileRepository
import com.ygxj.prshs.databinding.ViewEmergencyProfileDangerInfoBinding
import com.ygxj.prshs.ext.SimpleTimePickerListener
import com.ygxj.prshs.ext.click
import java.util.Calendar
import java.util.Date

/**
 * 急救档案中的危机信息表单
 */
class EmergencyProfileDangerInfoFormView(
    context: Context, attrs: AttributeSet? = null
) : LinearLayout(context, attrs) {

    private val binding = ViewEmergencyProfileDangerInfoBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        orientation = VERTICAL
    }

    fun setUserId(userId: Int) {
        // 设置危机事件列表数据
        setDangerEventRV()

        // 设置已有的数据
        setExistingData(userId)

        // 点击危机事件发生日期
        binding.tvDangerDate.click {
            KeyboardUtils.hideSoftInput(it)
            val startDate = Calendar.getInstance().apply { set(1925, 1, 1) }
            val endDate = Calendar.getInstance()
            val popup = TimePickerPopup(context)
                .setDefaultDate(endDate)
                .setDateRange(startDate, endDate)
                .setTimePickerListener(object : SimpleTimePickerListener {
                    override fun onTimeConfirm(date: Date, view: View) {
                        binding.tvDangerDate.text = TimeUtils.date2String(date, "yyyy年MM月dd日")
                    }
                })
            XPopup.Builder(context).isViewMode(true).asCustom(popup).show()
        }

        if (BuildConfig.DEBUG) {
            binding.tvDangerDate.text = "2025年07月28日"
            binding.etDangerDesc.setText("123")
        }
    }

    /**
     * 设置已有的数据
     */
    private fun setExistingData(userId: Int) {
        val profile = EmergencyProfileRepository.findEmergencyProfileByUserId(userId) ?: return
        // 发生日期
        binding.tvDangerDate.text = profile.dangerDate
        // 危机事件描述
        binding.etDangerDesc.setText(profile.dangerDesc)
        // 危机事件类型
        val allEvents = binding.rvDangerEvent.bindingAdapter.models ?: emptyList()
        val dangerEvents = profile.dangerEvents.split("#")
        allEvents.forEachIndexed { index, item ->
            if (item is DangerEventEntity) {
                binding.rvDangerEvent.bindingAdapter.setChecked(index, dangerEvents.contains(item.eventName))
            }
        }
    }

    /**
     * 设置危机事件列表数据
     */
    private fun setDangerEventRV() {
        val layoutManager = HoverGridLayoutManager(context, 3)
        layoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                if (position < 0) return 1
                return when (binding.rvDangerEvent.bindingAdapter.getItemViewType(position)) {
                    R.layout.item_danger_category -> 3 // 类别标题,占据3列(即独占一行)
                    else -> 1 // 事件item,占1列,即3个一行
                }
            }
        }
        layoutManager.setScrollEnabled(false)
        binding.rvDangerEvent.layoutManager = layoutManager
        binding.rvDangerEvent.setup {
            addType<DangerCategoryEntity>(R.layout.item_danger_category)
            addType<DangerEventEntity>(R.layout.item_danger_event)

            onChecked { position, isChecked, isAllChecked ->
                val model = getModel<DangerEventEntity>(position)
                model.selected = isChecked
                model.notifyChange() // 通知UI跟随数据变化
            }

            onClick(R.id.cb) {
                val isChecked = getModel<DangerEventEntity>().selected
                setChecked(absoluteAdapterPosition, isChecked) // 在点击事件中触发选择事件, 即点击列表条目就选中
            }
        }.models = DangerEvent.getData()
    }

    fun checkFormIsCompleted(): Boolean {
        // 发生日期
        val dangerDate = binding.tvDangerDate.text.toString()
        if (dangerDate.isBlank()) {
            Toaster.show("请选择危机事件发生日期")
            return false
        }
        // 选择的危机事件类型
        val dangerEvents = binding.rvDangerEvent.bindingAdapter.models?.filterIsInstance<DangerEventEntity>()?.filter { it.selected }
        if (dangerEvents.isNullOrEmpty()) {
            Toaster.show("请选择危机事件类型")
            return false
        }
        // 危机事件描述
        val dangerDesc = binding.etDangerDesc.text.toString()
        if (dangerDesc.isBlank()) {
            Toaster.show("请输入危机事件描述")
            return false
        }
        return true
    }

    fun getFormData(): Map<String, String> {
        val dangerEvents = binding.rvDangerEvent.bindingAdapter.models?.filterIsInstance<DangerEventEntity>()?.filter { it.selected } ?: emptyList()
        return mapOf(
            "dangerDate" to binding.tvDangerDate.text.toString(),
            "dangerEvents" to dangerEvents.joinToString("#") { it.eventName },
            "dangerDesc" to binding.etDangerDesc.text.toString()
        )
    }
}