package com.ygxj.prshs.ui.view

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.ygxj.prshs.R
import com.ygxj.prshs.databinding.ViewTitleBarBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.ext.gone
import com.ygxj.prshs.ext.toDp

class TitleBarView(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

    private val binding: ViewTitleBarBinding = ViewTitleBarBinding.inflate(LayoutInflater.from(context), this, true)

    init {

        // padding设置为10
        setPadding(10.toDp(), 10.toDp(), 10.toDp(), 10.toDp())

        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.TitleBarView)
        val title = typedArray.getString(R.styleable.TitleBarView_title)
        typedArray.recycle()

        binding.tvTitle.text = title

        binding.btnBack.click {
            (context as? Activity)?.onBackPressed()
        }
    }

    fun setTitle(title: String?) {
        binding.tvTitle.text = title ?: ""
    }

    fun hideBackBtn() {
        binding.btnBack.gone()
    }
}
