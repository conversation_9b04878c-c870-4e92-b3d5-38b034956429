package com.ygxj.prshs.ui.emergencyProfile

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import com.blankj.utilcode.util.TimeUtils
import com.hjq.toast.Toaster
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.util.KeyboardUtils
import com.lxj.xpopupext.popup.CommonPickerPopup
import com.lxj.xpopupext.popup.TimePickerPopup
import com.ygxj.prshs.BuildConfig
import com.ygxj.prshs.data.repository.EmergencyProfileRepository
import com.ygxj.prshs.data.repository.UserRepository
import com.ygxj.prshs.databinding.ViewEmergencyProfileBaseInfoBinding
import com.ygxj.prshs.ext.SimpleCommonPickerListener
import com.ygxj.prshs.ext.SimpleTimePickerListener
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.util.AgeUtils
import java.util.Calendar
import java.util.Date

/**
 * 急救档案中的基础信息表单
 */
class EmergencyProfileBaseInfoFormView(
    context: Context, attrs: AttributeSet? = null
) : LinearLayout(context, attrs) {

    private val binding = ViewEmergencyProfileBaseInfoBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        orientation = VERTICAL
    }

    fun setUserId(userId: Int) {
        // 设置已有数据
        setExistData(userId)

        // 点击出生日期
        binding.tvBirthDate.click {
            KeyboardUtils.hideSoftInput(it)
            val startDate = Calendar.getInstance().apply { set(1925, 1, 1) }
            val endDate = Calendar.getInstance()
            val popup = TimePickerPopup(context)
                .setDefaultDate(endDate)
                .setDateRange(startDate, endDate)
                .setTimePickerListener(object : SimpleTimePickerListener {

                    override fun onTimeConfirm(date: Date, view: View) {
                        val birthDate = TimeUtils.date2String(date, "yyyy年MM月dd日")
                        binding.tvBirthDate.text = birthDate
                        binding.tvAge.text = AgeUtils.calculateAge(birthDate, "yyyy年MM月dd日").toString()
                    }
                })
            XPopup.Builder(context).isViewMode(true).asCustom(popup).show()
        }

        // 点击性别
        binding.tvSex.click {
            KeyboardUtils.hideSoftInput(it)
            val popup = CommonPickerPopup(context).apply {
                setPickerData(listOf("男", "女"))
                setCommonPickerListener(object : SimpleCommonPickerListener {
                    override fun onItemSelected(index: Int, data: String) {
                        binding.tvSex.text = data
                    }
                })
            }
            XPopup.Builder(context).isViewMode(true).asCustom(popup).show()
        }

        if (BuildConfig.DEBUG) {
            binding.etPhone.setText("0")
            binding.etEmergencyName.setText("1")
            binding.etEmergencyPhone.setText("2")
        }
    }

    private fun setExistData(userId: Int) {
        // ===== 已有的用户信息
        val user = UserRepository.findUserById(userId) ?: return
        // 姓名
        binding.etName.setText(user.name)
        // 性别
        binding.tvSex.text = user.sex
        // 出生日期
        binding.tvBirthDate.text = user.birthDate
        // 年龄
        binding.tvAge.text = AgeUtils.calculateAge(user.birthDate, "yyyy年MM月dd日").toString()

        // ===== 已有的急救档案信息
        val profile = EmergencyProfileRepository.findEmergencyProfileByUserId(userId) ?: return
        // 联系方式
        binding.etPhone.setText(profile.phone)
        // 紧急联系人
        binding.etEmergencyName.setText(profile.emergencyName)
        // 紧急联系人联系方式
        binding.etEmergencyPhone.setText(profile.emergencyPhone)
    }

    fun checkFormIsCompleted(): Boolean {
        // 姓名
        val userName = binding.etName.text.toString()
        if (userName.isBlank()) {
            Toaster.show("请输入姓名")
            return false
        }
        // 性别
        val sex = binding.tvSex.text.toString()
        if (sex.isBlank()) {
            Toaster.show("请选择性别")
            return false
        }

        // 出生日期
        val birthDate = binding.tvBirthDate.text.toString()
        if (birthDate.isBlank()) {
            Toaster.show("请选择出生日期")
            return false
        }

        // 联系方式
        val phone = binding.etPhone.text.toString()
        if (phone.isBlank()) {
            Toaster.show("请输入联系方式")
            return false
        }
        // 紧急联系人
        val emergencyName = binding.etEmergencyName.text.toString()
        if (emergencyName.isBlank()) {
            Toaster.show("请输入紧急联系人")
            return false
        }
        // 紧急联系人联系方式
        val emergencyPhone = binding.etEmergencyPhone.text.toString()
        if (emergencyPhone.isBlank()) {
            Toaster.show("请输入紧急联系人联系方式")
            return false
        }
        return true
    }

    fun getFormData(): Map<String, String> {
        return mapOf(
            "name" to binding.etName.text.toString(),
            "sex" to binding.tvSex.text.toString(),
            "birthDate" to binding.tvBirthDate.text.toString(),
            "phone" to binding.etPhone.text.toString(),
            "emergencyName" to binding.etEmergencyName.text.toString(),
            "emergencyPhone" to binding.etEmergencyPhone.text.toString()
        )
    }
}