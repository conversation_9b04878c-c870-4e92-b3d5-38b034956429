package com.ygxj.prshs.ui.testAdjust.quickEvaluate

import android.content.Context
import android.content.Intent
import androidx.lifecycle.lifecycleScope
import com.drake.brv.annotaion.DividerOrientation
import com.drake.brv.utils.divider
import com.drake.brv.utils.grid
import com.drake.brv.utils.models
import com.drake.brv.utils.setup
import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.databinding.ActivityQuickEvaluateBinding
import com.ygxj.prshs.ui.testAdjust.quickEvaluate.QuickEvaluateDetailActivity
import com.ygxj.scale.ScaleHelper
import com.ygxj.scale.entity.ScaleEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class QuickEvaluateActivity : NoVMBaseActivity<ActivityQuickEvaluateBinding>(
    R.layout.activity_quick_evaluate
) {

    companion object {

        fun start(context: Context) {
            context.startActivity(Intent(context, QuickEvaluateActivity::class.java))
        }
    }

    override fun initData() {

        binding.rv.grid(3).divider {
            setDivider(30, true)
            orientation = DividerOrientation.GRID
            includeVisible = true
        }.setup {
            addType<ScaleEntity>(R.layout.item_scale)
            R.id.llRoot.onClick {
                DialogQuickEvaluateHelp(context, getModel()) {
                    QuickEvaluateDetailActivity.start(context, getModel<ScaleEntity>().scaleId)
                }.build().show()
            }
        }

        // 获取数据
        binding.state.showLoading()
        lifecycleScope.launch(Dispatchers.IO) {
            // 从数据库中读取数据
            val list = ScaleHelper.getScaleEntityListByIds(listOf(690, 691, 692, 693, 694))
            // 切换到主线程设置数据
            withContext(Dispatchers.Main) {
                binding.state.showContent()
                binding.rv.models = list
            }
        }

    }
}