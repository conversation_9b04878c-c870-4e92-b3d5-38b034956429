package com.ygxj.prshs.ui.scale.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.hjq.toast.Toaster
import com.ygxj.prshs.databinding.ViewScaleQuestionButtonBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.ui.scale.ScaleTestModel

class ScaleQuestionButton(context: Context, attrs: AttributeSet?) : LinearLayout(context, attrs) {

    private var binding = ViewScaleQuestionButtonBinding.inflate(LayoutInflater.from(context), this, true)

    fun setModel(model: ScaleTestModel?) {
        model ?: return
        // 上一题
        binding.btnPre.click {
            if (model.questionIndex.value!! <= 0) {
                Toaster.show("已经是第一题了")
                return@click
            }
            model.questionIndex.value = model.questionIndex.value!! - 1
        }

        // 下一题
        binding.btnNext.click {
            val count = model.scale.value!!.scaleQuestions[model.questionIndex.value!!].questionOptions.count { it.selected }
            if (count <= 0) {
                Toaster.show("请选择一个选项")
                return@click
            }
            if (model.questionIndex.value!! >= model.scale.value!!.scaleQuestions.lastIndex) {
                Toaster.show("已经是最后一题了")
                return@click
            }
            model.questionIndex.value = model.questionIndex.value!! + 1
        }

        // 提交
        binding.btnCommit.click {
            model.commitScale(it)
        }
    }
}