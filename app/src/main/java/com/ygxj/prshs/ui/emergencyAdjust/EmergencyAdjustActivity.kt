package com.ygxj.prshs.ui.emergencyAdjust

import android.content.Context
import android.content.Intent
import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.databinding.ActivityEmergencyAdjustBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.util.PermissionsUtils

/**
 * 紧急调节
 */
class EmergencyAdjustActivity : NoVMBaseActivity<ActivityEmergencyAdjustBinding>(
    R.layout.activity_emergency_adjust
) {

    companion object {

        fun start(context: Context) {
            context.startActivity(Intent(context, EmergencyAdjustActivity::class.java))
        }
    }

    override fun initData() {
        // 情绪急救
        binding.btnEmotionEmergencyHelp.click {
            checkBluetoothPermission(EmergencyAdjustType.EMOTION_EMERGENCY_HELP)
        }
        // 压力释放
        binding.btnPressRelease.click {
            checkBluetoothPermission(EmergencyAdjustType.PRESS_RELEASE)
        }
        // 认知重构
        binding.btnKnowledgeRebuild.click {
            checkBluetoothPermission(EmergencyAdjustType.KNOWLEDGE_REBUILD)
        }
    }

    private fun checkBluetoothPermission(type: EmergencyAdjustType) {
        PermissionsUtils.requestBlePermission(this) {
            EmergencyAdjustDetailActivity.start(this, type)
        }
    }
}