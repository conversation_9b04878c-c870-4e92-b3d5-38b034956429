package com.ygxj.prshs.ui.eyeMove.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.ygxj.prshs.databinding.ViewEyeMoveTrainSpeedControlBinding

class EyeMoveTrainSpeedControlView(context: Context, attrs: AttributeSet?) : LinearLayout(context, attrs) {

    private val binding = ViewEyeMoveTrainSpeedControlBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        orientation = VERTICAL
    }
}