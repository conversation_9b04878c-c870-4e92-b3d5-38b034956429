package com.ygxj.prshs.ui.testAdjust.quickEvaluate

import android.content.Context
import androidx.databinding.DataBindingUtil
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.CenterPopupView
import com.ygxj.prshs.R
import com.ygxj.prshs.databinding.DialogQuickEvaluateHelpBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.ext.toPx
import com.ygxj.scale.entity.ScaleEntity

class DialogQuickEvaluateHelp(
    context: Context,
    private val scaleEntity: ScaleEntity,
    private val onConfirmClick: () -> Unit
) : CenterPopupView(context) {

    private lateinit var binding: DialogQuickEvaluateHelpBinding

    override fun getImplLayoutId() = R.layout.dialog_quick_evaluate_help

    override fun onCreate() {
        super.onCreate()
        binding = DataBindingUtil.bind(popupImplView)!!

        binding.tvTitle.text = scaleEntity.scaleName
        binding.tvGuide.text = scaleEntity.scaleHelper
        binding.btnConfirm.click {
            dismissWith { onConfirmClick() }
        }
    }

    override fun getPopupWidth(): Int {
        return 603.toPx()
    }

    override fun getPopupHeight(): Int {
        return 364.toPx()
    }

    fun build(): BasePopupView {
        return XPopup.Builder(context)
            .isViewMode(true)
            .asCustom(this)
    }
}