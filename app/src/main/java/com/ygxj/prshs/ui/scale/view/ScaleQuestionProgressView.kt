package com.ygxj.prshs.ui.scale.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.ygxj.prshs.databinding.ViewScaleQuestionProgressBinding
import com.ygxj.prshs.ui.scale.ScaleTestModel

class ScaleQuestionProgressView(context: Context, attrs: AttributeSet?) : LinearLayout(context, attrs) {

    private val binding = ViewScaleQuestionProgressBinding.inflate(LayoutInflater.from(context), this, true)
    private var model: ScaleTestModel? = null

    init {
        orientation = HORIZONTAL
    }

    fun setModel(model: ScaleTestModel?) {
        this.model = model
    }

    fun setQuestionIndex(index: Int) {
        model?.let {
            binding.tvProgress.text = "测试进度: ${index + 1}/${it.scale.value?.scaleQuestions?.count()}"
        }
    }
}