package com.ygxj.prshs.ui.emergencyProfile

import android.annotation.SuppressLint
import android.content.Intent
import androidx.appcompat.app.AppCompatActivity
import com.hjq.toast.Toaster
import com.lxj.xpopup.util.KeyboardUtils
import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.data.entity.EmergencyProfileEntity
import com.ygxj.prshs.data.repository.EmergencyProfileRepository
import com.ygxj.prshs.data.repository.UserRepository
import com.ygxj.prshs.databinding.ActivityEmergencyProfileBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.ext.gone
import com.ygxj.prshs.ext.visible

class EmergencyProfileActivity : NoVMBaseActivity<ActivityEmergencyProfileBinding>(R.layout.activity_emergency_profile) {

    private val userId by lazy { intent.getIntExtra("userId", 0) }

    companion object {

        fun start(context: AppCompatActivity, userId: Int) {
            val intent = Intent(context, EmergencyProfileActivity::class.java)
            intent.putExtra("userId", userId)
            context.startActivity(intent)
        }
    }

    override fun initData() {

        // 基础信息
        binding.vBaseInfo.setUserId(userId)
        // 危机信息
        binding.vDangerInfo.setUserId(userId)

        // 点击保存
        binding.btnSave.click { save() }

        // 点击取消
        binding.btnCancel.click { finish() }

        // 是新增急救档案,还是编辑急救档案
        val profile = EmergencyProfileRepository.findEmergencyProfileByUserId(userId)
        if (profile == null) {
            binding.titleBar.setTitle("新增急救档案")
            binding.titleBar.hideBackBtn()
            binding.btnCancel.gone()
        } else {
            binding.titleBar.setTitle("编辑急救档案")
            binding.btnCancel.visible()
        }
    }

    /**
     * 保存
     */
    private fun save() {
        KeyboardUtils.hideSoftInput(binding.btnSave)
        // 检查基础信息表单是否完成
        if (!binding.vBaseInfo.checkFormIsCompleted()) return
        // 检查危机信息表单是否完成
        if (!binding.vDangerInfo.checkFormIsCompleted()) return

        // 基础信息表单数据
        val baseInfoData = binding.vBaseInfo.getFormData()
        // 危机信息表单数据
        val dangerInfoData = binding.vDangerInfo.getFormData()


        showLoadingDialog("正在保存")
        // 先保存用户信息
        UserRepository.findUserById(userId)?.let {
            it.name = baseInfoData["name"] ?: ""
            it.sex = baseInfoData["sex"] ?: ""
            it.birthDate = baseInfoData["birthDate"] ?: ""
            UserRepository.updateUser(it)
        }
        // 再保存危机档案
        EmergencyProfileRepository.saveOrUpdate(
            EmergencyProfileEntity(
                userId = userId,
                phone = baseInfoData["phone"] ?: "",
                emergencyName = baseInfoData["emergencyName"] ?: "",
                emergencyPhone = baseInfoData["emergencyPhone"] ?: "",
                dangerDate = dangerInfoData["dangerDate"] ?: "",
                dangerEvents = dangerInfoData["dangerEvents"] ?: "",
                dangerDesc = dangerInfoData["dangerDesc"] ?: ""
            )
        )
        hideLoadingDialog()
        Toaster.show("已保存")
        finish()
    }

    /**
     * 屏蔽返回事件
     */
    @SuppressLint("MissingSuperCall", "GestureBackNavigation")
    override fun onBackPressed() {
        // do nothing
    }

}