package com.ygxj.prshs.ui.scale

import android.content.Context
import android.content.Intent
import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.base.VMBaseActivity
import com.ygxj.prshs.databinding.ActivityScaleTestBinding

class ScaleTestActivity : VMBaseActivity<ActivityScaleTestBinding, ScaleTestModel>(
    R.layout.activity_scale_test, ScaleTestModel()
) {

    private val scaleId by lazy { intent.getIntExtra("scaleId", 0) }

    companion object {

        fun start(context: Context, scaleId: Int) {
            val intent = Intent(context, ScaleTestActivity::class.java)
            intent.putExtra("scaleId", scaleId)
            context.startActivity(intent)
        }
    }

    override fun initData() {
        binding.model = model
        model.getScaleDetail(scaleId)
    }
}