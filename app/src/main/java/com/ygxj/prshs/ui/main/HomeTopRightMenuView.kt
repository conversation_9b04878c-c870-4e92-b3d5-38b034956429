package com.ygxj.prshs.ui.main

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.blankj.utilcode.util.ActivityUtils
import com.hjq.toast.Toaster
import com.lxj.xpopup.XPopup
import com.ygxj.prshs.R
import com.ygxj.prshs.constant.UserRole
import com.ygxj.prshs.data.manager.UserManager
import com.ygxj.prshs.databinding.ViewHomeTopRightMenuBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.ext.show
import com.ygxj.prshs.ui.login.LoginActivity

class HomeTopRightMenuView(context: Context, attrs: AttributeSet?) : LinearLayout(context, attrs) {

    private val binding = ViewHomeTopRightMenuBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        orientation = HORIZONTAL
        initData()
    }

    private fun initData() {
        if (isInEditMode) return
        val user = UserManager.loginUser
        binding.btnUserCenter.show(user?.role != UserRole.GUEST)
        binding.btnSystemSetting.show(user?.role != UserRole.GUEST)


        binding.btnUserCenter.click {
            Toaster.show("用户中心")
        }

        binding.btnSystemSetting.click {
            Toaster.show("系统设置")
        }

        binding.btnLogout.click {
            XPopup.Builder(context)
                .isViewMode(true)
                .asConfirm("提示", "确定要退出登录吗？", "取消", "确定", {
                    LoginActivity.start(context)
                    ActivityUtils.finishAllActivities()
                }, {}, false, R.layout.dialog_custom_comfirm)
                .show()
        }

    }
}