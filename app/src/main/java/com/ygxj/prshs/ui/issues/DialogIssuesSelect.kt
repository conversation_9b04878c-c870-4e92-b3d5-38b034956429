package com.ygxj.prshs.ui.issues

import android.content.Context
import com.drake.brv.annotaion.DividerOrientation
import com.drake.brv.utils.divider
import com.drake.brv.utils.grid
import com.drake.brv.utils.setup
import com.hjq.toast.Toaster
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.impl.FullScreenPopupView
import com.ygxj.prshs.R
import com.ygxj.prshs.constant.IssuesStatus
import com.ygxj.prshs.data.entity.IssueEntity
import com.ygxj.prshs.databinding.DialogIssuesSelectBinding
import com.ygxj.prshs.ext.click

/**
 * 症状状态选择弹窗
 */
class DialogIssuesSelect(
    context: Context,
    private val onSelected: (List<IssueEntity>) -> Unit
) : FullScreenPopupView(context) {

    private lateinit var binding: DialogIssuesSelectBinding
    private val issuesData by lazy { IssuesStatus.getIssues() }

    override fun getImplLayoutId(): Int {
        return R.layout.dialog_issues_select
    }

    override fun onCreate() {
        super.onCreate()
        binding = DialogIssuesSelectBinding.bind(popupImplView)
        configRV()
        binding.btnCancel.click { dismiss() }
        binding.btnGoTrain.click { onConfirm() }
    }

    private fun configRV() {
        binding.rv.grid(3).divider {
            setDivider(10, true)
            orientation = DividerOrientation.GRID
        }.setup {
            addType<IssueEntity>(R.layout.item_issues)
            onChecked { position, isChecked, isAllChecked ->
                val model = getModel<IssueEntity>(position)
                model.checked = isChecked
                model.notifyChange() // 通知UI跟随数据变化
            }
            onClick(R.id.llRoot) {
                val isChecked = getModel<IssueEntity>().checked
                setChecked(absoluteAdapterPosition, !isChecked)
            }
        }.models = issuesData
    }

    private fun onConfirm() {
        // 如果当前一个都没选,提示至少选择一项
        if (issuesData.none { it.checked }) {
            Toaster.show("请至少选择一项")
            return
        }
        onSelected(issuesData.filter { it.checked })
        dismiss()
    }

    fun build(): BasePopupView {
        return XPopup.Builder(context)
            .isViewMode(true)
            .asCustom(this)
    }
}