package com.ygxj.prshs.ui.eyeMove.fragment

import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.base.NoVMBaseFragment
import com.ygxj.prshs.databinding.FragmentEyeMoveTrainStep2Binding

class EyeMoveTrainStep2Fragment : NoVMBaseFragment<FragmentEyeMoveTrainStep2Binding>(
    R.layout.fragment_eye_move_train_step2
) {

    companion object {

        fun getInstance() = EyeMoveTrainStep2Fragment()
    }

    override fun initData() {

    }

}
