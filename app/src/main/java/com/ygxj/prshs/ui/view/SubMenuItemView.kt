package com.ygxj.prshs.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.ygxj.prshs.R
import com.ygxj.prshs.databinding.ViewSubMenuItemBinding
import com.ygxj.prshs.ext.toDp

class SubMenuItemView(context: Context, attrs: AttributeSet? = null) : LinearLayout(context, attrs) {

    private val binding = ViewSubMenuItemBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        orientation = VERTICAL
        gravity = Gravity.CENTER_HORIZONTAL
        setBackgroundResource(R.mipmap.bg_sub_menu_out)

        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.SubMenuItemView)
        val iconResId = typedArray.getResourceId(R.styleable.SubMenuItemView_sub_menu_icon, 0)
        val title = typedArray.getString(R.styleable.SubMenuItemView_sub_menu_title)
        val padding = typedArray.getDimensionPixelSize(R.styleable.SubMenuItemView_siv_padding, 0)
        typedArray.recycle()

        if (iconResId != 0) {
            binding.icon.setImageResource(iconResId)
        }
        if (padding > 0) {
            binding.icon.setPadding(padding, padding, padding, padding)
        }
        binding.tvTitle.text = title
    }

    fun getMenuTitle(): String {
        return binding.tvTitle.text.toString()
    }
}
