@file:Suppress("DEPRECATION")

package com.ygxj.prshs.ui.trainPlan

import android.content.Context
import android.media.AudioManager
import android.widget.SeekBar
import com.google.android.exoplayer2.ExoPlayer
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.CenterPopupView
import com.ygxj.prshs.R
import com.ygxj.prshs.databinding.DialogTrainVolumeBinding // 请确保这是您正确的Binding类名
import com.ygxj.prshs.ext.SimpleSeekBarChangeListener
import com.ygxj.prshs.ext.click

/**
 * 调节音量弹窗
 */
class DialogTrainVolume(
    context: Context,
    private val audioPlayer: ExoPlayer,
    private val videoPlayer: ExoPlayer,
) : CenterPopupView(context) {

    private lateinit var binding: DialogTrainVolumeBinding

    // 系统音量管理器,用于获取和设置系统音量
    private lateinit var audioManager: AudioManager

    override fun getImplLayoutId() = R.layout.dialog_train_volume

    override fun onCreate() {
        super.onCreate()
        binding = DialogTrainVolumeBinding.bind(popupImplView)
        binding.btnBack.click { dismiss() }

        audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

        setupSystemVolumeSeekBar()
        setupAudioPlayerVolumeSeekBar()
        setupVideoPlayerVolumeSeekBar()
    }

    /**
     * 设置系统音量SeekBar
     */
    private fun setupSystemVolumeSeekBar() {
        val seekBar = binding.seekBarSystemVolume
        val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
        val currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC)

        seekBar.max = maxVolume
        seekBar.progress = currentVolume

        seekBar.setOnSeekBarChangeListener(object : SimpleSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, progress, 0)
                }
            }
        })
    }

    /**
     * 设置音频播放器音量SeekBar
     */
    private fun setupAudioPlayerVolumeSeekBar() {
        val seekBar = binding.seekBarAudioPlayer
        seekBar.max = 100
        seekBar.progress = (audioPlayer.volume * 100).toInt()

        seekBar.setOnSeekBarChangeListener(object : SimpleSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    audioPlayer.volume = progress / 100f
                }
            }
        })
    }

    /**
     * 设置视频播放器音量SeekBar
     */
    private fun setupVideoPlayerVolumeSeekBar() {
        val seekBar = binding.seekBarVideoPlayer
        seekBar.max = 100
        seekBar.progress = (videoPlayer.volume * 100).toInt()

        seekBar.setOnSeekBarChangeListener(object : SimpleSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    videoPlayer.volume = progress / 100f
                }
            }
        })
    }

    /**
     * 保留您原有的build方法
     */
    fun build(): BasePopupView {
        return XPopup.Builder(context)
            .isViewMode(true)
            .asCustom(this)
    }
}