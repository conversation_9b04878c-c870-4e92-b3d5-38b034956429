package com.ygxj.prshs.ui.eyeMove.view

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.drake.brv.annotaion.DividerOrientation
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.divider
import com.drake.brv.utils.grid
import com.drake.brv.utils.setup
import com.ygxj.prshs.R
import com.ygxj.prshs.data.entity.EyeMoveTrainColorEntity
import com.ygxj.prshs.databinding.ItemEyeMoveTrainColorControlBinding
import com.ygxj.prshs.databinding.ViewEyeMoveTrainColorControlBinding
import com.ygxj.prshs.ui.eyeMove.EyeMoveTrainModel

class EyeMoveTrainColorControlView(context: Context, attrs: AttributeSet?) : LinearLayout(context, attrs) {

    private val binding = ViewEyeMoveTrainColorControlBinding.inflate(LayoutInflater.from(context), this, true)
    private var model: EyeMoveTrainModel? = null

    init {
        orientation = VERTICAL
    }

    fun setModel(model: EyeMoveTrainModel?) {
        model ?: return
        this.model = model
        binding.rv.grid(6).divider {
            setDivider(5, true)
            orientation = DividerOrientation.GRID
        }.setup {
            addType<EyeMoveTrainColorEntity>(R.layout.item_eye_move_train_color_control)
            onBind {
                val item = getModel<EyeMoveTrainColorEntity>()
                getBinding<ItemEyeMoveTrainColorControlBinding>().vColor.shapeDrawableBuilder
                    .setSolidColor(item.color)
                    .setStrokeColor(Color.WHITE)
                    .setStrokeSize(if (item.checked) 5 else 0)
                    .intoBackground()
            }
            singleMode = true
            onChecked { position, checked, allChecked ->
                val item = getModel<EyeMoveTrainColorEntity>(position)
                item.checked = checked
                notifyItemChanged(position)
                model.currentColor.value = item
            }
            R.id.llRoot.onClick {
                binding.rv.bindingAdapter.setChecked(absoluteAdapterPosition, true)
            }
        }.models = model.colors

        binding.rv.bindingAdapter.setChecked(0, true)
    }
}