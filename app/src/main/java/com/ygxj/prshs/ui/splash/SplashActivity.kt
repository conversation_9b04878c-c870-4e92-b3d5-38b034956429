package com.ygxj.prshs.ui.splash

import com.ygxj.active.v2.ActivateUtilV2
import com.ygxj.prshs.BuildConfig
import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.data.repository.UserRepository
import com.ygxj.prshs.databinding.ActivitySplashBinding
import com.ygxj.prshs.ui.login.LoginActivity

class SplashActivity : NoVMBaseActivity<ActivitySplashBinding>(R.layout.activity_splash) {

    override fun initData() {

        if (BuildConfig.DEBUG) {
            doAfterActivate()
        } else {
            ActivateUtilV2.getInstance(this).doActivate {
                doAfterActivate()
            }
        }
    }

    private fun doAfterActivate() {

        // 检查是否有管理员账号
        UserRepository.checkDefaultAdminUser()
        // 检查是否有游客账号
        UserRepository.checkDefaultGuestUser()

        LoginActivity.start(this)
        finish()
    }

}