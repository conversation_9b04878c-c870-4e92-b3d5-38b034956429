package com.ygxj.prshs.ui.eyeMove

import android.content.Context
import android.content.Intent
import com.ygxj.prshs.R
import com.ygxj.prshs.base.VMBaseActivity
import com.ygxj.prshs.databinding.ActivityEyeMoveTrainBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.ui.eyeMove.fragment.EyeMoveTrainStep1Fragment
import com.ygxj.prshs.ui.eyeMove.fragment.EyeMoveTrainStep2Fragment
import com.ygxj.prshs.ui.eyeMove.fragment.EyeMoveTrainStep3Fragment
import com.ygxj.prshs.ui.eyeMove.fragment.EyeMoveTrainStep4Fragment
import com.ygxj.prshs.ui.eyeMove.fragment.EyeMoveTrainStep5Fragment
import com.ygxj.prshs.ui.eyeMove.fragment.EyeMoveTrainStep6Fragment
import com.ygxj.prshs.ui.eyeMove.fragment.EyeMoveTrainStep7Fragment
import com.ygxj.prshs.ui.eyeMove.fragment.EyeMoveTrainStep8Fragment

/**
 * 眼动训练
 */
class EyeMoveTrainActivity : VMBaseActivity<ActivityEyeMoveTrainBinding, EyeMoveTrainModel>(
    R.layout.activity_eye_move_train, EyeMoveTrainModel()
) {

    private val selectedIssues by lazy { intent.getStringExtra("selectedIssues") ?: "" }

    companion object {

        fun start(context: Context, selectedIssues: String) {
            val intent = Intent(context, EyeMoveTrainActivity::class.java)
            intent.putExtra("selectedIssues", selectedIssues)
            context.startActivity(intent)
        }
    }

    override fun initData() {
        binding.model = model

        setObserver()

        binding.btnNext.click {
            model.currentStep.value = model.currentStep.value!! + 1
        }
    }

    private fun setObserver() {
        model.currentStep.observe(this) {
            val ft = supportFragmentManager.beginTransaction()
            when (it) {
                0 -> ft.replace(R.id.flContainer, EyeMoveTrainStep1Fragment.getInstance())
                1 -> ft.replace(R.id.flContainer, EyeMoveTrainStep2Fragment.getInstance())
                2 -> ft.replace(R.id.flContainer, EyeMoveTrainStep3Fragment.getInstance())
                3 -> ft.replace(R.id.flContainer, EyeMoveTrainStep4Fragment.getInstance())
                4 -> ft.replace(R.id.flContainer, EyeMoveTrainStep5Fragment.getInstance())
                5 -> ft.replace(R.id.flContainer, EyeMoveTrainStep6Fragment.getInstance())
                6 -> ft.replace(R.id.flContainer, EyeMoveTrainStep7Fragment.getInstance())
                7 -> ft.replace(R.id.flContainer, EyeMoveTrainStep8Fragment.getInstance())
            }
            ft.commit()
        }
    }
}