package com.ygxj.prshs.ui.scale.view

import androidx.databinding.DataBindingUtil
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.ygxj.prshs.R
import com.ygxj.prshs.databinding.ItemScaleOptionBinding
import com.ygxj.scale.entity.ScaleEntity

class ScaleQuestionOptionAdapter : BaseQuickAdapter<ScaleEntity.ScaleQuestion.QuestionOptions, BaseViewHolder>(
    R.layout.item_scale_option
) {

    override fun onItemViewHolderCreated(viewHolder: BaseViewHolder, viewType: Int) {
        DataBindingUtil.bind<ItemScaleOptionBinding>(viewHolder.itemView)
    }

    override fun convert(helper: BaseViewHolder, item: ScaleEntity.ScaleQuestion.QuestionOptions) {
        helper.getBinding<ItemScaleOptionBinding>()?.let { it ->
            it.value = item.optionValue
            it.checked = item.selected
        }
    }
}