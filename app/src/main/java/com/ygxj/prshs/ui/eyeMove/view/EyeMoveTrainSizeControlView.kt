package com.ygxj.prshs.ui.eyeMove.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.ygxj.prshs.databinding.ViewEyeMoveTrainSizeControlBinding

class EyeMoveTrainSizeControlView(context: Context, attrs: AttributeSet?) : LinearLayout(context, attrs) {

    private val binding = ViewEyeMoveTrainSizeControlBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        orientation = VERTICAL
    }
}