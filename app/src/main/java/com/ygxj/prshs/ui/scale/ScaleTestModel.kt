package com.ygxj.prshs.ui.scale

import android.view.View
import androidx.lifecycle.MutableLiveData
import com.hjq.toast.Toaster
import com.ygxj.prshs.base.BaseViewModel
import com.ygxj.prshs.data.manager.UserManager
import com.ygxj.prshs.util.AgeUtils
import com.ygxj.scale.ScaleHelper
import com.ygxj.scale.ScaleResultHelper
import com.ygxj.scale.entity.ScaleEntity
import com.ygxj.scale.util.DateUtils

class ScaleTestModel : BaseViewModel() {

    val scale = MutableLiveData<ScaleEntity>()
    val questionIndex = MutableLiveData(0)
    var startTimestamp = 0L
    private var endTimestamp = 0L

    fun getScaleDetail(scaleId: Int) {
        if (scaleId == -1) return
        scale.value = ScaleHelper.getScaleEntityById(scaleId)
    }

    /**
     * 提交测试结果
     */
    fun commitScale(view: View) {
        // 获取所有的选项
        val list = arrayListOf<Int>()
        scale.value!!.scaleQuestions.forEach { i ->
            val index = i.questionOptions.indexOfFirst { it.selected }
            list.add(index)
        }
        if (list.count { it == -1 } > 0) {
            Toaster.show("请在完成所有题目后提交")
            return
        }
        // 结束计时
        endTimestamp = System.currentTimeMillis()

        // ====== 组织提交参数
        // 量表id
        val scaleId = scale.value!!.scaleId
        // 选项
        val selectedOptions = mutableMapOf<Int, String>()
        for (index in list.indices) {
            selectedOptions[index] = list[index].toString()
        }
        // 用时
        val costTime = DateUtils.getHourMinSecFromSec((endTimestamp - startTimestamp) / 1000)
        val userAge = UserManager.loginUser?.birthDate?.let { AgeUtils.calculateAge(it, "yyyy年MM月dd日") } ?: 0
        val userSex = UserManager.loginUser?.sex ?: "---"
        val result = ScaleResultHelper.getResult(
            scaleId = scaleId,
            selectedOptions = selectedOptions,
            costTime = costTime,
            userAge = userAge,
            userSex = userSex
        )
        if (result == null) {
            Toaster.show("该量表暂不支持")
            return
        }
        DialogScaleResult(view.context, scaleId, result, {
            finishActivity()
        }, {
            finishActivity()
        }).build().show()
    }
}