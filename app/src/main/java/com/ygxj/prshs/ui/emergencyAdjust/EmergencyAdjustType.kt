package com.ygxj.prshs.ui.emergencyAdjust

import com.ygxj.prshs.R
import com.ygxj.prshs.base.App

enum class EmergencyAdjustType(
    val title: String,
    val menu1: String,
    val menu2: String,
    val menu3: String,
    val resUrl1: String,
    val resUrl2: String,
    val resUrl3: String
) {

    // 情绪急救
    EMOTION_EMERGENCY_HELP(
        "情绪急救",
        "识别情绪风暴",
        "即时平复技巧",
        "安全空间构建",
        "android.resource://${App.context.packageName}/${R.raw.hu_xi_fang_song_fa}",
        "android.resource://${App.context.packageName}/${R.raw.jian_jin_ji_rou_fang_song_fa}",
        "android.resource://${App.context.packageName}/${R.raw.xiang_xiang_fang_song_fa}"

    ),

    // 压力释放
    PRESS_RELEASE(
        "压力释放",
        "压力信号识别",
        "渐进式肌肉放松",
        "呼吸引导深度放松",
        "android.resource://${App.context.packageName}/${R.raw.jian_jin_ji_rou_fang_song_fa}",
        "android.resource://${App.context.packageName}/${R.raw.xiang_xiang_fang_song_fa}",
        "android.resource://${App.context.packageName}/${R.raw.hu_xi_fang_song_fa}"
    ),

    // 认知重构
    KNOWLEDGE_REBUILD(
        "认知重构",
        "识别思维陷阱",
        "思维叫停与转移",
        "简单证据检验",
        "android.resource://${App.context.packageName}/${R.raw.xiang_xiang_fang_song_fa}",
        "android.resource://${App.context.packageName}/${R.raw.hu_xi_fang_song_fa}",
        "android.resource://${App.context.packageName}/${R.raw.jian_jin_ji_rou_fang_song_fa}"
    )
}