package com.ygxj.prshs.ui.eyeMove

import androidx.core.graphics.toColorInt
import androidx.lifecycle.MutableLiveData
import com.ygxj.prshs.base.BaseViewModel
import com.ygxj.prshs.data.entity.EyeMoveTrainColorEntity

class EyeMoveTrainModel : BaseViewModel() {

    val currentStep = MutableLiveData(0)
    val colors = listOf(
        EyeMoveTrainColorEntity("#FFDE02".toColorInt()),
        EyeMoveTrainColorEntity("#FD788B".toColorInt()),
        EyeMoveTrainColorEntity("#03FFFF".toColorInt()),
        EyeMoveTrainColorEntity("#0000FF".toColorInt()),
        EyeMoveTrainColorEntity("#72F500".toColorInt()),
        EyeMoveTrainColorEntity("#ED0100".toColorInt())
    )
    val currentColor = MutableLiveData(colors[0])
}