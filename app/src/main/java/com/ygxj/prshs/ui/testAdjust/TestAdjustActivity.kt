package com.ygxj.prshs.ui.testAdjust

import android.content.Context
import android.content.Intent
import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.databinding.ActivityTestAdjustBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.ui.scale.ScaleTestActivity
import com.ygxj.prshs.ui.testAdjust.quickEvaluate.QuickEvaluateActivity

/**
 * 测试后调节
 */
class TestAdjustActivity : NoVMBaseActivity<ActivityTestAdjustBinding>(
    R.layout.activity_test_adjust
) {

    companion object {

        fun start(context: Context) {
            context.startActivity(Intent(context, TestAdjustActivity::class.java))
        }
    }

    override fun initData() {

        // 快速评估,五个特殊的量表,布局也与常规量表不一样
        binding.btnQuickEvaluate.click { QuickEvaluateActivity.start(this) }
        // 系统评估,直接使用scl-90量表进行测试
        binding.btnSystemEvaluate.click { ScaleTestActivity.start(this, 6) }

    }
}