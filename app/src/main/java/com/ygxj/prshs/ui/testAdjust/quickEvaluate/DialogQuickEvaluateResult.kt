package com.ygxj.prshs.ui.testAdjust.quickEvaluate

import android.content.Context
import androidx.databinding.DataBindingUtil
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.CenterPopupView
import com.ygxj.prshs.R
import com.ygxj.prshs.databinding.DialogQuickEvaluateResultBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.ext.toPx
import com.ygxj.scale.entity.ScaleResultEntity

/**
 * 快速评估结果
 */
class DialogQuickEvaluateResult(
    context: Context,
    private val scaleName: String,
    private val result: ScaleResultEntity,
    private val onConfirmClick: () -> Unit,
    private val onCancelClick: () -> Unit,
) : CenterPopupView(context) {

    private lateinit var binding: DialogQuickEvaluateResultBinding

    override fun getImplLayoutId() = R.layout.dialog_quick_evaluate_result

    override fun onCreate() {
        super.onCreate()
        binding = DataBindingUtil.bind(popupImplView)!!

        val result = "        基于《${scaleName}》的结果评估显示，您可能正处于${result.YJ}，系统已为您自动匹配训练方案，可前往【训练方案】模块进行训练。"
        binding.tvResult.text = result

        binding.btnGoTrain.click { dismissWith { onConfirmClick() } }
        binding.btnLater.click { dismissWith { onCancelClick() } }
    }

    override fun getPopupWidth(): Int {
        return 603.toPx()
    }

    override fun getPopupHeight(): Int {
        return 364.toPx()
    }

    fun build(): BasePopupView {
        return XPopup.Builder(context)
            .isViewMode(true)
            .asCustom(this)
    }
}