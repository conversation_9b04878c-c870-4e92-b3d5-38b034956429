package com.ygxj.prshs.ui.testAdjust.quickEvaluate

import android.content.Context
import android.content.Intent
import com.drake.brv.utils.divider
import com.drake.brv.utils.grid
import com.drake.brv.utils.linear
import com.drake.brv.utils.models
import com.drake.brv.utils.setup
import com.drake.net.utils.TipUtils
import com.hjq.toast.Toaster
import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.data.manager.UserManager
import com.ygxj.prshs.databinding.ActivityQuickEvaluateDetailBinding
import com.ygxj.prshs.databinding.ItemScaleQuestionBinding
import com.ygxj.prshs.databinding.ItemScaleQuestionOptionBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.util.AgeUtils
import com.ygxj.scale.ScaleHelper
import com.ygxj.scale.ScaleResultHelper
import com.ygxj.scale.entity.ScaleEntity
import com.ygxj.scale.util.DateUtils

/**
 * 快速评估量表详情
 */
class QuickEvaluateDetailActivity : NoVMBaseActivity<ActivityQuickEvaluateDetailBinding>(
    R.layout.activity_quick_evaluate_detail
) {

    private val scaleId by lazy { intent.getIntExtra("scaleId", 0) }
    private var startTimeStamp = System.currentTimeMillis()

    companion object {

        fun start(context: Context, scaleId: Int) {
            val intent = Intent(context, QuickEvaluateDetailActivity::class.java)
            intent.putExtra("scaleId", scaleId)
            context.startActivity(intent)
        }
    }

    override fun initData() {
        val scale = ScaleHelper.getScaleEntityById(scaleId)

        // 设置量表标题
        binding.vTitleBar.setTitle(scale.scaleName)

        // 设置量表题目
        setRVData(scale)

        // 提交量表
        binding.btnConfirm.click {
            // 检查是否所有的题目都做了选择
            if (scale.scaleQuestions.any { it.questionOptions.none { option -> option.selected } }) {
                TipUtils.toast("请完成所有题目")
                return@click
            }
            val selectedOptions = mutableMapOf<Int, String>()
            scale.scaleQuestions.forEachIndexed { index, question ->
                selectedOptions[index] = question.questionOptions.indexOfFirst { it.selected }.toString()
            }
            val endTimeStamp = System.currentTimeMillis()
            val costSeconds = (endTimeStamp - startTimeStamp) / 1000
            val costTime = DateUtils.getHourMinSecFromSec(costSeconds)
            val userAge = UserManager.loginUser?.birthDate?.let { AgeUtils.calculateAge(it, "yyyy年MM月dd日") } ?: 0
            val userSex = UserManager.loginUser?.sex ?: "---"
            val result = ScaleResultHelper.getResult(scaleId, selectedOptions, costTime, userAge, userSex)
            if (result == null) {
                Toaster.show("该量表暂不支持，敬请期待~")
                return@click
            }

            // 快速评估量表的结果
            DialogQuickEvaluateResult(this, scale.scaleName, result, {
                Toaster.show("立即训练")
                finish()
            }, { finish() }).build().show()

        }
    }

    /**
     * 设置量表题目数据
     */
    private fun setRVData(scale: ScaleEntity) {
        // 外层rv用来显示题目,内层rv用来显示选项
        binding.rv.linear().divider {
            setDivider(20, true)
        }.setup {
            addType<ScaleEntity.ScaleQuestion>(R.layout.item_scale_question)
            onCreate {
                // 配置内层rv
                getBinding<ItemScaleQuestionBinding>().rvOption.grid(5).divider {
                    setDivider(10, true)
                    startVisible = true
                }.setup {
                    addType<ScaleEntity.ScaleQuestion.QuestionOptions>(R.layout.item_scale_question_option)
                    // BRV的单选功能复用时有问题，暂时不要使用，手动来实现选择
                    R.id.llRoot.onClick {
                        val selectedIndex = models?.indexOfFirst { it is ScaleEntity.ScaleQuestion.QuestionOptions && it.selected } ?: -1
                        if (selectedIndex != -1) {
                            getModel<ScaleEntity.ScaleQuestion.QuestionOptions>(selectedIndex).selected = false
                            notifyItemChanged(selectedIndex)
                        }
                        getModel<ScaleEntity.ScaleQuestion.QuestionOptions>().selected = true
                        notifyItemChanged(absoluteAdapterPosition)
                    }
                    onBind {
                        val itemBind = getBinding<ItemScaleQuestionOptionBinding>()
                        val model = getModel<ScaleEntity.ScaleQuestion.QuestionOptions>()
                        itemBind.tvOption.text = model.optionValue
                        itemBind.ivSelected.setImageResource(
                            if (model.selected) R.mipmap.ic_scale_option_checked else R.mipmap.ic_scale_option_unchecked
                        )
                    }
                }
            }
            onBind {
                // 设置内层rv的数据
                getBinding<ItemScaleQuestionBinding>().rvOption.models = getModel<ScaleEntity.ScaleQuestion>().questionOptions
            }
        }.models = scale.scaleQuestions
    }
}