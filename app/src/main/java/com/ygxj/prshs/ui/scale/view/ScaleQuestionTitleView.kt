package com.ygxj.prshs.ui.scale.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.ygxj.prshs.databinding.ViewScaleQuestionTitleBinding
import com.ygxj.prshs.ext.gone
import com.ygxj.prshs.ext.visible
import com.ygxj.prshs.ui.scale.ScaleTestModel

class ScaleQuestionTitleView(context: Context, attrs: AttributeSet) : FrameLayout(context, attrs) {

    private val binding = ViewScaleQuestionTitleBinding.inflate(LayoutInflater.from(context), this, true)
    private var model: ScaleTestModel? = null

    fun setModel(model: ScaleTestModel?) {
        this.model = model
    }

    fun setQuestionIndex(index: Int) {
        model?.let {
            val title = it.scale.value?.scaleQuestions?.getOrNull(index)?.questionTitle
            if (it.scale.value!!.scaleId == 50) {
                // 瑞文量表,显示图片
                binding.ivTitle.visible()
                Glide.with(context).asBitmap()
                    .load("file:///android_asset/quiz" + title!!.substring(2))
                    .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                    .into(binding.ivTitle)
            } else {
                // 非瑞文量表,显示文本
                binding.tvTitle.visible()
                binding.tvTitle.text = title?.replace("|", "\n")?.replace("<br/>", "\n")?.replace("<br>", "\n")

                binding.ivTitle.gone()
            }

        }
    }
}