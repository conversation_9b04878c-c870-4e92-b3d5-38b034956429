package com.ygxj.prshs.ui.register

import android.content.Context
import android.content.Intent
import android.view.View
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.TimeUtils
import com.hjq.toast.Toaster
import com.lxj.xpopup.XPopup
import com.lxj.xpopupext.listener.CommonPickerListener
import com.lxj.xpopupext.listener.TimePickerListener
import com.lxj.xpopupext.popup.CommonPickerPopup
import com.lxj.xpopupext.popup.TimePickerPopup
import com.ygxj.prshs.BuildConfig
import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.constant.UserRole
import com.ygxj.prshs.data.entity.UserEntity
import com.ygxj.prshs.data.manager.UserManager
import com.ygxj.prshs.data.repository.UserRepository
import com.ygxj.prshs.databinding.ActivityRegisterBinding
import com.ygxj.prshs.ext.click
import com.ygxj.prshs.ui.main.MainActivity
import java.util.Calendar
import java.util.Date

class RegisterActivity : NoVMBaseActivity<ActivityRegisterBinding>(R.layout.activity_register) {

    companion object {

        fun start(context: Context) {
            context.startActivity(Intent(context, RegisterActivity::class.java))
        }
    }

    override fun initData() {

        binding.activity = this
        // 出生日期
        binding.tvBirthDate.click {
            hideSoftInput()
            showBirthDateDialog()
        }
        // 性别
        binding.tvSex.click {
            hideSoftInput()
            showSexDialog()
        }
        // 注册按钮
        binding.btnRegister.click {
            hideSoftInput()
            register()
        }

        if (BuildConfig.DEBUG) {
            binding.etAccount.setText("ttt")
            binding.etPwd.setText("123456")
            binding.etName.setText("1111")
            binding.tvBirthDate.text = "2000年01月01日"
            binding.tvSex.text = "男"
        }
    }

    /**
     * 性别选择弹窗
     */
    private fun showSexDialog() {
        val popup = CommonPickerPopup(this).apply {
            setPickerData(listOf("男", "女"))
            setCommonPickerListener(object : CommonPickerListener {
                override fun onItemSelected(index: Int, data: String) {
                    binding.tvSex.text = data
                }

                override fun onCancel() {}
            })
        }
        XPopup.Builder(this).isViewMode(true).asCustom(popup).show()
    }

    /**
     * 出生日期选择弹窗
     */
    private fun showBirthDateDialog() {
        val startDate = Calendar.getInstance().apply { set(1925, 1, 1) }
        val endDate = Calendar.getInstance()
        val popup = TimePickerPopup(this)
            .setDefaultDate(endDate)
            .setDateRange(startDate, endDate)
            .setTimePickerListener(object : TimePickerListener {
                override fun onTimeChanged(date: Date?) {}

                override fun onTimeConfirm(date: Date, view: View) {
                    binding.tvBirthDate.text = TimeUtils.date2String(date, "yyyy年MM月dd日")
                }

                override fun onCancel() {}
            })
        XPopup.Builder(this).isViewMode(true).asCustom(popup).show()
    }

    /**
     * 注册
     */
    private fun register() {

        // 是否输入了账号
        val account = binding.etAccount.text.toString()
        if (account.isBlank()) {
            Toaster.show("请输入账号")
            return
        }

        // 是否输入了密码或小于6位
        val pwd = binding.etPwd.text.toString()
        if (pwd.isBlank()) {
            Toaster.show("请输入密码")
            return
        }
        if (pwd.length < 6) {
            Toaster.show("为了您的账号安全，密码长度不能小于6位")
            return
        }

        // 是否输入了姓名
        val name = binding.etName.text.toString()
        if (name.isBlank()) {
            Toaster.show("请输入姓名")
            return
        }
        // 是否输入了出生日期
        val birthDate = binding.tvBirthDate.text.toString()
        if (birthDate.isBlank()) {
            Toaster.show("请选择出生日期")
            return
        }
        // 是否输入了性别
        val sex = binding.tvSex.text.toString()
        if (sex.isBlank()) {
            Toaster.show("请选择性别")
            return
        }

        // account是否已存在
        if (UserRepository.findUserByAccount(account) != null) {
            Toaster.show("该账号已存在，您可以直接使用此账号登录，或注册其他账号。")
            return
        }

        // 创建用户
        val newUser = UserEntity(
            account = account,
            role = UserRole.USER,
            password = pwd,
            name = name,
            birthDate = birthDate,
            sex = sex
        )
        newUser.save()
        XPopup.Builder(this)
            .isViewMode(true)
            .asConfirm(
                "注册成功", "是否使用此账号直接登录？", "不登录", "登录", {
                    UserManager.loginUser = newUser
                    MainActivity.start(this)
                    ActivityUtils.finishAllActivities()
                }, {
                    finish()
                }, false, R.layout.dialog_custom_comfirm
            ).show()
    }
}