package com.ygxj.prshs.ui.eyeMove.fragment

import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.base.NoVMBaseFragment
import com.ygxj.prshs.databinding.FragmentEyeMoveTrainStep8Binding

class EyeMoveTrainStep8Fragment : NoVMBaseFragment<FragmentEyeMoveTrainStep8Binding>(
    R.layout.fragment_eye_move_train_step8
) {

    companion object {

        fun getInstance() = EyeMoveTrainStep8Fragment()
    }

    override fun initData() {

    }

}
