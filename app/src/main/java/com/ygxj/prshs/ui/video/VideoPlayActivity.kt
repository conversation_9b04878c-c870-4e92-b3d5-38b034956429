@file:Suppress("DEPRECATION")

package com.ygxj.prshs.ui.video

import android.content.Context
import android.content.Intent
import android.widget.ImageButton
import android.widget.TextView
import androidx.core.net.toUri
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.ygxj.prshs.R
import com.ygxj.prshs.base.NoVMBaseActivity
import com.ygxj.prshs.databinding.ActivityVideoPlayBinding
import com.ygxj.prshs.ext.click

class VideoPlayActivity : NoVMBaseActivity<ActivityVideoPlayBinding>(R.layout.activity_video_play) {

    private var exoPlayer: ExoPlayer? = null
    private val videoUrl by lazy { intent.getStringExtra("videoUrl") ?: "" }
    private val title by lazy { intent.getStringExtra("title") ?: "" }

    companion object {

        fun start(context: Context, videoUrl: String, title: String) {
            val intent = Intent(context, VideoPlayActivity::class.java)
            intent.putExtra("videoUrl", videoUrl)
            intent.putExtra("title", title)
            context.startActivity(intent)
        }
    }

    override fun initData() {

        initExoPlayer()
    }

    /**
     * 初始化 ExoPlayer 播放器
     */
    private fun initExoPlayer() {
        exoPlayer = ExoPlayer.Builder(this).build()
        binding.playerView.player = exoPlayer

        // 先查找本地有没有
        val mediaItem = MediaItem.fromUri(videoUrl.toUri())
        exoPlayer?.setMediaItem(mediaItem)
        exoPlayer?.prepare()
        exoPlayer?.playWhenReady = true // 自动播放

        // 在自定义播放控制视图中的关闭按钮的点击事件
        binding.playerView.findViewById<ImageButton>(R.id.btnBack).click {
            releaseExoPlayer()
            finish()
        }
        // 在自定义播放控制视图中的标题
        binding.playerView.findViewById<TextView>(R.id.tvTitle).text = title
    }

    override fun onDestroy() {
        super.onDestroy()
        releaseExoPlayer()
    }

    /**
     * 释放 ExoPlayer 播放器资源
     */
    private fun releaseExoPlayer() {
        exoPlayer?.release()
        exoPlayer = null
    }
}