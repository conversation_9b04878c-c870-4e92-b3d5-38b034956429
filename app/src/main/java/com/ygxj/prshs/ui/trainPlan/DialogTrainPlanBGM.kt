package com.ygxj.prshs.ui.trainPlan

import android.content.Context
import com.drake.brv.annotaion.DividerOrientation
import com.drake.brv.utils.divider
import com.drake.brv.utils.grid
import com.drake.brv.utils.setup
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.CenterPopupView
import com.ygxj.prshs.R
import com.ygxj.prshs.constant.TrainPlanUtil
import com.ygxj.prshs.data.entity.BgmEntity
import com.ygxj.prshs.databinding.DialogTrainPlanBgmBinding
import com.ygxj.prshs.ext.click

class DialogTrainPlanBGM(
    context: Context,
    private val currentPlayBgm: BgmEntity?,
    private val onSelected: (BgmEntity) -> Unit
) : CenterPopupView(context) {

    private lateinit var binding: DialogTrainPlanBgmBinding

    override fun getImplLayoutId() = R.layout.dialog_train_plan_bgm

    override fun onCreate() {
        super.onCreate()
        binding = DialogTrainPlanBgmBinding.bind(popupImplView)
        binding.btnBack.click { dismiss() }

        val bgmList = TrainPlanUtil.getTrainPlanBgmList()
        if (currentPlayBgm != null) {
            bgmList.forEach { it -> it.checked = it.id == currentPlayBgm.id }
        }
        binding.rv.grid(3).divider {
            setDivider(10, true)
            orientation = DividerOrientation.GRID
        }.setup {
            addType<BgmEntity>(R.layout.item_train_plan_bgm)
            R.id.tvBgmName.onClick {
                val selectedBgm = getModel<BgmEntity>()
                onSelected(selectedBgm)
                dismiss()
            }
        }.models = bgmList
    }

    fun build(): BasePopupView {
        return XPopup.Builder(context)
            .isViewMode(true)
            .asCustom(this)
    }
}