package com.ygxj.prshs.ext

import android.view.View
import androidx.databinding.BindingAdapter

@BindingAdapter("show")
fun View.show(show: <PERSON><PERSON><PERSON>) {
    visibility = if (show) View.VISIBLE else View.GONE
}

fun View.gone() {
    if (visibility != View.GONE) {
        visibility = View.GONE
    }
}

fun View.visible() {
    if (visibility != View.VISIBLE) {
        visibility = View.VISIBLE
    }
}

fun View.invisible() {
    if (visibility != View.INVISIBLE) {
        visibility = View.INVISIBLE
    }
}