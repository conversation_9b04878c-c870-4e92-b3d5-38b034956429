package com.ygxj.prshs.ext

import android.widget.ImageView
import androidx.databinding.BindingAdapter
import com.bumptech.glide.Glide

@BindingAdapter("loadImageUrl")
fun ImageView.loadImageUrl(url: String?) {
    Glide.with(context).load(url).into(this)
}

@BindingAdapter("loadImageRes")
fun ImageView.loadImageRes(resId: Int) {
    Glide.with(context).load(resId).into(this)
}

@BindingAdapter("loadImage")
fun ImageView.loadImage(res: Any) {
    if (res is String) {
        loadImageUrl(res)
        return
    }
    Glide.with(context).load(res).into(this)
}
