package com.ygxj.prshs.ext

import android.view.View
import com.lxj.xpopupext.listener.CommonPickerListener
import com.lxj.xpopupext.listener.TimePickerListener
import java.util.Date

interface SimpleTimePickerListener : TimePickerListener {

    override fun onCancel() {

    }

    override fun onTimeChanged(date: Date) {

    }

    override fun onTimeConfirm(date: Date, view: View) {

    }
}

interface SimpleCommonPickerListener : CommonPickerListener {

    override fun onCancel() {

    }

    override fun onItemSelected(index: Int, data: String) {

    }
}