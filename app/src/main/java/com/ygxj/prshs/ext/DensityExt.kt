package com.ygxj.prshs.ext

import android.content.res.Resources

/**
 * Px to Dp
 */
fun Int.toDp(): Int {
    val density = Resources.getSystem().displayMetrics.density
    return (this / density).toInt()
}

/**
 * Px to Dp
 */
fun Float.toDp(): Float {
    val density = Resources.getSystem().displayMetrics.density
    return this / density
}

/**
 * Dp to Px
 */
fun Int.toPx(): Int {
    val density = Resources.getSystem().displayMetrics.density
    return (this * density).toInt()
}

/**
 * Dp to Px
 */
fun Float.toPx(): Float {
    val density = Resources.getSystem().displayMetrics.density
    return this * density
}

