@file:Suppress("UNCHECKED_CAST")

package com.ygxj.prshs.ext

import android.view.View
import android.view.View.OnClickListener
import androidx.databinding.BindingAdapter

/**
 * 防止重复点击的view扩展
 * 支持BindingAdapter用法,dataBinding中使用
 */
private var clickTime: Long = 0
private const val delayTime = 500

/**
 * 代码中使用,可返回控件类型
 */
fun <T : View> T.click(block: (T) -> Unit) {
    setOnClickListener {
        if (System.currentTimeMillis() - clickTime > delayTime) {
            clickTime = System.currentTimeMillis()
            block(it as T)
        }
    }
}

/**
 * xml中dataBinding使用
 */
@BindingAdapter("click")
fun View.click(listener: OnClickListener) {
    setOnClickListener {
        if (System.currentTimeMillis() - clickTime > delayTime) {
            clickTime = System.currentTimeMillis()
            listener.onClick(it)
        }
    }
}

@BindingAdapter("android:onClick")
fun View.onClick(listener: OnClickListener) {
    setOnClickListener {
        if (System.currentTimeMillis() - clickTime > delayTime) {
            clickTime = System.currentTimeMillis()
            listener.onClick(this)
        }
    }
}



