package com.ygxj.prshs.util

import com.ygxj.prshs.data.entity.FingerDeviceData

object FingerDataUtil {

    private var count = 0
     fun parseFingerData(value: ByteArray): FingerDeviceData? {
         count+=1
         var heartRateWave = 0
         var heartRate = 0
         var bloodOxy = 0
         if (value.size == 6) {
             heartRateWave = BytesOrInt.bytesToInt(value[1])
             heartRate = BytesOrInt.bytesToInt(value[3])
             bloodOxy = BytesOrInt.bytesToInt(value[4])
         }
         if (value.size == 20) {
             for (i in value.indices) {
                 if (BytesOrInt.bytesToInt(value[i]) > 127) { //高位开始标识
                     heartRateWave = BytesOrInt.bytesToInt(value[i + 1])
                     heartRate = BytesOrInt.bytesToInt(value[i + 3])
                     if (BytesOrInt.bytesToInt(value[i + 2]) > 63) heartRate += 127
                     bloodOxy = BytesOrInt.bytesToInt(value[i + 4])
                     break
                 }
             }
         }
         // 过滤无效值:
         // 心率: =127 或 <50 视为无效
         // 血氧: =127 或 <70 或 >100 视为无效
         if (heartRate > 50 && heartRate != 127 && bloodOxy > 70 && bloodOxy != 127 && bloodOxy <= 100) {
            if (count>=100){
                count = 0
                val finger = FingerDeviceData()
                finger.heartRateWave = heartRateWave
                finger.bloodOxygen = bloodOxy
                finger.heartRate = heartRate
                return finger
            }
         }
         return null
    }

    /**
     * 重置参数
     */
    fun reset() {
        count = 0
    }
}