package com.ygxj.prshs.util

import android.content.Context
import android.os.Build
import com.bhm.ble.BleManager
import com.bhm.ble.log.BleLogger
import com.bhm.ble.utils.BleUtil
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.XXPermissions
import com.hjq.permissions.permission.PermissionLists
import com.hjq.permissions.permission.base.IPermission
import com.hjq.toast.Toaster

object PermissionsUtils {

    /**
     * 申请蓝牙权限
     */
    fun requestBlePermission(context: Context, callback: () -> Unit) {

        val isBleSupport = BleManager.get().isBleSupport()
        BleLogger.e("设备是否支持蓝牙: $isBleSupport")

        if (!isBleSupport) {
            Toaster.show("当前设备不支持蓝牙")
            return
        }

        if (!BleUtil.isGpsOpen(context)) {
            Toaster.show("请打开设备的GPS定位")
            return
        }

        if (!BleManager.get().isBleEnable()) {
            Toaster.show("请打开设备的蓝牙开关")
            return
        }

        val permissionArray = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            arrayOf(
                PermissionLists.getAccessFineLocationPermission(),
                PermissionLists.getAccessCoarseLocationPermission()
            )
        } else {
            arrayOf(
                PermissionLists.getBluetoothScanPermission(),
                PermissionLists.getBluetoothConnectPermission()
            )
        }
        //如果有权限
        if (XXPermissions.isGrantedPermissions(context, permissionArray)) {
            callback()
            return
        }

        XXPermissions.with(context).permissions(permissionArray).request(object : OnPermissionCallback {

            override fun onGranted(permissions: List<IPermission?>, allGranted: Boolean) {
                if (!allGranted) {
                    Toaster.show("获取部分权限成功，但部分权限未正常授予")
                    return
                }
                callback()
            }

            override fun onDenied(permissions: List<IPermission?>, doNotAskAgain: Boolean) {
                if (doNotAskAgain) {
                    Toaster.show("被永久拒绝授权，请手动授予权限")
                    // 如果是被永久拒绝就跳转到应用权限系统设置页面
                    XXPermissions.startPermissionActivity(context, permissions)
                } else {
                    Toaster.show("获取权限失败")
                }
            }
        })
    }

}