package com.ygxj.prshs.util

import com.blankj.utilcode.util.TimeUtils
import java.util.Calendar

object AgeUtils {

    /**
     * 根据日期计算年龄
     * @param timeStr 日期字符串
     * @param pattern 日期格式
     * @return 返回年龄
     */
    fun calculateAge(timeStr: String, pattern: String): Int {
        val birthDate = TimeUtils.string2Date(timeStr, pattern) ?: return 0

        val today = Calendar.getInstance()
        val birth = Calendar.getInstance()
        birth.time = birthDate

        val todayYear = today.get(Calendar.YEAR)
        val todayMonth = today.get(Calendar.MONTH)
        val todayDay = today.get(Calendar.DAY_OF_MONTH)

        val birthYear = birth.get(Calendar.YEAR)
        val birthMonth = birth.get(Calendar.MONTH)
        val birthDay = birth.get(Calendar.DAY_OF_MONTH)

        var age = todayYear - birthYear

        if (todayMonth < birthMonth || (todayMonth == birthMonth && todayDay < birthDay)) {
            age--
        }

        return age
    }
}