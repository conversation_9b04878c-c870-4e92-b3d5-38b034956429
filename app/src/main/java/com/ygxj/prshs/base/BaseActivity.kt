package com.ygxj.prshs.base

import android.os.Bundle
import android.view.ViewGroup
import android.view.Window
import androidx.appcompat.app.AppCompatActivity
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.impl.LoadingPopupView
import com.lxj.xpopup.util.KeyboardUtils

abstract class BaseActivity(private val layoutId: Int) : AppCompatActivity() {

    private var loadingDialog: LoadingPopupView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(layoutId)

        getContentView()?.setOnClickListener { hideSoftInput() }
        initData()
    }

    protected abstract fun initData()

    fun showLoadingDialog(text: String) {
        loadingDialog = XPopup.Builder(this)
            .isViewMode(true)
            .hasShadowBg(false)
            .dismissOnTouchOutside(false)
            .dismissOnBackPressed(false)
            .asLoading(text)
        loadingDialog?.show()
    }

    fun hideLoadingDialog() {
        loadingDialog?.dismiss()
        loadingDialog = null
    }

    /**
     * 和 setContentView 对应的方法
     */
    open fun getContentView(): ViewGroup? {
        return findViewById(Window.ID_ANDROID_CONTENT)
    }

    override fun finish() {
        super.finish()
        hideSoftInput()
    }

    protected fun hideSoftInput() {
        currentFocus?.let { KeyboardUtils.hideSoftInput(it) }
    }
}