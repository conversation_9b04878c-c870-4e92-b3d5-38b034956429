package com.ygxj.prshs.base

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import com.bhm.ble.BleManager
import com.bhm.ble.attribute.BleOptions
import com.blankj.utilcode.util.LogUtils
import com.drake.brv.utils.BRV
import com.drake.statelayout.StateConfig
import com.hjq.toast.Toaster
import com.tencent.mmkv.MMKV
import com.ygxj.prshs.BR
import com.ygxj.prshs.R
import com.ygxj.scale.ScaleHelper
import org.litepal.LitePal

class App : Application() {
    companion object {

        // 全局context
        @SuppressLint("StaticFieldLeak")
        lateinit var context: Context
    }

    override fun onCreate() {
        super.onCreate()
        context = this

        // 数据库初始化
        LitePal.initialize(this)

        // MMKV 初始化
        MMKV.initialize(this)

        // toast框架
        Toaster.init(this)

        // 日志框架
        LogUtils.getConfig()
            .setSingleTagSwitch(false)
            .setBorderSwitch(true)
            .setLogHeadSwitch(true)

        // state
        StateConfig.apply {
            emptyLayout = R.layout.layout_empty
            loadingLayout = R.layout.layout_loading
            errorLayout = R.layout.layout_empty
            setRetryIds(R.id.btnRetry)
        }

        // BRV 配置 详见 https://liangjingkanji.github.io/BRV/
        BRV.modelId = BR.m

        //初始化蓝牙
        BleManager.get().init(
            this,
            BleOptions.Builder()
                .setScanMillisTimeOut(3000)
                .setConnectMillisTimeOut(3000)
                .setMaxConnectNum(3)
                //.setScanDeviceName(DeviceManager.fingerName,DeviceManager.bikeName,DeviceManager.breathName)//只扫描这些设备
                .setConnectRetryCountAndInterval(1, 1000)
                .build()
        )

        // 量表库
        ScaleHelper.initialize(this)

        // Net框架初始化
        //NetConfig.initialize(HostManager.host, this) {
        //    // 超时设置
        //    connectTimeout(30, TimeUnit.SECONDS)
        //    // 下载文件时,如果开启日志会导致内存溢出
        //    //addInterceptor(HttpLoggingInterceptor().apply {
        //    //    if (BuildConfig.DEBUG) level = HttpLoggingInterceptor.Level.BODY
        //    //})
        //    // 数据转换器
        //    setConverter(GsonConverter())
        //    // 错误处理
        //    setErrorHandler(GlobalErrorHandler())
        //
        //    // http缓存
        //    cache(Cache(cacheDir, 1024 * 1024 * 128))
        //}
    }
}