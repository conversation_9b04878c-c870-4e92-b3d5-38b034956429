package com.ygxj.prshs.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment

abstract class BaseFragment(private val layoutId: Int) : Fragment() {

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(layoutId, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
    }

    protected abstract fun initData()

    protected fun showLoadingDialog(text: String) {
        try {
            val act = requireActivity() as? BaseActivity
            act?.showLoadingDialog(text)
        } catch (_: Exception) {

        }

    }

    protected fun hideLoadingDialog() {
        try {
            val act = requireActivity() as? BaseActivity
            act?.hideLoadingDialog()
        } catch (_: Exception) {
        }
    }

}