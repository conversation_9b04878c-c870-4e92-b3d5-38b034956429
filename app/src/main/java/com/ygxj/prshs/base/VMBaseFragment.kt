package com.ygxj.prshs.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.ViewModelProvider

abstract class VMBaseFragment<B : ViewDataBinding, VM : BaseViewModel>(
    private val layoutId: Int,
    private val vm: VM
) : BaseFragment(layoutId) {

    protected val model by lazy {
        ViewModelProvider(this, BaseViewModel.createViewModelFactory(vm))[vm::class.java]
    }

    protected lateinit var binding: B

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, layoutId, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        model.showLoadingTipFlag.observe(viewLifecycleOwner) { msg ->
            showLoadingDialog(msg)
        }
        model.hideLoadingTipFlag.observe(viewLifecycleOwner) { hideLoadingDialog() }
    }
}