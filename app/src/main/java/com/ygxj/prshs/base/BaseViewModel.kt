package com.ygxj.prshs.base

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider

abstract class BaseViewModel : ViewModel() {

    companion object {

        fun <T : BaseViewModel> createViewModelFactory(viewModel: T): ViewModelProvider.Factory {
            return BaseModelFactory(viewModel)
        }
    }

    val showLoadingTipFlag = MutableLiveData<String>()
    val hideLoadingTipFlag = MutableLiveData<Int>()
    val finishActivityFlag = MutableLiveData(false)

    protected fun showLoadingTip(msg: String) {
        showLoadingTipFlag.value = msg
    }

    protected fun hideLoadingTip() {
        hideLoadingTipFlag.value = hideLoadingTipFlag.value?.plus(1)
    }

    protected fun finishActivity() {
        finishActivityFlag.postValue(true)
    }
}

@Suppress("UNCHECKED_CAST")
class BaseModelFactory(private val viewModel: BaseViewModel) : ViewModelProvider.Factory {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return viewModel as T
    }
}