package com.ygxj.prshs.base

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.ViewModelProvider

abstract class VMBaseActivity<B : ViewDataBinding, VM : BaseViewModel>(
    private val layoutId: Int,
    private val vm: VM
) : BaseActivity(layoutId) {

    protected val binding: B by lazy {
        DataBindingUtil.setContentView<B>(this, layoutId)
    }

    protected val model by lazy {
        ViewModelProvider(this, BaseViewModel.createViewModelFactory(vm))[vm::class.java]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding.lifecycleOwner = this

        model.showLoadingTipFlag.observe(this) { msg -> showLoadingDialog(msg) }
        model.hideLoadingTipFlag.observe(this) { hideLoadingDialog() }
        model.finishActivityFlag.observe(this) { v -> if (v) finish() }

    }
}