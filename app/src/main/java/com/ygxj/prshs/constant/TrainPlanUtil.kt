package com.ygxj.prshs.constant

import com.hjq.toast.Toaster
import com.ygxj.prshs.R
import com.ygxj.prshs.data.entity.BgmEntity
import com.ygxj.prshs.data.entity.TrainPlanEntity
import com.ygxj.prshs.data.manager.UserManager
import com.ygxj.prshs.data.repository.EmergencyProfileRepository
import java.util.Calendar
import java.util.Random

object TrainPlanUtil {

    /**
     * 获取训练方案列表
     * @param quickEvaluateId 5个快速评估的ID,当做完快速评估后,向用户推荐训练方案时需要传入
     * 5个快速评估分别是:
     * 690	紧张情绪状态评估问卷
     * 691	害怕/恐惧情绪状态评估问卷
     * 692	悲伤情绪状态评估问卷
     * 693	愤怒情绪状态评估问卷
     * 694	无助感状态评估问卷
     */
    fun getTrainPlanList(quickEvaluateId: Int? = null): List<TrainPlanEntity> {
        val loginUser = UserManager.loginUser
        if (loginUser == null) {
            Toaster.show("未能获取到登录用户信息,无法获取训练方案列表")
            return emptyList()
        }
        val profile = EmergencyProfileRepository.findEmergencyProfileByUserId(loginUser.id)
        if (profile == null) {
            Toaster.show("未能获取到急救档案信息,无法获取训练方案列表")
            return emptyList()
        }

        val allTrainPlan = listOf(
            TrainPlanEntity(1, "呼吸放松法", R.raw.hu_xi_fang_song_fa),
            TrainPlanEntity(2, "渐进式肌肉放松", R.raw.jian_jin_ji_rou_fang_song_fa),
            TrainPlanEntity(3, "心理着陆技术", R.raw.xiang_xiang_fang_song_fa),
            TrainPlanEntity(4, "内在安全岛技术", R.raw.hu_xi_fang_song_fa),
            TrainPlanEntity(5, "蝴蝶拍技术", R.raw.hu_xi_fang_song_fa),
            TrainPlanEntity(6, "积极想象技术", R.raw.hu_xi_fang_song_fa),
            TrainPlanEntity(7, "正念呼吸技术", R.raw.hu_xi_fang_song_fa),
            TrainPlanEntity(8, "想象放松法", R.raw.hu_xi_fang_song_fa)
        )

        return when {
            // 如果危机事件发生在一个月内
            profile.isInOneMonth() -> {
                // 如果未传入 quickEvaluateId,则返回所有训练方案
                if (quickEvaluateId == null) {
                    allTrainPlan
                } else {
                    // 如果传入了 quickEvaluateId,则根据不同的 quickEvaluateId 返回不同的训练方案
                    when (quickEvaluateId) {
                        690 -> allTrainPlan.filter { it.id in arrayOf(1, 2, 3) } // 紧张情绪状态评估
                        691 -> allTrainPlan.filter { it.id in arrayOf(4, 5, 6) } // 害怕/恐惧情绪状态评估
                        692 -> allTrainPlan.filter { it.id in arrayOf(6, 7, 5) } // 悲伤情绪状态评估
                        693 -> allTrainPlan.filter { it.id in arrayOf(3, 1, 8) } // 愤怒情绪状态评估
                        694 -> allTrainPlan.filter { it.id in arrayOf(6, 7, 3) } // 无助感状态评估
                        else -> emptyList()
                    }
                }
            }
            // 危机事件发生在1-3月内,或发生在三个月前：每周在八个训练方案中随机分配3个训练
            profile.isInThreeMonth() || profile.isBeforeThreeMonth() -> {
                // 获取当前周数作为随机种子
                val calendar = Calendar.getInstance()
                val weekOfYear = calendar.get(Calendar.WEEK_OF_YEAR)
                // 使用当前周数作为随机种子，打乱训练方案列表并取前三个
                // 这样可以确保周内固定,当周数变化时,随机的训练方案才会变化
                val random = Random(weekOfYear.toLong())
                allTrainPlan.shuffled(random).take(3)
            }
            // 其他情况返回所有训练方案
            else -> allTrainPlan
        }
    }

    /**
     * 获取训练方案背景音乐列表
     */
    fun getTrainPlanBgmList(): List<BgmEntity> {
        return listOf(
            BgmEntity(1, "雨声", R.raw.bgm_rain),
            BgmEntity(2, "小溪", R.raw.bgm_rain),
            BgmEntity(3, "鸟鸣", R.raw.bgm_rain),
            BgmEntity(4, "海浪", R.raw.bgm_rain),
            BgmEntity(5, "夏夜", R.raw.bgm_rain)
        )
    }
}