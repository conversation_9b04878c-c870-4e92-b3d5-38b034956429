package com.ygxj.prshs.constant

import com.ygxj.prshs.data.entity.DangerCategoryEntity
import com.ygxj.prshs.data.entity.DangerEventEntity

/**
 * 危机事件
 */
object DangerEvent {

    /**
     * 获取危机事件数据
     * 常量会保持选中状态,不要使用常量
     */
    fun getData(): ArrayList<DangerCategoryEntity> {
        return arrayListOf(
            DangerCategoryEntity(
                1, "自然灾害类",
                arrayListOf(
                    DangerEventEntity("地震"),
                    DangerEventEntity("洪水/海啸"),
                    DangerEventEntity("台风/飓风"),
                    DangerEventEntity("山火/森林火灾"),
                    DangerEventEntity("泥石流/山体滑坡")
                )
            ),
            DangerCategoryEntity(
                2, "社会安全类",
                arrayListOf(
                    DangerEventEntity("恐怖袭击"),
                    DangerEventEntity("暴力冲突/骚乱"),
                    DangerEventEntity("人质劫持事件"),
                    DangerEventEntity("枪击/持械伤人事件"),
                    DangerEventEntity("大规模诈骗/金融犯罪")
                )
            ),
            DangerCategoryEntity(
                3, "事故灾难类",
                arrayListOf(
                    DangerEventEntity("重大交通事故（空难/火车脱轨等）"),
                    DangerEventEntity("危险化学品泄漏"),
                    DangerEventEntity("核辐射事故"),
                    DangerEventEntity("建筑坍塌"),
                    DangerEventEntity("爆炸事件")
                )

            ),
            DangerCategoryEntity(
                4, "公共卫生类",
                arrayListOf(
                    DangerEventEntity("突发传染病疫情（如新冠级别）"),
                    DangerEventEntity("群体性食物中毒"),
                    DangerEventEntity("饮用水污染事件"),
                )
            ),
            DangerCategoryEntity(
                5, "个人与家庭危机",
                arrayListOf(
                    DangerEventEntity("重大疾病确诊（癌症等）"),
                    DangerEventEntity("亲人意外死亡"),
                    DangerEventEntity("家庭暴力"),
                    DangerEventEntity("离婚/情感破裂"),
                    DangerEventEntity("失业/破产")
                )
            ),
            DangerCategoryEntity(
                6, "心理与学业职业",
                arrayListOf(
                    DangerEventEntity("重大考试失败（高考/职业资格考等）"),
                    DangerEventEntity("校园欺凌/职场霸凌"),
                    DangerEventEntity("重度抑郁/自杀倾向"),
                    DangerEventEntity("重要项目失败（创业/科研等）")
                )
            )
        )

    }
}