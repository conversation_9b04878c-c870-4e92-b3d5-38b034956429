package com.ygxj.prshs.constant

import com.ygxj.prshs.data.entity.IssueEntity

object IssuesStatus {

    fun getIssues(): List<IssueEntity> {
        return listOf(
            IssueEntity("画面闯入"),
            IssueEntity("胸口发紧"),
            IssueEntity("焦虑不安"),
            IssueEntity("失眠多梦"),
            IssueEntity("情绪低落"),
            IssueEntity("回避行为"),
            IssueEntity("过度警觉"),
            IssueEntity("身体疼痛"),
            IssueEntity("其他")
        )
    }
}