<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <merge
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:background="#4186b4"
        tools:parentTag="android.widget.ScrollView">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="2dp"
                android:text="量表信息"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvIntro"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:lineSpacingExtra="2dp"
                android:textColor="@color/white"
                android:textSize="10sp"
                tools:text="简介：父母教养方式问卷于1980年由瑞典Umea大学精神医学系C.Perris等人编制，用于评定父母的教养方式。中文版EMBU由中国医科大学心理学教研室的岳冬梅、李鸣果、金魁和、丁宝坤修订。修订后的量表经过主因素分析，分为2部分，每部分为66个条目。" />

            <TextView
                android:id="@+id/tvGuide"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:lineSpacingExtra="2dp"
                android:textColor="@color/white"
                android:textSize="10sp"
                tools:text="指导语：在每个人成长的过程中，父母的言传身教都给我们留下了深刻的印象。请您努力回想一下在父母对待您的教育方式，回答下列问题。" />

            <TextView
                android:id="@+id/tvQuestionCount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:lineSpacingExtra="2dp"
                android:textColor="@color/white"
                android:textSize="10sp"
                tools:text="题目数量：10题" />
        </LinearLayout>
    </merge>
</layout>