<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="m"
            type="com.ygxj.prshs.data.entity.IssueEntity" />
    </data>

    <LinearLayout
        android:id="@+id/llRoot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            app:loadImage="@{m.checkStatusIcon()}"
            tools:src="@mipmap/ic_checkbox_checked" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="@{m.title}"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="紧张恐惧"
            tools:textColor="@color/black" />


    </LinearLayout>
</layout>