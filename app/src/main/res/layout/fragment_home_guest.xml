<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        tools:background="@mipmap/bg_main"
        tools:context=".ui.main.HomeFragmentGuest">

        <com.ygxj.prshs.ui.view.LogoView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="50dp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.ygxj.prshs.ui.view.HomeMenuItemView
                android:id="@+id/btnVideo1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:menu_icon="@mipmap/home_menu_hu_xi_fang_song"
                app:menu_title="呼吸放松法" />

            <com.ygxj.prshs.ui.view.HomeMenuItemView
                android:id="@+id/btnVideo2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                app:menu_icon="@mipmap/home_menu_xiang_xiang_fang_song"
                app:menu_title="想象放松法" />

            <com.ygxj.prshs.ui.view.HomeMenuItemView
                android:id="@+id/btnVideo3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                app:menu_icon="@mipmap/home_menu_jian_jin_fang_song"
                app:menu_title="渐进式肌肉放松法" />

        </LinearLayout>
    </LinearLayout>
</layout>