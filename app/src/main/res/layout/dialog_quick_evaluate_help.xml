<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="603dp"
        android:layout_height="364dp"
        android:background="@mipmap/bg_dialog_evaluate_tips"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingVertical="20dp"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            tools:text="量表名称" />

        <TextView
            android:id="@+id/tvGuide"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="30dp"
            android:layout_marginBottom="30dp"
            android:layout_weight="1"
            android:lineSpacingExtra="4dp"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="@tools:sample/lorem/random" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnConfirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="20dp"
            android:background="@mipmap/btn_start_evaluate"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:text="开始评估"
            android:textColor="@color/white" />
    </LinearLayout>
</layout>