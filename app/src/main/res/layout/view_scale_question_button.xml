<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        tools:parentTag="LinearLayout">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnPre"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:background="@mipmap/btn_start_evaluate"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:text="上一题"
            android:textColor="@color/white" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnNext"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginHorizontal="40dp"
            android:background="@mipmap/btn_start_evaluate"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:text="下一题"
            android:textColor="@color/white" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnCommit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:background="@mipmap/btn_start_evaluate"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:text="提交"
            android:textColor="@color/white" />

    </LinearLayout>
</layout>