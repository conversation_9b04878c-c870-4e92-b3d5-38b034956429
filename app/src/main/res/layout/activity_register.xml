<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="activity"
            type="android.app.Activity" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_common"
        android:gravity="center"
        android:orientation="vertical"
        tools:context=".ui.register.RegisterActivity">

        <com.ygxj.prshs.ui.view.LogoView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <com.hjq.shape.layout.ShapeLinearLayout
            android:layout_width="370dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="50dp"
            android:paddingTop="15dp"
            android:paddingBottom="5dp"
            app:shape_radius="10dp"
            app:shape_solidGradientEndColor="#7cd0e5"
            app:shape_solidGradientOrientation="topToBottom"
            app:shape_solidGradientStartColor="#498acc"
            app:shape_strokeColor="#BFD6EA"
            app:shape_strokeSize="4dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="注册"
                android:textColor="@color/white"
                android:textSize="18sp" />

            <com.hjq.shape.view.ShapeEditText
                android:id="@+id/etAccount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:hint="请输入账号"
                android:inputType="text"
                android:lines="1"
                android:maxLines="1"
                android:padding="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textColorHint="#999999"
                android:textSize="14sp"
                app:shape_radius="8dp"
                app:shape_solidColor="@color/white" />

            <com.hjq.shape.view.ShapeEditText
                android:id="@+id/etPwd"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp"
                android:hint="请输入密码"
                android:inputType="textPassword"
                android:lines="1"
                android:maxLines="1"
                android:padding="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textColorHint="#999999"
                android:textSize="14sp"
                app:shape_radius="8dp"
                app:shape_solidColor="@color/white" />

            <com.hjq.shape.view.ShapeEditText
                android:id="@+id/etName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:hint="请输入姓名"
                android:inputType="text"
                android:lines="1"
                android:maxLines="1"
                android:padding="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textColorHint="#999999"
                android:textSize="14sp"
                app:shape_radius="8dp"
                app:shape_solidColor="@color/white" />

            <com.hjq.shape.view.ShapeTextView
                android:id="@+id/tvBirthDate"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp"
                android:drawableEnd="@drawable/baseline_arrow_drop_down_24"
                android:drawableTint="@color/colorPrimary"
                android:hint="请选择出生日期"
                android:inputType="text"
                android:lines="1"
                android:maxLines="1"
                android:padding="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textColorHint="#999999"
                android:textSize="14sp"
                app:shape_radius="8dp"
                app:shape_solidColor="@color/white" />

            <com.hjq.shape.view.ShapeTextView
                android:id="@+id/tvSex"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp"
                android:drawableEnd="@drawable/baseline_arrow_drop_down_24"
                android:drawableTint="@color/colorPrimary"
                android:hint="请选择性别"
                android:inputType="text"
                android:lines="1"
                android:maxLines="1"
                android:padding="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textColorHint="#999999"
                android:textSize="14sp"
                app:shape_radius="8dp"
                app:shape_solidColor="@color/white" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:orientation="horizontal">

                <com.hjq.shape.view.ShapeButton
                    android:id="@+id/btnRegister"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:paddingVertical="10dp"
                    android:text="注册"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    app:shape_radius="8dp"
                    app:shape_solidColor="@color/colorPrimary"
                    app:shape_solidPressedColor="@color/colorAccent" />

                <com.hjq.shape.view.ShapeButton
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:paddingVertical="10dp"
                    android:text="返回"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    app:click="@{()->activity.onBackPressed()}"
                    app:shape_radius="8dp"
                    app:shape_solidColor="@color/colorPrimary"
                    app:shape_solidPressedColor="@color/colorAccent" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:paddingVertical="5dp"
                android:text="已有账号，去登录"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:click="@{()->activity.onBackPressed()}" />

        </com.hjq.shape.layout.ShapeLinearLayout>

    </LinearLayout>
</layout>