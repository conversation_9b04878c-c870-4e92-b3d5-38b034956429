<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main"
        android:orientation="vertical"
        tools:context=".ui.emergencyAdjust.EmergencyAdjustDetailActivity">

        <com.ygxj.prshs.ui.view.TitleBarView
            android:id="@+id/vTitleBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:title="情绪急救" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="20dp"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:paddingTop="30dp">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/selector_emotion_menu_bg"
                    tools:text="识别情绪风暴"
                    android:textColor="@color/selector_button_text_color" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="20dp"
                    android:background="@drawable/selector_emotion_menu_bg"
                    tools:text="即时平复技巧"
                    android:textColor="@color/selector_button_text_color" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/selector_emotion_menu_bg"
                    tools:text="安全空间构建"
                    android:textColor="@color/selector_button_text_color" />

            </LinearLayout>

            <com.hjq.shape.layout.ShapeFrameLayout
                android:id="@+id/playerContainer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginEnd="20dp"
                android:layout_marginBottom="20dp"
                android:clipChildren="true"
                app:shape_solidColor="@color/black"
                app:shape_strokeColor="@color/white"
                app:shape_strokeSize="2dp">

                <!-- StyledPlayerView 自带控制 UI -->
                <com.google.android.exoplayer2.ui.StyledPlayerView
                    android:background="@color/black"
                    android:id="@+id/playerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="2dp"
                    android:keepScreenOn="true"
                    app:controller_layout_id="@layout/view_video_player_control_toggle_fullscreen"
                    app:show_buffering="when_playing" />
            </com.hjq.shape.layout.ShapeFrameLayout>
        </LinearLayout>
    </LinearLayout>
</layout>