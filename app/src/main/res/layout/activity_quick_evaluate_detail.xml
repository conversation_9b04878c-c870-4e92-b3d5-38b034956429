<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main"
        android:orientation="vertical"
        tools:context=".ui.testAdjust.quickEvaluate.QuickEvaluateDetailActivity">

        <com.ygxj.prshs.ui.view.TitleBarView
            android:id="@+id/vTitleBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:title="紧张状态" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:layout_marginHorizontal="100dp"
            android:layout_marginTop="20dp"
            android:layout_weight="1"
            android:background="@mipmap/bg_dialog_evaluate_tips">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="20dp"
                tools:listitem="@layout/item_scale_question" />
        </FrameLayout>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnConfirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:background="@mipmap/btn_start_evaluate"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:text="提交"
            android:textColor="@color/white" />

    </LinearLayout>
</layout>