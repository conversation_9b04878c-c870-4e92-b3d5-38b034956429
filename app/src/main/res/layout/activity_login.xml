<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_common"
        android:gravity="center"
        android:orientation="vertical"
        tools:context=".ui.login.LoginActivity">

        <com.ygxj.prshs.ui.view.LogoView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <com.hjq.shape.layout.ShapeLinearLayout
            android:layout_width="370dp"
            android:layout_height="wrap_content"
            android:layout_marginVertical="30dp"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="50dp"
            android:paddingVertical="35dp"
            app:shape_radius="10dp"
            app:shape_solidGradientEndColor="#7cd0e5"
            app:shape_solidGradientOrientation="topToBottom"
            app:shape_solidGradientStartColor="#498acc"
            app:shape_strokeColor="#BFD6EA"
            app:shape_strokeSize="4dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="用户登录"
                android:textColor="@color/white"
                android:textSize="18sp" />

            <com.hjq.shape.layout.ShapeLinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="10dp"
                app:shape_radius="8dp"
                app:shape_solidColor="@color/white">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_account" />

                <com.hjq.shape.view.ShapeView
                    android:layout_width="2dp"
                    android:layout_height="match_parent"
                    android:layout_margin="10dp"
                    app:shape_solidColor="#e3e4e5" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/etAccount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:hint="请输入账号"
                    android:inputType="text"
                    android:lines="1"
                    android:maxLines="1"
                    android:paddingVertical="10dp"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

            </com.hjq.shape.layout.ShapeLinearLayout>

            <com.hjq.shape.layout.ShapeLinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="10dp"
                app:shape_radius="8dp"
                app:shape_solidColor="@color/white">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_pwd" />

                <com.hjq.shape.view.ShapeView
                    android:layout_width="2dp"
                    android:layout_height="match_parent"
                    android:layout_margin="10dp"
                    app:shape_solidColor="#e3e4e5" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/etPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:hint="请输入密码"
                    android:inputType="textPassword"
                    android:lines="1"
                    android:maxLines="1"
                    android:paddingVertical="10dp"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

            </com.hjq.shape.layout.ShapeLinearLayout>

            <com.hjq.shape.view.ShapeButton
                android:id="@+id/btn_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:gravity="center"
                android:paddingVertical="10dp"
                android:text="登录"
                android:textColor="@color/white"
                android:textSize="16sp"
                app:shape_radius="8dp"
                app:shape_solidColor="@color/colorPrimary"
                app:shape_solidPressedColor="@color/colorAccent" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btn_register"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingVertical="5dp"
                    android:text="还没有账号？去注册"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

                <Space
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/btn_guest_login"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingVertical="5dp"
                    android:text="我是游客"
                    android:textColor="@color/colorPrimaryDark"
                    android:textSize="14sp" />


            </LinearLayout>
        </com.hjq.shape.layout.ShapeLinearLayout>

    </LinearLayout>
</layout>