<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <merge
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:orientation="vertical"
        tools:parentTag="LinearLayout">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="基础信息"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <com.hjq.shape.view.ShapeView
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_marginTop="5dp"
                app:shape_solidColor="#91B8E3" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:baselineAligned="false"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="2dp"
                    android:text="姓名"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

                <com.hjq.shape.view.ShapeEditText
                    android:id="@+id/etName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:lines="1"
                    android:maxLines="1"
                    android:padding="8dp"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    app:shape_radius="4dp"
                    app:shape_solidColor="#D8E6F2"
                    app:shape_strokeColor="#7A5F97D9"
                    app:shape_strokeSize="1dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="30dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="2dp"
                    android:text="性别"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

                <com.hjq.shape.view.ShapeTextView
                    android:id="@+id/tvSex"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:drawableEnd="@drawable/baseline_arrow_drop_down_24"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="6dp"
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    app:shape_radius="4dp"
                    app:shape_solidColor="#D8E6F2"
                    app:shape_strokeColor="#7A5F97D9"
                    app:shape_strokeSize="1dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="2dp"
                    android:text="出生日期"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

                <com.hjq.shape.view.ShapeTextView
                    android:id="@+id/tvBirthDate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:drawableEnd="@drawable/baseline_arrow_drop_down_24"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="6dp"
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    app:shape_radius="4dp"
                    app:shape_solidColor="#D8E6F2"
                    app:shape_strokeColor="#7A5F97D9"
                    app:shape_strokeSize="1dp" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:baselineAligned="false"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="2dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="年龄"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="  (根据出生日期自动计算，无需手动填写)"
                        android:textColor="#999999"
                        android:textSize="8sp" />
                </LinearLayout>

                <com.hjq.shape.view.ShapeTextView
                    android:id="@+id/tvAge"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="8dp"
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    app:shape_radius="4dp"
                    app:shape_solidColor="#D8E6F2"
                    app:shape_strokeColor="#7A5F97D9"
                    app:shape_strokeSize="1dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="30dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="2dp"
                    android:text="联系方式"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

                <com.hjq.shape.view.ShapeEditText
                    android:id="@+id/etPhone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:lines="1"
                    android:maxLines="1"
                    android:padding="8dp"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    app:shape_radius="4dp"
                    app:shape_solidColor="#D8E6F2"
                    app:shape_strokeColor="#7A5F97D9"
                    app:shape_strokeSize="1dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="2dp"
                    android:text="紧急联系人"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

                <com.hjq.shape.view.ShapeEditText
                    android:id="@+id/etEmergencyName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:lines="1"
                    android:maxLines="1"
                    android:padding="8dp"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    app:shape_radius="4dp"
                    app:shape_solidColor="#D8E6F2"
                    app:shape_strokeColor="#7A5F97D9"
                    app:shape_strokeSize="1dp" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:baselineAligned="false"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="2dp"
                    android:text="紧急联系人联系方式"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

                <com.hjq.shape.view.ShapeEditText
                    android:id="@+id/etEmergencyPhone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:lines="1"
                    android:maxLines="1"
                    android:padding="8dp"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    app:shape_radius="4dp"
                    app:shape_solidColor="#D8E6F2"
                    app:shape_strokeColor="#7A5F97D9"
                    app:shape_strokeSize="1dp" />

            </LinearLayout>

            <Space
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginHorizontal="30dp"
                android:layout_weight="1" />

            <Space
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />
        </LinearLayout>

    </merge>
</layout>