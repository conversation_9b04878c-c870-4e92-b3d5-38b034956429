<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.ygxj.prshs.R" />

        <variable
            name="value"
            type="String" />

        <variable
            name="checked"
            type="Boolean" />
    </data>

    <com.hjq.shape.layout.ShapeLinearLayout
        android:id="@+id/llRoot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="10dp"
        android:paddingVertical="5dp"
        android:layout_marginBottom="10dp"
        app:shape_radius="20dp"
        app:shape_strokeColor="#59AFD5"
        app:shape_strokeSize="1dp"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:id="@+id/ivSelected"
            android:layout_width="20dp"
            android:layout_height="20dp"
            app:loadImage="@{checked?R.mipmap.ic_scale_option_checked:R.mipmap.ic_scale_option_unchecked}"
            tools:src="@mipmap/ic_scale_option_unchecked" />

        <TextView
            android:id="@+id/tvOption"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="@{value}"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="1. 你是否出现以下情况" />


    </com.hjq.shape.layout.ShapeLinearLayout>

</layout>
