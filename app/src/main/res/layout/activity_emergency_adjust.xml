<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main"
        android:orientation="vertical"
        tools:context=".ui.emergencyAdjust.EmergencyAdjustActivity">

        <com.ygxj.prshs.ui.view.TitleBarView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:title="紧急调节" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="horizontal">

            <com.ygxj.prshs.ui.view.SubMenuItemView
                android:id="@+id/btnEmotionEmergencyHelp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:sub_menu_icon="@mipmap/sub_menu_emotion_help"
                app:sub_menu_title="情绪急救" />

            <com.ygxj.prshs.ui.view.SubMenuItemView
                android:id="@+id/btnPressRelease"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                app:sub_menu_icon="@mipmap/sub_menu_press_release"
                app:sub_menu_title="压力释放" />

            <com.ygxj.prshs.ui.view.SubMenuItemView
                android:id="@+id/btnKnowledgeRebuild"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:sub_menu_icon="@mipmap/sub_menu_knowledge_rebuild"
                app:sub_menu_title="认知重构" />

        </LinearLayout>

    </LinearLayout>
</layout>