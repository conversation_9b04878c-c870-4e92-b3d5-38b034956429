<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <merge
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:background="@color/black"
        tools:parentTag="android.widget.FrameLayout">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:minWidth="300dp"
            tools:itemCount="3"
            tools:listitem="@layout/item_scale_option" />

        <Button
            android:id="@+id/btnAutoTest"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:background="@color/black"
            android:drawableTop="@mipmap/ic_launcher"
            android:drawablePadding="4dp"
            android:padding="4dp"
            android:text="自动测试:关闭"
            android:visibility="gone"
            tools:visibility="visible" />

    </merge>
</layout>