<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.ygxj.prshs.ui.scale.ScaleTestModel" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main"
        android:orientation="vertical"
        tools:context=".ui.scale.ScaleTestActivity">

        <com.ygxj.prshs.ui.view.TitleBarView
            android:id="@+id/vTitleBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:title="@{model.scale.scaleName}"
            tools:title="量表详情" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginHorizontal="150dp"
            android:layout_marginVertical="40dp"
            android:background="@mipmap/bg_dialog_evaluate_tips"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:padding="20dp">

            <com.ygxj.prshs.ui.scale.view.ScaleQuestionTitleView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:model="@{model}"
                app:questionIndex="@{model.questionIndex}" />

            <com.ygxj.prshs.ui.scale.view.ScaleQuestionOptionView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="20dp"
                android:layout_weight="1"
                app:model="@{model}"
                app:questionIndex="@{model.questionIndex}" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.ygxj.prshs.ui.scale.view.ScaleQuestionProgressView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|end"
                    app:model="@{model}"
                    app:questionIndex="@{model.questionIndex}" />

                <com.ygxj.prshs.ui.scale.view.ScaleQuestionButton
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    app:model="@{model}" />
            </FrameLayout>


        </LinearLayout>


    </LinearLayout>
</layout>