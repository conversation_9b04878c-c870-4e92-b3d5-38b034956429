<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.ygxj.prshs.ui.eyeMove.EyeMoveTrainModel" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main_blur"
        android:orientation="vertical"
        tools:context=".ui.eyeMove.EyeMoveTrainActivity">

        <com.ygxj.prshs.ui.view.TitleBarView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:title="眼动训练" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="30dp"
            android:baselineAligned="false"
            android:orientation="horizontal">

            <com.hjq.shape.layout.ShapeLinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.35"
                android:orientation="vertical"
                app:shape_radius="10dp"
                app:shape_solidGradientEndColor="#333F7EB4"
                app:shape_solidGradientOrientation="topLeftToBottomRight"
                app:shape_solidGradientStartColor="#4A93C8FF"
                app:shape_strokeColor="#80c3fd"
                app:shape_strokeSize="1dp">

                <com.hjq.shape.view.ShapeView
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    app:shape_solidGradientCenterColor="@color/white"
                    app:shape_solidGradientEndColor="@android:color/transparent"
                    app:shape_solidGradientStartColor="@android:color/transparent" />

                <com.ygxj.prshs.ui.eyeMove.view.EyeMoveTrainStepIndicatorView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="15dp"
                    android:layout_marginTop="15dp"
                    app:currentStep="@{model.currentStep}" />

                <com.ygxj.prshs.ui.eyeMove.view.EyeMoveTrainSpeedControlView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="15dp"
                    android:layout_marginTop="15dp" />

                <com.ygxj.prshs.ui.eyeMove.view.EyeMoveTrainSizeControlView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="15dp"
                    android:layout_marginTop="15dp" />

                <com.ygxj.prshs.ui.eyeMove.view.EyeMoveTrainColorControlView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="15dp"
                    android:layout_marginTop="15dp"
                    app:model="@{model}" />
            </com.hjq.shape.layout.ShapeLinearLayout>

            <com.hjq.shape.layout.ShapeLinearLayout
                android:id="@+id/llRight"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="15dp"
                android:layout_weight="1"
                android:orientation="vertical"
                app:shape_radius="10dp"
                app:shape_solidGradientEndColor="#333F7EB4"
                app:shape_solidGradientOrientation="topLeftToBottomRight"
                app:shape_solidGradientStartColor="#4A93C8FF"
                app:shape_strokeColor="#80c3fd"
                app:shape_strokeSize="1dp">

                <com.hjq.shape.view.ShapeView
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    app:shape_solidGradientCenterColor="@color/white"
                    app:shape_solidGradientEndColor="@android:color/transparent"
                    app:shape_solidGradientStartColor="@android:color/transparent" />

                <FrameLayout
                    android:id="@+id/flContainer"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnNext"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="下一步" />
            </com.hjq.shape.layout.ShapeLinearLayout>

        </LinearLayout>

    </LinearLayout>
</layout>