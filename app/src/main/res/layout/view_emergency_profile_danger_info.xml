<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <merge
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:orientation="vertical"
        tools:parentTag="LinearLayout">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="危机信息"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <com.hjq.shape.view.ShapeView
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_marginTop="5dp"
                app:shape_solidColor="#91B8E3" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:baselineAligned="false"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="2dp"
                android:text="危机事件发生日期"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <com.hjq.shape.view.ShapeTextView
                android:id="@+id/tvDangerDate"
                android:layout_width="300dp"
                android:layout_height="wrap_content"
                android:drawableEnd="@drawable/baseline_arrow_drop_down_24"
                android:gravity="center_vertical"
                android:paddingHorizontal="8dp"
                android:paddingVertical="6dp"
                android:textColor="@color/black"
                android:textSize="12sp"
                app:shape_radius="4dp"
                app:shape_solidColor="#D8E6F2"
                app:shape_strokeColor="#7A5F97D9"
                app:shape_strokeSize="1dp" />
        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="2dp"
            android:text="危机事件类型"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <com.hjq.shape.layout.ShapeFrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="10dp"
            app:shape_radius="4dp"
            app:shape_solidColor="#D8E6F2"
            app:shape_strokeColor="#7A5F97D9"
            app:shape_strokeSize="1dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvDangerEvent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:itemCount="30"
                tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                tools:spanCount="3" />

        </com.hjq.shape.layout.ShapeFrameLayout>


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="2dp"
            android:text="危机事件简述"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <com.hjq.shape.view.ShapeEditText
            android:id="@+id/etDangerDesc"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:gravity="top|start"
            android:inputType="text"
            android:lines="1"
            android:maxLines="1"
            android:padding="6dp"
            android:singleLine="true"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:shape_radius="4dp"
            app:shape_solidColor="#D8E6F2"
            app:shape_strokeColor="#7A5F97D9"
            app:shape_strokeSize="1dp"
            tools:text="123" />

    </merge>
</layout>