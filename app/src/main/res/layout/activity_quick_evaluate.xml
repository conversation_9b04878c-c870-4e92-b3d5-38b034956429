<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main"
        android:orientation="vertical"
        tools:context=".ui.testAdjust.quickEvaluate.QuickEvaluateActivity">

        <com.ygxj.prshs.ui.view.TitleBarView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:title="快速评估" />

        <com.drake.statelayout.StateLayout
            android:id="@+id/state"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="50dp"
            android:layout_marginTop="30dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:spanCount="3"
                tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                tools:listitem="@layout/item_scale" />

        </com.drake.statelayout.StateLayout>
    </LinearLayout>
</layout>