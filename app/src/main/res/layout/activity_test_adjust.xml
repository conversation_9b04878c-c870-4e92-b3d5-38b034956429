<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_main"
        android:orientation="vertical"
        tools:context=".ui.testAdjust.TestAdjustActivity">

        <com.ygxj.prshs.ui.view.TitleBarView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:title="测试后调节" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="horizontal">

            <com.ygxj.prshs.ui.view.SubMenuItemView
                android:id="@+id/btnQuickEvaluate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:siv_padding="20dp"
                app:sub_menu_icon="@mipmap/sub_menu_quick_evaluate"
                app:sub_menu_title="快速评估" />

            <com.ygxj.prshs.ui.view.SubMenuItemView
                android:id="@+id/btnSystemEvaluate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="50dp"
                app:siv_padding="20dp"
                app:sub_menu_icon="@mipmap/sub_menu_system_evaluate"
                app:sub_menu_title="系统评估" />
        </LinearLayout>
    </LinearLayout>
</layout>