<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <merge
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:gravity="center"
        tools:parentTag="LinearLayout">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/ic_logo" />

        <com.hjq.shape.view.ShapeTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:shadowColor="#474788CB"
            android:shadowDx="0"
            android:shadowDy="5"
            android:shadowRadius="1"
            android:text="@string/app_name"
            android:textSize="34sp"
            android:textStyle="bold"
            app:shape_textEndColor="#b5e2fc"
            app:shape_textGradientOrientation="vertical"
            app:shape_textStartColor="@color/white" />


    </merge>
</layout>