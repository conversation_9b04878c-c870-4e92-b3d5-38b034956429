<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <com.hjq.shape.layout.ShapeLinearLayout
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:shape_radius="10dp"
        app:shape_solidGradientEndColor="#8042affb"
        app:shape_solidGradientStartColor="#803470A4"
        app:shape_strokeColor="#5ec0fe"
        app:shape_strokeSize="2dp">

        <com.hjq.shape.view.ShapeTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingVertical="10dp"
            android:text="音量调节"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:shape_radiusInTopLeft="10dp"
            app:shape_radiusInTopRight="10dp"
            app:shape_solidGradientEndColor="#CC42AFFB"
            app:shape_solidGradientStartColor="#CC3470A4" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="40dp"
            android:layout_marginVertical="30dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="系统音量"
                android:textColor="#bae2fc" />

            <SeekBar
                android:id="@+id/seekBarSystemVolume"
                android:layout_width="match_parent"
                android:layout_height="24dp"
                android:max="100"
                android:paddingVertical="6dp"
                android:progress="0"
                android:progressDrawable="@drawable/volume_seekbar_track"
                android:splitTrack="false"
                android:thumb="@drawable/volume_seekbar_thumb" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="背景音量"
                android:textColor="#bae2fc" />

            <SeekBar
                android:id="@+id/seekBarAudioPlayer"
                android:layout_width="match_parent"
                android:layout_height="24dp"
                android:max="100"
                android:paddingVertical="6dp"
                android:progress="0"
                android:progressDrawable="@drawable/volume_seekbar_track"
                android:splitTrack="false"
                android:thumb="@drawable/volume_seekbar_thumb" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="讲师音量"
                android:textColor="#bae2fc" />

            <SeekBar
                android:id="@+id/seekBarVideoPlayer"
                android:layout_width="match_parent"
                android:layout_height="24dp"
                android:max="100"
                android:paddingVertical="6dp"
                android:progress="0"
                android:progressDrawable="@drawable/volume_seekbar_track"
                android:splitTrack="false"
                android:thumb="@drawable/volume_seekbar_thumb" />
        </LinearLayout>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="20dp"
            android:background="@mipmap/btn_start_evaluate"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:text="返回"
            android:textColor="@color/white" />

    </com.hjq.shape.layout.ShapeLinearLayout>
</layout>