<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_common"
        android:orientation="vertical"
        tools:context=".ui.emergencyProfile.EmergencyProfileActivity">

        <com.ygxj.prshs.ui.view.TitleBarView
            android:id="@+id/titleBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:title="急救档案" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <com.hjq.shape.layout.ShapeLinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:orientation="vertical"
                android:padding="20dp"
                app:shape_radius="8dp"
                app:shape_solidColor="#66FFFFFF">

                <com.ygxj.prshs.ui.emergencyProfile.EmergencyProfileBaseInfoFormView
                    android:id="@+id/vBaseInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <com.ygxj.prshs.ui.emergencyProfile.EmergencyProfileDangerInfoFormView
                    android:id="@+id/vDangerInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    tools:layout_height="300dp" />

            </com.hjq.shape.layout.ShapeLinearLayout>
        </androidx.core.widget.NestedScrollView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnSave"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="保存" />

            <Button
                android:id="@+id/btnCancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:text="取消" />
        </LinearLayout>


    </LinearLayout>
</layout>