<?xml version="1.0" encoding="utf-8"?><!--
 放松训练-视频播放的控制按钮,
 复制了exoPlayer默认的控制布局(@layout/exo_styled_player_control_view)
 在其基础上调整
-->
<merge
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <View
        android:id="@id/exo_controls_background"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/exo_black_opacity_60" />

    <FrameLayout
        android:id="@id/exo_bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/exo_styled_bottom_bar_height"
        android:layout_gravity="bottom"
        android:layout_marginTop="@dimen/exo_styled_bottom_bar_margin_top"
        android:background="@color/exo_bottom_bar_background"
        android:layoutDirection="ltr">

        <LinearLayout
            android:id="@id/exo_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|start"
            android:layoutDirection="ltr"
            android:paddingStart="@dimen/exo_styled_bottom_bar_time_padding"
            android:paddingLeft="@dimen/exo_styled_bottom_bar_time_padding"
            android:paddingEnd="@dimen/exo_styled_bottom_bar_time_padding"
            android:paddingRight="@dimen/exo_styled_bottom_bar_time_padding">

            <TextView
                android:id="@id/exo_position"
                style="@style/ExoStyledControls.TimeText.Position" />

            <TextView style="@style/ExoStyledControls.TimeText.Separator" />

            <TextView
                android:id="@id/exo_duration"
                style="@style/ExoStyledControls.TimeText.Duration" />

        </LinearLayout>

        <LinearLayout
            android:id="@id/exo_center_controls"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@android:color/transparent"
            android:clipToPadding="false"
            android:gravity="center"
            android:layoutDirection="ltr"
            android:padding="@dimen/exo_styled_controls_padding">

            <!--<ImageButton-->
            <!--    android:id="@id/exo_prev"-->
            <!--    style="@style/ExoStyledControls.Button.Center.Previous"-->
            <!--    android:alpha="0" />-->

            <include layout="@layout/exo_styled_player_control_rewind_button" />

            <ImageButton
                android:id="@id/exo_play_pause"
                style="@style/ExoStyledControls.Button.Center.PlayPause" />

            <include layout="@layout/exo_styled_player_control_ffwd_button" />

            <!--<ImageButton-->
            <!--    android:id="@id/exo_next"-->
            <!--    style="@style/ExoStyledControls.Button.Center.Next"-->
            <!--    android:alpha="0" />-->

        </LinearLayout>

        <LinearLayout
            android:id="@id/exo_basic_controls"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:layoutDirection="ltr">

            <ImageButton
                android:id="@id/exo_vr"
                style="@style/ExoStyledControls.Button.Bottom.VR" />

            <ImageButton
                android:id="@id/exo_shuffle"
                style="@style/ExoStyledControls.Button.Bottom.Shuffle" />

            <ImageButton
                android:id="@id/exo_repeat_toggle"
                style="@style/ExoStyledControls.Button.Bottom.RepeatToggle" />

            <ImageButton
                android:id="@id/exo_subtitle"
                style="@style/ExoStyledControls.Button.Bottom.CC" />

            <ImageButton
                android:id="@id/exo_settings"
                style="@style/ExoStyledControls.Button.Bottom.Settings" />

            <ImageButton
                android:id="@id/exo_fullscreen"
                style="@style/ExoStyledControls.Button.Bottom.FullScreen" />

            <ImageButton
                android:id="@id/exo_overflow_show"
                style="@style/ExoStyledControls.Button.Bottom.OverflowShow" />

        </LinearLayout>

        <HorizontalScrollView
            android:id="@id/exo_extra_controls_scroll_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:visibility="invisible">

            <LinearLayout
                android:id="@id/exo_extra_controls"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layoutDirection="ltr">

                <ImageButton
                    android:id="@id/exo_overflow_hide"
                    style="@style/ExoStyledControls.Button.Bottom.OverflowHide" />

            </LinearLayout>

        </HorizontalScrollView>

    </FrameLayout>

    <View
        android:id="@id/exo_progress_placeholder"
        android:layout_width="match_parent"
        android:layout_height="@dimen/exo_styled_progress_layout_height"
        android:layout_gravity="bottom"
        android:layout_marginBottom="@dimen/exo_styled_progress_margin_bottom" />

    <LinearLayout
        android:id="@id/exo_minimal_controls"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginBottom="@dimen/exo_styled_minimal_controls_margin_bottom"
        android:gravity="center_vertical"
        android:layoutDirection="ltr"
        android:orientation="horizontal">

        <ImageButton
            android:id="@id/exo_minimal_fullscreen"
            style="@style/ExoStyledControls.Button.Bottom.FullScreen" />

    </LinearLayout>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/exo_bottom_bar_background"
        android:orientation="horizontal">

        <ImageButton
            android:id="@+id/btnBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@null"
            android:padding="10dp"
            android:src="@mipmap/ic_back" />

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            tools:text="视频标题" />

    </RelativeLayout>


</merge>
