<?xml version="1.0" encoding="utf-8"?>
<com.hjq.shape.layout.ShapeLinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="370dp"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="20dp"
    app:shape_radius="10dp"
    app:shape_solidGradientEndColor="#7cd0e5"
    app:shape_solidGradientOrientation="topToBottom"
    app:shape_solidGradientStartColor="#498acc"
    app:shape_strokeColor="#BFD6EA"
    app:shape_strokeSize="4dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:lines="1"
        android:paddingBottom="5dp"
        android:textColor="@color/white"
        android:textSize="18sp"
        tools:text="提示" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:paddingHorizontal="15dp"
        android:text=""
        android:textColor="@color/white"
        android:textSize="14sp"
        tools:text="@tools:sample/lorem" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="42dp"
        android:layout_marginTop="20dp"
        android:gravity="end"
        android:orientation="horizontal">

        <com.hjq.shape.view.ShapeTextView
            android:id="@+id/tv_cancel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="15dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/xpopup_cancel"
            android:textColor="@color/white"
            android:textSize="14sp"
            app:shape_radius="8dp"
            app:shape_solidColor="@color/colorAccent"
            app:shape_solidPressedColor="@color/colorPrimary" />

        <com.hjq.shape.view.ShapeTextView
            android:id="@+id/tv_confirm"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/xpopup_ok"
            android:textColor="@color/white"
            android:textSize="14sp"
            app:shape_radius="8dp"
            app:shape_solidColor="@color/colorPrimaryDark"
            app:shape_solidPressedColor="@color/colorPrimary" />
    </LinearLayout>
</com.hjq.shape.layout.ShapeLinearLayout>