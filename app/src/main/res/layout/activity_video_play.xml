<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:background="@color/black"
        android:layout_height="match_parent"
        tools:context=".ui.video.VideoPlayActivity">
        <!-- StyledPlayerView 自带控制 UI -->
        <com.google.android.exoplayer2.ui.StyledPlayerView
            android:id="@+id/playerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:controller_layout_id="@layout/view_video_player_control"
            app:show_buffering="when_playing"
            app:use_controller="true" />

    </FrameLayout>
</layout>