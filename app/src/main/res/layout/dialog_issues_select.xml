<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@mipmap/bg_dialog_evaluate_tips"
            android:orientation="vertical"
            android:paddingHorizontal="30dp"
            android:paddingTop="30dp"
            android:paddingBottom="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="即将进入训练"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="请确认您当前症状状态："
                android:textColor="@color/white"
                android:textSize="14sp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                app:spanCount="4"
                tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                tools:listitem="@layout/item_issues" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_gravity="center"
                android:layout_marginBottom="20dp"
                android:layout_weight="1"
                android:gravity="center|bottom"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnGoTrain"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@mipmap/btn_start_evaluate"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:text="开始训练"
                    android:textColor="@color/white" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnCancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:background="@mipmap/btn_start_evaluate"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:text="取消"
                    android:textColor="@color/white" />
            </LinearLayout>
        </LinearLayout>
    </FrameLayout>
</layout>