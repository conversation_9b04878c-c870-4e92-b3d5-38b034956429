<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="m"
            type="com.ygxj.scale.entity.ScaleEntity" />
    </data>

    <LinearLayout
        android:id="@+id/llRoot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@mipmap/bg_scale_item"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="10dp">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/ic_scale_item" />

        <com.hjq.shape.view.ShapeTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:maxLines="2"
            android:text="@{m.scaleName}"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:shape_textEndColor="#b5e2fc"
            app:shape_textGradientOrientation="vertical"
            app:shape_textStartColor="@color/white"
            tools:text="@tools:sample/lorem/random" />

    </LinearLayout>
</layout>