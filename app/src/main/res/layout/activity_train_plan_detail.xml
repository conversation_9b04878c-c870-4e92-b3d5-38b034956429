<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_common"
        android:orientation="vertical"
        tools:context=".ui.trainPlan.TrainPlanDetailActivity">

        <com.ygxj.prshs.ui.view.TitleBarView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:title="训练方案" />

        <com.hjq.shape.layout.ShapeFrameLayout
            android:id="@+id/playerContainer"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="20dp"
            android:layout_marginBottom="20dp"
            android:layout_weight="1"
            android:clipChildren="true"
            app:shape_solidColor="@color/black"
            app:shape_strokeColor="@color/white"
            app:shape_strokeSize="2dp">

            <!-- StyledPlayerView 自带控制 UI -->
            <com.google.android.exoplayer2.ui.StyledPlayerView
                android:id="@+id/playerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="2dp"
                android:background="@color/black"
                android:keepScreenOn="true"
                app:controller_layout_id="@layout/view_video_player_control_toggle_fullscreen"
                app:show_buffering="when_playing" />
        </com.hjq.shape.layout.ShapeFrameLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginBottom="20dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnBGM"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:background="@mipmap/btn_start_evaluate"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="背景白噪音"
                android:textColor="@color/white" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnVolume"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginStart="20dp"
                android:background="@mipmap/btn_start_evaluate"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="音量调节"
                android:textColor="@color/white" />

        </LinearLayout>

    </LinearLayout>
</layout>