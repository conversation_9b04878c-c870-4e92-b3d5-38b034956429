<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <merge
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@mipmap/bg_home_menu_out"
        android:gravity="center"
        android:orientation="vertical"
        tools:parentTag="android.widget.FrameLayout">

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="20dp"
            android:background="@mipmap/bg_home_menu_inner">

            <ImageView
                android:id="@+id/icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                tools:src="@mipmap/home_menu_hu_xi_fang_song" />
        </FrameLayout>

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|bottom"
            android:layout_marginBottom="30dp"
            android:shadowColor="#0d5795"
            android:shadowDx="1"
            android:shadowDy="2"
            android:shadowRadius="1"
            android:textColor="@color/white"
            android:textSize="16sp"
            tools:text="呼吸放松法" />
    </merge>
</layout>