<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <com.hjq.shape.layout.ShapeLinearLayout
        android:id="@+id/llRoot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="5dp"
        app:shape_radius="20dp"
        app:shape_strokeGradientEndColor="@android:color/transparent"
        app:shape_strokeGradientStartColor="#59AFD5"
        app:shape_strokeSize="1dp"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:id="@+id/ivSelected"
            android:layout_width="20dp"
            android:layout_height="20dp"
            tools:src="@mipmap/ic_scale_option_unchecked" />

        <TextView
            android:id="@+id/tvOption"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="1. 你是否出现以下情况" />


    </com.hjq.shape.layout.ShapeLinearLayout>
</layout>