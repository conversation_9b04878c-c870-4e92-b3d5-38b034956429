<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="m"
            type="com.ygxj.prshs.data.entity.BgmEntity" />
    </data>

    <com.hjq.shape.view.ShapeTextView
        android:id="@+id/tvBgmName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingVertical="10dp"
        android:text="@{m.name}"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:shape_radius="4dp"
        app:shape_solidColor="#CC275F8D"
        app:shape_strokeColor="#5ec0fe"
        app:shape_strokeSize="1dp"
        app:textColor="@{m.statusTextColor}"
        tools:text="小雨" />
</layout>