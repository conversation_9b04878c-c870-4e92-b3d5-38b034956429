<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <com.hjq.shape.view.ShapeTextView
        android:id="@+id/tvIndicator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingVertical="5dp"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:shape_radius="6dp"
        app:shape_strokeColor="#5ec0fe"
        app:shape_strokeSize="1dp"
        tools:text="阶段1" />


</layout>