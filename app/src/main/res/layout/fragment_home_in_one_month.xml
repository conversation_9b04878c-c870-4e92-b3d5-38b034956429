<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        tools:background="@mipmap/bg_main"
        tools:context=".ui.main.HomeFragmentInOneMonth">

        <com.ygxj.prshs.ui.view.LogoView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="50dp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.ygxj.prshs.ui.view.HomeMenuItemView
                android:id="@+id/btnEmergencyAdjust"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:menu_icon="@mipmap/home_menu_emergency_adjust"
                app:menu_title="紧急调节" />

            <com.ygxj.prshs.ui.view.HomeMenuItemView
                android:id="@+id/btnTestAdjust"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                app:menu_icon="@mipmap/home_menu_test_adjust"
                app:menu_title="测试后调节" />

            <com.ygxj.prshs.ui.view.HomeMenuItemView
                android:id="@+id/btnTrainingPlan"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                app:menu_icon="@mipmap/home_menu_train_plan"
                app:menu_title="训练方案" />

            <com.ygxj.prshs.ui.view.HomeMenuItemView
                android:id="@+id/btnProfileManage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                app:menu_icon="@mipmap/home_menu_profile_manage"
                app:menu_title="档案管理" />

        </LinearLayout>

    </LinearLayout>
</layout>