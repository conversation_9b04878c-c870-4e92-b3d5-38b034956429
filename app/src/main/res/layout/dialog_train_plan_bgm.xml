<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <com.hjq.shape.layout.ShapeLinearLayout
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:shape_radius="10dp"
        app:shape_solidGradientEndColor="#8042affb"
        app:shape_solidGradientStartColor="#803470A4"
        app:shape_strokeColor="#5ec0fe"
        app:shape_strokeSize="2dp">

        <com.hjq.shape.view.ShapeTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingVertical="10dp"
            android:text="背景白噪音"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:shape_radiusInTopLeft="10dp"
            app:shape_radiusInTopRight="10dp"
            app:shape_solidGradientEndColor="#CC42AFFB"
            app:shape_solidGradientStartColor="#CC3470A4" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="40dp"
            android:layout_marginVertical="30dp"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="3"
            tools:itemCount="5"
            tools:listitem="@layout/item_train_plan_bgm" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="20dp"
            android:background="@mipmap/btn_start_evaluate"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:text="返回"
            android:textColor="@color/white" />

    </com.hjq.shape.layout.ShapeLinearLayout>
</layout>