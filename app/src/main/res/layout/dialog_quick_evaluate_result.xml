<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="603dp"
        android:layout_height="364dp"
        android:background="@mipmap/bg_dialog_evaluate_tips"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingVertical="20dp"
            android:text="结果解释"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvResult"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="30dp"
            android:layout_marginBottom="30dp"
            android:layout_weight="1"
            android:lineSpacingExtra="4dp"
            android:textColor="@color/white"
            android:textSize="16sp"
            tools:text="@tools:sample/lorem/random" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnGoTrain"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@mipmap/btn_start_evaluate"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="前往训练"
                android:textColor="@color/white" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnLater"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:background="@mipmap/btn_start_evaluate"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="稍后训练"
                android:textColor="@color/white" />

        </LinearLayout>

    </LinearLayout>
</layout>