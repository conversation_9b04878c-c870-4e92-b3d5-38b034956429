<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvMsg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:text="没有找到数据"
        android:textColor="#999999"
        android:textSize="12sp"
        tools:ignore="HardcodedText" />

    <com.hjq.shape.view.ShapeButton
        android:id="@+id/btnRetry"
        android:layout_width="120dp"
        android:layout_height="36dp"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:text="重新加载"
        android:textSize="12sp"
        app:shape_radius="20dp"
        app:shape_solidColor="@color/colorPrimary"
        app:shape_textColor="@color/white" />

</LinearLayout>