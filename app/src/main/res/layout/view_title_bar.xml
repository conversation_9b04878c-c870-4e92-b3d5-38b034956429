<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <merge
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="10dp"
        tools:parentTag="android.widget.FrameLayout">

        <ImageView
            android:id="@+id/btnBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:padding="10dp"
            android:src="@mipmap/ic_back" />

        <com.hjq.shape.view.ShapeTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:paddingVertical="10dp"
            android:textColor="@color/white"
            android:textSize="22sp"
            android:textStyle="bold"
            app:shape_textEndColor="#b5e2fc"
            app:shape_textGradientOrientation="vertical"
            app:shape_textStartColor="@color/white"
            tools:text="急救档案" />

    </merge>
</layout>