<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- android 30之前 静态声明的蓝牙权限 -->
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        tools:remove="android:maxSdkVersion" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" /> <!-- 使用蓝牙还需要 静态声明+动态申请位置（前台位置）权限 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- android 30之后 静态声明+动态申请的蓝牙权限 -->
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation"
        tools:targetApi="s" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <application
        android:name=".base.App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:theme="@style/Theme.PsyRescueSelfHelpSystem">
        <activity
            android:name=".ui.trainPlan.TrainPlanActivity"
            android:exported="false" />
        <activity
            android:name=".ui.scale.ScaleTestActivity"
            android:exported="false" />
        <activity
            android:name=".ui.testAdjust.quickEvaluate.QuickEvaluateDetailActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.testAdjust.quickEvaluate.QuickEvaluateActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.testAdjust.TestAdjustActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.emergencyAdjust.EmergencyAdjustDetailActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.emergencyAdjust.EmergencyAdjustActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.video.VideoPlayActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.emergencyProfile.EmergencyProfileActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.register.RegisterActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.login.LoginActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".ui.splash.SplashActivity"
            android:exported="true"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.main.MainActivity"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <meta-data
            android:name="design_width_in_dp"
            android:value="960" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="540" />
    </application>

</manifest>