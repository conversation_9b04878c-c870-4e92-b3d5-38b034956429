pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()

        maven { setUrl("https://jitpack.io") }

        // 云加密私有仓库
        maven {
            credentials {
                username = "6607d0bc7f227660ad200398"
                password = "SuuEmJ4Hp4L8"
            }
            setUrl("https://packages.aliyun.com/6607d0fb702faeb063ba32e0/maven/repo-activate")
        }

        // 阿里云云效仓库：https://maven.aliyun.com/mvn/guide
        maven { setUrl("https://maven.aliyun.com/repository/public") }
        maven { setUrl("https://maven.aliyun.com/repository/google") }
        // 华为开源镜像：https://mirrors.huaweicloud.com
        maven { setUrl("https://repo.huaweicloud.com/repository/maven") }
    }
}

rootProject.name = "PsyRescueSelfHelpSystem"
include(":app")
include(":scale")
